(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_lib_authApi_ts_3c2a9f63._.js", {

"[project]/src/lib/authApi.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/lib/authApi.ts [app-client] (ecmascript)");
    });
});
}}),
}]);