"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7907],{44838:(e,t,a)=>{a.d(t,{SQ:()=>f,_2:()=>p,lp:()=>m,mB:()=>x,rI:()=>c,ty:()=>u});var s=a(95155),o=a(12115),r=a(87560),n=a(13052),l=a(5196),d=a(9428),i=a(59434);let c=r.bL,u=r.l9;r.YJ,r.Z<PERSON>,r.Pb,r.z6,o.forwardRef((e,t)=>{let{className:a,inset:o,children:l,...d}=e;return(0,s.jsxs)(r.ZP,{ref:t,className:(0,i.cn)("focus:bg-accent data-[state=open]:bg-accent flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-none select-none",o&&"pl-8",a),...d,children:[l,(0,s.jsx)(n.A,{className:"ml-auto h-4 w-4"})]})}).displayName=r.ZP.displayName,o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.G5,{ref:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-lg",a),...o})}).displayName=r.G5.displayName;let f=o.forwardRef((e,t)=>{let{className:a,sideOffset:o=4,...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{ref:t,sideOffset:o,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",a),...n})})});f.displayName=r.UC.displayName;let p=o.forwardRef((e,t)=>{let{className:a,inset:o,...n}=e;return(0,s.jsx)(r.q7,{ref:t,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",o&&"pl-8",a),...n})});p.displayName=r.q7.displayName,o.forwardRef((e,t)=>{let{className:a,children:o,checked:n,...d}=e;return(0,s.jsxs)(r.H_,{ref:t,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:n,...d,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})}),o]})}).displayName=r.H_.displayName,o.forwardRef((e,t)=>{let{className:a,children:o,...n}=e;return(0,s.jsxs)(r.hN,{ref:t,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center rounded-sm py-1.5 pr-2 pl-8 text-sm transition-colors outline-none select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),o]})}).displayName=r.hN.displayName;let m=o.forwardRef((e,t)=>{let{className:a,inset:o,...n}=e;return(0,s.jsx)(r.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",o&&"pl-8",a),...n})});m.displayName=r.JU.displayName;let x=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.wv,{ref:t,className:(0,i.cn)("bg-muted -mx-1 my-1 h-px",a),...o})});x.displayName=r.wv.displayName},54165:(e,t,a)=>{a.d(t,{Cf:()=>u,Es:()=>p,L3:()=>m,c7:()=>f,lG:()=>d,rr:()=>x});var s=a(95155),o=a(12115),r=a(59154),n=a(54416),l=a(59434);let d=r.bL;r.l9;let i=r.ZL;r.bm;let c=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.hJ,{ref:t,className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",a),...o})});c.displayName=r.hJ.displayName;let u=o.forwardRef((e,t)=>{let{className:a,children:o,...d}=e;return(0,s.jsxs)(i,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(r.UC,{ref:t,className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] fixed top-[50%] left-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border p-6 shadow-lg duration-200 sm:rounded-lg",a),...d,children:[o,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-none disabled:pointer-events-none",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;let f=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};f.displayName="DialogHeader";let p=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};p.displayName="DialogFooter";let m=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.hE,{ref:t,className:(0,l.cn)("text-lg leading-none font-semibold tracking-tight",a),...o})});m.displayName=r.hE.displayName;let x=o.forwardRef((e,t)=>{let{className:a,...o}=e;return(0,s.jsx)(r.VY,{ref:t,className:(0,l.cn)("text-muted-foreground text-sm",a),...o})});x.displayName=r.VY.displayName},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>p,gC:()=>f,l6:()=>i,yv:()=>c});var s=a(95155);a(12115);var o=a(33897),r=a(66474),n=a(5196),l=a(47863),d=a(59434);function i(e){let{...t}=e;return(0,s.jsx)(o.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,s.jsx)(o.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:n,...l}=e;return(0,s.jsxs)(o.l9,{"data-slot":"select-trigger","data-size":a,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,s.jsx)(o.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function f(e){let{className:t,children:a,position:r="popper",...n}=e;return(0,s.jsx)(o.ZL,{children:(0,s.jsxs)(o.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...n,children:[(0,s.jsx)(m,{}),(0,s.jsx)(o.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(x,{})]})})}function p(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(o.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(o.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(o.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(o.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(o.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(r.A,{className:"size-4"})})}},66695:(e,t,a)=>{a.d(t,{BT:()=>d,Wu:()=>i,ZB:()=>l,Zp:()=>r,aR:()=>n,wL:()=>c});var s=a(95155);a(12115);var o=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-3 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},88539:(e,t,a)=>{a.d(t,{T:()=>r});var s=a(95155);a(12115);var o=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},90010:(e,t,a)=>{a.d(t,{$v:()=>m,EO:()=>c,Lt:()=>l,Rx:()=>x,Zr:()=>g,ck:()=>f,r7:()=>p,wd:()=>u});var s=a(95155);a(12115);var o=a(32588),r=a(59434),n=a(30285);function l(e){let{...t}=e;return(0,s.jsx)(o.bL,{"data-slot":"alert-dialog",...t})}function d(e){let{...t}=e;return(0,s.jsx)(o.ZL,{"data-slot":"alert-dialog-portal",...t})}function i(e){let{className:t,...a}=e;return(0,s.jsx)(o.hJ,{"data-slot":"alert-dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(i,{}),(0,s.jsx)(o.UC,{"data-slot":"alert-dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(o.hE,{"data-slot":"alert-dialog-title",className:(0,r.cn)("text-lg font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(o.VY,{"data-slot":"alert-dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(o.rc,{className:(0,r.cn)((0,n.r)(),t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(o.ZD,{className:(0,r.cn)((0,n.r)({variant:"outline"}),t),...a})}},93885:(e,t,a)=>{a.d(t,{L6:()=>i,gw:()=>d,pr:()=>l,s3:()=>c,vr:()=>n});var s=a(4982),o=a(54897),r=a(89761);async function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0;try{var n,l;if(console.log("[DEBUG] Fetching workflows with page=".concat(e,", pageSize=").concat(t)),console.log("[DEBUG] Using endpoint: ".concat(r.Sn.WORKFLOWS.LIST)),a)console.log("[DEBUG] Using provided access token (length: ".concat(a.length,")"));else try{a=(0,o.XI)(),console.log("[DEBUG] Retrieved client token (length: ".concat(a?a.length:0,")"))}catch(e){console.error("[DEBUG] Error retrieving token:",e)}let d={headers:{"Content-Type":"application/json"},withCredentials:!1,params:{page:e,page_size:t}};a?(a.startsWith("Bearer ")?d.headers.Authorization=a:d.headers.Authorization="Bearer ".concat(a),console.log("[DEBUG] Added Authorization header")):console.warn("[DEBUG] No access token available for request"),console.log("[DEBUG] Making request to: ".concat(r.Sn.WORKFLOWS.LIST));let i=await s.yq.get(r.Sn.WORKFLOWS.LIST,d);return console.log("[DEBUG] Successful response with ".concat((null===(l=i.data)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.length)||0," workflows")),i.data}catch(e){if(console.error("Error fetching workflows:",e),e.response)throw console.error("[DEBUG] Response status: ".concat(e.response.status)),console.error("[DEBUG] Response data:",e.response.data),Error("Failed to fetch workflows: ".concat(e.response.status," ").concat(e.response.statusText));if(e.request)throw console.error("[DEBUG] No response received:",e.request),Error("Failed to fetch workflows: No response received from server");throw console.error("[DEBUG] Request setup error:",e.message),Error("Failed to fetch workflows: ".concat(e.message))}}async function l(){try{let e,t={name:"Untitled Workflow",description:"New workflow",workflow_data:{nodes:[{id:"start-node",type:"WorkflowNode",position:{x:100,y:100},data:{label:"Start",type:"component",originalType:"StartNode",definition:{name:"StartNode",display_name:"Start",description:"The starting point for all workflows. Only nodes connected to this node will be executed.",category:"Input/Output",icon:"Play",beta:!1,inputs:[],outputs:[{name:"flow",display_name:"Flow",output_type:"Any"}],is_valid:!0,path:"components.io.start_node"},config:{collected_parameters:{}}}}],edges:[]},start_node_data:[]};e=(0,o.XI)();let a={headers:{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}},withCredentials:!1};return(await s.yq.post(r.Sn.WORKFLOWS.CREATE,t,a)).data}catch(e){if(console.error("Error creating workflow:",e),e.response)throw Error("Failed to create workflow: ".concat(e.response.status," ").concat(e.response.statusText));throw e}}async function d(e){try{let t;t=(0,o.XI)();let a={headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}},withCredentials:!1},n=await s.yq.get(r.Sn.WORKFLOWS.GET(e),a);return console.log("Workflow API response:",n.data),n.data}catch(e){if(console.error("Error fetching workflow:",e),e.response)throw Error("Failed to fetch workflow: ".concat(e.response.status," ").concat(e.response.statusText));throw e}}async function i(e){try{var t;let a;a=(0,o.XI)();let n={headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)}},withCredentials:!1},l=await s.yq.delete(r.Sn.WORKFLOWS.DELETE(e),n);return console.log("Workflow delete response:",l.data),{success:!0,message:(null===(t=l.data)||void 0===t?void 0:t.message)||"Workflow deleted successfully"}}catch(e){if(console.error("Error deleting workflow:",e),e.response)throw Error("Failed to delete workflow: ".concat(e.response.status," ").concat(e.response.statusText));throw e}}async function c(e,t){try{let a;a=(0,o.XI)();let n={headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)}},withCredentials:!1};return(await s.yq.patch(r.Sn.WORKFLOWS.UPDATE(e),t,n)).data}catch(e){if(console.error("Error updating workflow metadata:",e),e.response)throw Error("Failed to update workflow: ".concat(e.response.status," ").concat(e.response.statusText));throw e}}}}]);