(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3778],{5469:(e,s,a)=>{Promise.resolve().then(a.bind(a,36310))},17759:(e,s,a)=>{"use strict";a.d(s,{C5:()=>f,MJ:()=>p,eI:()=>x,lR:()=>g,lV:()=>c,zB:()=>i});var t=a(95155),r=a(12115),n=a(99708),o=a(62177),l=a(59434),d=a(85057);let c=o.Op,m=r.createContext({}),i=e=>{let{...s}=e;return(0,t.jsx)(m.Provider,{value:{name:s.name},children:(0,t.jsx)(o.xI,{...s})})},u=()=>{let e=r.useContext(m),s=r.useContext(h),{getFieldState:a}=(0,o.xW)(),t=(0,o.lN)({name:e.name}),n=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},h=r.createContext({});function x(e){let{className:s,...a}=e,n=r.useId();return(0,t.jsx)(h.Provider,{value:{id:n},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",s),...a})})}function g(e){let{className:s,...a}=e,{error:r,formItemId:n}=u();return(0,t.jsx)(d.J,{"data-slot":"form-label","data-error":!!r,className:(0,l.cn)("data-[error=true]:text-destructive",s),htmlFor:n,...a})}function p(e){let{...s}=e,{error:a,formItemId:r,formDescriptionId:o,formMessageId:l}=u();return(0,t.jsx)(n.DX,{"data-slot":"form-control",id:r,"aria-describedby":a?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!a,...s})}function f(e){var s;let{className:a,...r}=e,{error:n,formMessageId:o}=u(),d=n?String(null!==(s=null==n?void 0:n.message)&&void 0!==s?s:""):r.children;return d?(0,t.jsx)("p",{"data-slot":"form-message",id:o,className:(0,l.cn)("text-destructive text-sm",a),...r,children:d}):null}},35695:(e,s,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},36310:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>y});var t=a(95155),r=a(12115),n=a(62177),o=a(90221),l=a(48282),d=a(30285),c=a(62523),m=a(17759),i=a(40646),u=a(78749),h=a(92657),x=a(54861),g=a(51154),p=a(35695),f=a(27997);function w(e){let{token:s}=e,[a,w]=(0,r.useState)(!1),[j,b]=(0,r.useState)(!1),[N,y]=(0,r.useState)(!1),[v,P]=(0,r.useState)({length:!1,hasNumber:!1,hasSymbol:!1}),[z,A]=(0,r.useState)(!1),k=(0,p.useRouter)(),S=(0,n.mN)({resolver:(0,o.u)(l.Oh),defaultValues:{password:"",confirmPassword:""}});(0,r.useEffect)(()=>{let e=S.watch((e,s)=>{let{name:a}=s;("password"===a||void 0===a)&&P((0,l.Oj)(e.password||""))});return()=>e.unsubscribe()},[S]);let C=async e=>{y(!0);try{await f.authApi.updatePassword({token:s,password:e.password}),A(!0)}catch(e){console.error("Update password error:",e),S.setError("root",{type:"manual",message:e.message||"Failed to update password. Please try again."})}finally{y(!1)}};return z?(0,t.jsxs)("div",{className:"space-y-6 text-center",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)("div",{className:"rounded-full bg-green-100 p-3",children:(0,t.jsx)(i.A,{className:"h-10 w-10 text-green-600"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Password Updated"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Your password has been updated successfully. You can now log in with your new password."})]}),(0,t.jsx)(d.$,{onClick:()=>k.push("/login"),className:"w-full",children:"Go to Login"})]}):(0,t.jsx)(m.lV,{...S,children:(0,t.jsxs)("form",{onSubmit:S.handleSubmit(C),className:"space-y-6",children:[S.formState.errors.root&&(0,t.jsx)("div",{className:"rounded-md bg-red-50 p-3 text-sm text-red-500",children:S.formState.errors.root.message}),(0,t.jsx)(m.zB,{control:S.control,name:"password",render:e=>{let{field:s}=e;return(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"New Password"}),(0,t.jsx)(m.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.p,{placeholder:"Create a new password",type:a?"text":"password",disabled:N,...s}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!a),disabled:N,children:a?(0,t.jsx)(u.A,{className:"text-muted-foreground h-4 w-4"}):(0,t.jsx)(h.A,{className:"text-muted-foreground h-4 w-4"})})]})}),(0,t.jsx)(m.C5,{}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[v.length?(0,t.jsx)(i.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,t.jsx)("span",{className:v.length?"text-green-500":"text-muted-foreground",children:"Between 6-15 characters"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[v.hasNumber?(0,t.jsx)(i.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,t.jsx)("span",{className:v.hasNumber?"text-green-500":"text-muted-foreground",children:"At least one number"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[v.hasSymbol?(0,t.jsx)(i.A,{className:"h-4 w-4 text-green-500"}):(0,t.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,t.jsx)("span",{className:v.hasSymbol?"text-green-500":"text-muted-foreground",children:"At least one symbol"})]})]})]})}}),(0,t.jsx)(m.zB,{control:S.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Confirm Password"}),(0,t.jsx)(m.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.p,{placeholder:"Confirm your new password",type:j?"text":"password",disabled:N,...s}),(0,t.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>b(!j),disabled:N,children:j?(0,t.jsx)(u.A,{className:"text-muted-foreground h-4 w-4"}):(0,t.jsx)(h.A,{className:"text-muted-foreground h-4 w-4"})})]})}),(0,t.jsx)(m.C5,{})]})}}),(0,t.jsxs)(d.$,{type:"submit",className:"w-full",disabled:N,children:[N&&(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Password"]})]})})}var j=a(6874),b=a.n(j),N=a(40081);function y(){let e=(0,p.useParams)().token;return(0,t.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,t.jsx)(N.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Update Password"}),(0,t.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Create a new password for your account"})]}),(0,t.jsx)(w,{token:e}),(0,t.jsx)("div",{className:"text-center text-sm",children:(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Remember your password?"," ",(0,t.jsx)(b(),{href:"/login",className:"text-primary font-medium hover:underline",children:"Log in"})]})})]})})}},40646:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48282:(e,s,a)=>{"use strict";a.d(s,{O5:()=>r,Oh:()=>o,Oj:()=>l,jc:()=>n});var t=a(55594);t.z.object({email:t.z.string().email({message:"Please enter a valid email address."}),password:t.z.string().min(1,{message:"Password is required."})}),t.z.object({email:t.z.string().email({message:"Please enter a valid email address."})});let r=t.z.object({fullName:t.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:t.z.string().email({message:"Please enter a valid email address."}),password:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:t.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),n=t.z.object({email:t.z.string().email({message:"Please enter a valid email address."})});t.z.object({newPassword:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:t.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]});let o=t.z.object({password:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:t.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});function l(e){return{length:e.length>=6&&e.length<=15,hasNumber:/[0-9]/.test(e),hasSymbol:/[!@#$%^&*(),.?":{}|<>]/.test(e)}}},51154:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},78749:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},92657:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[9352,3291,6874,8517,2313,7764,7997,8441,1684,7358],()=>s(5469)),_N_E=e.O()}]);