"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9527],{3201:(e,o,t)=>{t.d(o,{LN:()=>a});var n=t(65453),c=t(46786);let a=(0,n.v)()((0,c.Zr)((e,o)=>({nodes:{},setValue:(o,t,n)=>e(e=>({nodes:{...e.nodes,[o]:{...e.nodes[o]||{},[t]:n}}})),getValue:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,c=o();return c.nodes[e]&&void 0!==c.nodes[e][t]?c.nodes[e][t]:n},clearNodeState:o=>e(e=>{let t={...e.nodes};return delete t[o],{nodes:t}}),clearNodeKey:(o,t)=>e(e=>{if(!e.nodes[o])return e;let n={...e.nodes[o]};return delete n[t],{nodes:{...e.nodes,[o]:n}}}),clearAllState:()=>e({nodes:{}})}),{name:"component-state-store"}))},27735:(e,o,t)=>{function n(e,o,t){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return!!o||!n&&e&&!t}function c(e,o,t,c,a,i,l,r,s){let d=arguments.length>9&&void 0!==arguments[9]&&arguments[9],u=new Date().toISOString().replace("T"," ").substring(0,19),g=n(a,l,r,d);console.log("[".concat(u,"] [").concat(e,"] Field ").concat(t,".").concat(c," status check:\n    - required property value: ").concat(void 0===i?"undefined":i,"\n    - required !== false: ").concat(a?"YES":"NO","\n    - directly connected to Start: ").concat(l?"YES":"NO","\n    - has configured value: ").concat(r?"YES":"NO","\n    - has incoming connection: ").concat(d?"YES":"NO","\n    - configured value: ").concat(r?JSON.stringify(s):"undefined","\n    - Should include: ").concat(g?"YES":"NO"))}t.d(o,{J:()=>c,K:()=>n})},51456:(e,o,t)=>{t.d(o,{h:()=>n});var n=function(e){return e.WORKFLOW_INVALID_JSON="WF001",e.WORKFLOW_MISSING_NODES="WF002",e.WORKFLOW_MISSING_EDGES="WF003",e.WORKFLOW_MISSING_START_NODE="WF004",e.WORKFLOW_DISCONNECTED_NODES="WF005",e.WORKFLOW_CYCLE_DETECTED="WF006",e.WORKFLOW_INVALID_NAME="WF007",e.WORKFLOW_EMPTY="WF008",e.WORKFLOW_USING_FALLBACK_START_NODE="WF009",e.NODE_MISSING_ID="ND001",e.NODE_MISSING_TYPE="ND002",e.NODE_MISSING_POSITION="ND003",e.NODE_MISSING_DATA="ND004",e.NODE_MISSING_DATA_TYPE="ND005",e.NODE_MISSING_DATA_LABEL="ND006",e.NODE_MISSING_DATA_DEFINITION="ND007",e.NODE_DUPLICATE_ID="ND008",e.NODE_INVALID_POSITION="ND009",e.EDGE_MISSING_ID="ED001",e.EDGE_MISSING_SOURCE="ED002",e.EDGE_MISSING_TARGET="ED003",e.EDGE_SOURCE_NOT_FOUND="ED004",e.EDGE_TARGET_NOT_FOUND="ED005",e.EDGE_DUPLICATE_ID="ED006",e.EDGE_SELF_REFERENCE="ED007",e.FIELD_REQUIRED="FD001",e.FIELD_STRING_LENGTH="FD002",e.FIELD_NUMBER_RANGE="FD003",e.FIELD_PATTERN_MISMATCH="FD004",e.FIELD_MISSING_REQUIRED_KEYS="FD005",e.FIELD_CONNECTED_INPUT="FD006",e}({})},56092:(e,o,t)=>{function n(e){var o,t,n;if(!e||!e.data)return!1;let c=new Date().toISOString().replace("T"," ").substring(0,19);return(console.log("[".concat(c,"] [isStartNode] Checking if node is a StartNode:"),{id:e.id,type:e.type,dataType:e.data.type,originalType:e.data.originalType,definitionName:null===(o=e.data.definition)||void 0===o?void 0:o.name,label:e.data.label}),"StartNode"===e.data.originalType)?(console.log("[".concat(c,"] [isStartNode] Node ").concat(e.id," identified as StartNode by originalType")),!0):(null===(t=e.data.definition)||void 0===t?void 0:t.name)==="StartNode"?(console.log("[".concat(c,"] [isStartNode] Node ").concat(e.id," identified as StartNode by definition name")),!0):"StartNode"===e.type||"StartNode"===e.data.type?(console.log("[".concat(c,"] [isStartNode] Node ").concat(e.id," identified as StartNode by node type")),!0):"Start"===e.data.label||"StartNode"===e.data.label?(console.log("[".concat(c,"] [isStartNode] Node ").concat(e.id," identified as StartNode by label")),!0):!!("component"===e.data.type&&(null===(n=e.data.definition)||void 0===n?void 0:n.name)&&e.data.definition.name.toLowerCase().includes("start"))&&(console.log("[".concat(c,"] [isStartNode] Node ").concat(e.id," identified as StartNode by component name pattern")),!0)}function c(e){if(!e||!Array.isArray(e)){console.warn("[findStartNode] Nodes array is null, undefined, or not an array");return}let o=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(o,"] [findStartNode] Searching for StartNode in ").concat(e.length," nodes")),console.log("[".concat(o,"] [findStartNode] Node types in workflow:"),e.map(e=>{var o,t,n,c,a;return{id:e.id,type:e.type,dataType:null===(o=e.data)||void 0===o?void 0:o.type,originalType:null===(t=e.data)||void 0===t?void 0:t.originalType,definitionName:null===(c=e.data)||void 0===c?void 0:null===(n=c.definition)||void 0===n?void 0:n.name,label:null===(a=e.data)||void 0===a?void 0:a.label}}));let t=e.find(n);return t?console.log("[".concat(o,"] [findStartNode] Found StartNode with ID: ").concat(t.id)):console.warn("[".concat(o,"] [findStartNode] No StartNode found in workflow with ").concat(e.length," nodes")),t}function a(e,o){return o.has(e)}function i(e,o,t){let n=new Set;return o.forEach(e=>{e.source===t&&n.add(e.target)}),n}function l(e,o,t){let n=new Map;return o.forEach(e=>{if(e.source===t&&e.target&&e.targetHandle){var o;n.has(e.target)||n.set(e.target,new Set);let t=e.targetHandle;null===(o=n.get(e.target))||void 0===o||o.add(t)}}),n}function r(e,o,t){let n=new Set([t]),c=[t],a=new Map;for(e.forEach(e=>{a.set(e.id,[])}),o.forEach(e=>{let o=a.get(e.source)||[];o.push(e.target),a.set(e.source,o)});c.length>0;){let e=c.shift();for(let o of a.get(e)||[])n.has(o)||(n.add(o),c.push(o))}return n}function s(e,o){let t=new Map;e.forEach(e=>{t.set(e.id,[])}),o.forEach(e=>{let o=t.get(e.source)||[];o.push(e.target),t.set(e.source,o)});let n=new Set,c=new Set,a=new Set;return e.forEach(e=>{n.has(e.id)||function e(o){if(c.has(o))return a.add(o),!0;if(n.has(o))return!1;for(let i of(n.add(o),c.add(o),t.get(o)||[]))if(e(i))return a.add(o),!0;return c.delete(o),!1}(e.id)}),Array.from(a)}t.r(o),t.d(o,{detectCycles:()=>s,findStartNode:()=>c,getConnectedNodes:()=>r,getDirectlyConnectedFields:()=>l,getDirectlyConnectedNodes:()=>i,isNodeConnected:()=>a,isStartNode:()=>n})},58706:(e,o,t)=>{function n(e){if(console.log("[unwrapDualPurposeValue] Input value:",e,"Type: ".concat(typeof e)),"object"==typeof e&&null!==e&&"value"in e){let o=Object.keys(e),t=o.includes("value"),n=1===o.length&&t,c=t&&o.every(e=>"value"===e||"transition_id"===e||"metadata"===e||"type"===e);if(console.log("[unwrapDualPurposeValue] Analysis:",{hasValueProperty:t,hasOnlyValueProperty:n,hasValueAndMetadata:c,keys:o,valueType:typeof e.value}),n||c){if(console.log("[unwrapDualPurposeValue] Detected dual-purpose wrapper, unwrapping:",e),console.log("[unwrapDualPurposeValue] Wrapper keys:",o),console.log("[unwrapDualPurposeValue] Extracted value:",e.value),"string"==typeof e.value&&e.value.trim().startsWith("{"))try{let o=JSON.parse(e.value);return console.log("[unwrapDualPurposeValue] Parsed JSON string to object:",o),o}catch(e){console.log("[unwrapDualPurposeValue] Failed to parse JSON string, returning as-is:",e)}return e.value}}if("string"==typeof e&&e.trim().startsWith("{"))try{let o=JSON.parse(e);return console.log("[unwrapDualPurposeValue] Parsed standalone JSON string to object:",o),o}catch(e){console.log("[unwrapDualPurposeValue] Failed to parse standalone JSON string, returning as-is:",e)}return console.log("[unwrapDualPurposeValue] No unwrapping needed, returning as-is:",e),e}function c(){return new Date().toISOString().replace("T"," ").substring(0,19)}function a(e){var o,t,n,a,i,l;if(!e||!e.data)return!1;let r="mcp"===e.data.type||"MCPMarketplaceComponent"===e.data.originalType||"MCPMarketplaceComponent"===e.data.type||e.data.definition&&"MCPMarketplaceComponent"===e.data.definition.type||e.data.definition&&e.data.definition.mcp_info||e.data.definition&&e.data.definition.path&&(e.data.definition.path.includes("mcp_marketplace")||e.data.definition.path.includes("components.mcp"));return console.log("[".concat(c(),"] [isMCPMarketplaceComponent] Node ").concat(e.id," (").concat(e.data.label||"Unnamed","):\n    - type: ").concat(e.data.type,"\n    - originalType: ").concat(e.data.originalType,"\n    - definition.type: ").concat(null===(t=e.data)||void 0===t?void 0:null===(o=t.definition)||void 0===o?void 0:o.type,"\n    - has mcp_info: ").concat(!!(null===(a=e.data)||void 0===a?void 0:null===(n=a.definition)||void 0===n?void 0:n.mcp_info),"\n    - path: ").concat(null===(l=e.data)||void 0===l?void 0:null===(i=l.definition)||void 0===i?void 0:i.path,"\n    - RESULT: ").concat(r?"IS MCP COMPONENT":"NOT MCP COMPONENT")),!!r}function i(e,o){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(console.log("[".concat(c(),"] [isFieldRequired] Checking if field is required:\n    - Node: ").concat(e.id," (").concat(e.data.label||"Unnamed",")\n    - Field: ").concat(o.name," (").concat(o.display_name||o.name,")\n    - Input type: ").concat(o.input_type,"\n    - Explicitly required: ").concat(!0===o.required?"YES":"NO","\n    - Is handle: ").concat(o.is_handle?"YES":"NO","\n    - Is handle connected: ").concat(t?"YES":"NO","\n    - Ends with _handle: ").concat(o.name.endsWith("_handle")?"YES":"NO","\n    - Has requirement rules: ").concat(o.requirement_rules&&o.requirement_rules.length>0?"YES":"NO")),o.requirement_rules&&o.requirement_rules.length>0){let n=e.data.config||{};if(function(e,o,t){if(!e||0===e.length)return!1;console.log("[".concat(c(),"] [evaluateRequirementRules] Evaluating ").concat(e.length," requirement rules with logic: ").concat(o||"OR"));let n=o||"OR",a=e.map(e=>{let o=t[e.field_name],n=e.operator||"equals",a=!1;switch(n){case"equals":default:a=o===e.field_value;break;case"not_equals":a=o!==e.field_value;break;case"contains":a="string"==typeof o&&o.includes(e.field_value);break;case"exists":a=null!=o;break;case"not_exists":a=null==o}return console.log("[".concat(c(),"] [evaluateRequirementRules] Rule: ").concat(e.field_name," ").concat(n," ").concat(e.field_value," | Target: ").concat(o," | Result: ").concat(a)),a}),i="AND"===n?a.every(e=>e):a.some(e=>e);return console.log("[".concat(c(),"] [evaluateRequirementRules] Final result with ").concat(n," logic: ").concat(i)),i}(o.requirement_rules,o.requirement_logic,n)){if(console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is required by requirement rules")),o.is_handle||"handle"===o.input_type||o.name.endsWith("_handle"))if(t)return console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is a handle input and is connected, not required for direct input despite requirement rules")),!1;else console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is a handle input but NOT connected, required for direct input due to requirement rules"));return!0}}if(!0===o.required){if(o.is_handle||"handle"===o.input_type||o.name.endsWith("_handle"))if(t)return console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is a handle input and is connected, not required for direct input")),!1;else return console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is a handle input but NOT connected, required for direct input")),!0;return console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is explicitly marked as required")),!0}if(o.is_handle||"handle"===o.input_type||o.name.endsWith("_handle"))return console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is a handle input and not explicitly required, not required for direct input")),!1;if(a(e)){if(console.log("[".concat(c(),"] [isFieldRequired] Node is an MCP component, applying special rules for field ").concat(o.name)),["link","api_key","base_url"].includes(o.name))return console.log("[".concat(c(),"] [isFieldRequired] Field ").concat(o.name," is in the common optional fields list for MCP components")),!1;let e=!1!==o.required;return console.log("[".concat(c(),"] [isFieldRequired] MCP component field ").concat(o.name," required status: ").concat(e?"REQUIRED":"OPTIONAL"," (required !== false: ").concat(!1!==o.required,")")),e}let n=!1!==o.required;return console.log("[".concat(c(),"] [isFieldRequired] Standard component field ").concat(o.name," required status: ").concat(n?"REQUIRED":"OPTIONAL"," (required !== false: ").concat(!1!==o.required,")")),n}function l(e,o){if(console.log("[".concat(c(),"] [isValueEmpty] Checking if value is empty:\n    - Input type: ").concat(o,"\n    - Value type: ").concat(typeof e,"\n    - Value: ").concat(JSON.stringify(e),"\n    - Is undefined: ").concat(void 0===e?"YES":"NO","\n    - Is null: ").concat(null===e?"YES":"NO")),null==e)return console.log("[".concat(c(),"] [isValueEmpty] Value is undefined or null, considered EMPTY")),!0;if("boolean"===o||"bool"===o)return console.log("[".concat(c(),"] [isValueEmpty] Boolean value ").concat(e,", considered NOT EMPTY (false is a valid value)")),!1;if("number"===o||"int"===o||"float"===o)return console.log("[".concat(c(),"] [isValueEmpty] Numeric value ").concat(e,", considered NOT EMPTY (0 is a valid value)")),!1;if("string"===o||"text"===o){let o=""===e;return console.log("[".concat(c(),'] [isValueEmpty] String value "').concat(e,'", considered ').concat(o?"EMPTY":"NOT EMPTY")),o}if(("object"===o||"dict"===o||"json"===o)&&"object"==typeof e){let o=0===Object.keys(e).length;return console.log("[".concat(c(),"] [isValueEmpty] Object value with ").concat(Object.keys(e).length," keys, considered ").concat(o?"EMPTY":"NOT EMPTY")),o}if(("array"===o||"list"===o)&&Array.isArray(e)){let o=0===e.length;return console.log("[".concat(c(),"] [isValueEmpty] Array value with ").concat(e.length," items, considered ").concat(o?"EMPTY":"NOT EMPTY")),o}if(("object"===o||"dict"===o||"json"===o||"array"===o||"list"===o)&&"string"==typeof e){let o=e.trim(),t=""===o||"{}"===o||"[]"===o;return console.log("[".concat(c(),'] [isValueEmpty] String representation of object/array: "').concat(o,'", considered ').concat(t?"EMPTY":"NOT EMPTY")),t}let t=""===e;return console.log("[".concat(c(),'] [isValueEmpty] Default case, value: "').concat(e,'", considered ').concat(t?"EMPTY":"NOT EMPTY")),t}function r(e,o,t){let n=t.some(t=>t.target===e&&t.targetHandle===o);return console.log("[".concat(c(),"] [isHandleConnected] Checking if handle ").concat(e,".").concat(o," is connected: ").concat(n?"YES":"NO")),n}function s(e,o){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];console.log("[".concat(c(),"] [collectAllFields] ========== STARTING ALL FIELDS COLLECTION ==========")),console.log("[".concat(c(),"] [collectAllFields] Total nodes: ").concat(e.length,", Connected nodes: ").concat(o.size,", Edges: ").concat(s.length)),console.log("[".concat(c(),"] [collectAllFields] Connected node IDs: ").concat(Array.from(o).join(", ")));let d=e.find(e=>"StartNode"===e.data.originalType),u=new Set,g=new Map;if(d){let{getDirectlyConnectedNodes:o,getDirectlyConnectedFields:n}=t(56092);u=o(e,s,d.id),g=n(e,s,d.id),console.log("[".concat(c(),"] [collectAllFields] Directly connected node IDs: ").concat(Array.from(u).join(", "))),g.forEach((e,o)=>{console.log("[".concat(c(),"] [collectAllFields] Node ").concat(o," has directly connected fields: ").concat(Array.from(e).join(", ")))})}let p=[];return e.forEach(e=>{var t,d;if(console.log("[".concat(c(),"] [collectAllFields] Examining node: ").concat(e.id," (").concat(e.data.label||"Unnamed",")")),!o.has(e.id)){console.log("[".concat(c(),"] [collectAllFields] Skipping node ").concat(e.id," - not connected to start node"));return}if(!(null===(d=e.data)||void 0===d?void 0:null===(t=d.definition)||void 0===t?void 0:t.inputs)){console.log("[".concat(c(),"] [collectAllFields] Skipping node ").concat(e.id," - no definition or inputs"));return}let f=e.data.definition.inputs,m=e.data.config||{};console.log("[".concat(c(),"] [collectAllFields] Node details:\n      - ID: ").concat(e.id,"\n      - Label: ").concat(e.data.label||"Unnamed","\n      - Type: ").concat(e.data.type,"\n      - Original Type: ").concat(e.data.originalType,"\n      - Position: (").concat(e.position.x,", ").concat(e.position.y,")\n      - Total inputs: ").concat(f.length,"\n      - Config keys: ").concat(Object.keys(m).join(", ")||"none","\n      - Is MCP component: ").concat(a(e)?"YES":"NO")),f.forEach(t=>{var a,d,f,_,y;console.log("[".concat(c(),"] [collectAllFields] Examining input: ").concat(t.name," (").concat(t.display_name||t.name,")"));let h=!1;t.is_handle&&((h=r(e.id,t.name,s))?console.log("[".concat(c(),"] [collectAllFields] Handle input ").concat(t.name," is connected, including in field list")):console.log("[".concat(c(),"] [collectAllFields] Handle input ").concat(t.name," is not connected, including in field list"))),console.log("[".concat(c(),"] [collectAllFields] Input details:\n        - Name: ").concat(t.name,"\n        - Display Name: ").concat(t.display_name||t.name,"\n        - Type: ").concat(t.input_type,"\n        - Required flag: ").concat(void 0===t.required?"undefined":t.required,"\n        - Is handle: ").concat(t.is_handle?"YES":"NO","\n        - Is connected: ").concat(h?"YES":"NO","\n        - Has info: ").concat(t.info?"YES":"NO","\n        - Current config value: ").concat(JSON.stringify(m[t.name])));let S=i(e,t,h),E=l(m[t.name],t.input_type);console.log("[".concat(c(),"] [collectAllFields] Field ").concat(t.name," details:\n        - Required: ").concat(S?"YES":"NO","\n        - Is Empty: ").concat(E?"YES":"NO","\n        - Value: ").concat(JSON.stringify(m[t.name])));let N=u.has(e.id),O=(null===(a=g.get(e.id))||void 0===a?void 0:a.has(t.name))||!1,T=!1!==t.required;if(console.log("[".concat(c(),"] [collectAllFields] Field ").concat(e.data.label||"",".").concat(t.name," direct connection check:\n        - Node directly connected to Start: ").concat(N?"YES":"NO","\n        - Field handle: ").concat(t.name,"\n        - Field directly connected fields map has node: ").concat(g.has(e.id)?"YES":"NO","\n        - Field directly connected to Start: ").concat(O?"YES":"NO","\n        - All directly connected fields for this node: ").concat(g.get(e.id)?Array.from(g.get(e.id)||[]).join(", "):"none")),T||O){let a,i=e.data.label||"Unknown Node",l=t.name||"unnamed_field",r=t.display_name||t.name||"Unnamed Field",s=t.input_type||"string";if(console.log("[".concat(c(),"] [collectAllFields] Adding field with validated properties:\n          - Node Name: ").concat(i,"\n          - Field Name: ").concat(l,"\n          - Display Name: ").concat(r,"\n          - Input Type: ").concat(s,"\n          - Required: ").concat(T?"YES":"NO","\n          - Directly connected to Start: ").concat(O?"YES":"NO","\n          - Is Empty: ").concat(E?"YES":"NO")),"json"===t.input_type||"object"===t.input_type||"dict"===t.input_type)if(null===(y=e.data.definition)||void 0===y?void 0:null===(_=y.mcp_info)||void 0===_?void 0:null===(f=_.input_schema)||void 0===f?void 0:null===(d=f.properties)||void 0===d?void 0:d[t.name]){let o=e.data.definition.mcp_info.input_schema.properties[t.name];a={type:o.type,properties:o.properties||{},required:o.required||[]},console.log("[".concat(c(),"] [collectAllFields] Found MCP schema for field ").concat(t.name,":"),a)}else t.properties?(a={type:"object",properties:t.properties,required:t.required_keys||[]},console.log("[".concat(c(),"] [collectAllFields] Using input properties as schema for field ").concat(t.name,":"),a)):"keywords"===t.name&&(a={type:"object",properties:{time:{type:"string",description:"Time for the script"},objective:{type:"string",description:"Objective of the script"},audience:{type:"string",description:"Audience for the script"},gender:{type:"string",description:"Gender for the script"},tone:{type:"string",description:"Tone of the script"},speakers:{type:"string",description:"Speaker in the script"}},required:[]},console.log("[".concat(c(),"] [collectAllFields] Using predefined schema for keywords field:"),a));console.log("[".concat(c(),"] [collectAllFields] Field ").concat(i,".").concat(l," connection status:\n          - Node connected to Start: ").concat(o.has(e.id)?"YES":"NO","\n          - Node directly connected to Start: ").concat(N?"YES":"NO","\n          - Field directly connected to Start: ").concat(O?"YES":"NO")),p.push({nodeId:e.id,nodeName:i,name:l,displayName:r,info:t.info||void 0,inputType:s,connected_to_start:o.has(e.id),directly_connected_to_start:O,required:T||O,isEmpty:E,currentValue:E?void 0:n(m[t.name]),options:t.options,schema:a,is_handle:t.is_handle||!1,is_connected:h}),console.log("[".concat(c(),"] [collectAllFields] Added field ").concat(e.data.label||"",".").concat(t.name," to all fields list"))}else console.log("[".concat(c(),"] [collectAllFields] Skipping field ").concat(t.name," - not required and not directly connected to Start node"))})}),console.log("[".concat(c(),"] [collectAllFields] ========== ALL FIELDS COLLECTION COMPLETE ==========")),console.log("[".concat(c(),"] [collectAllFields] Total fields found: ").concat(p.length)),p}function d(e,o){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];console.log("[".concat(c(),"] [collectMissingRequiredFields] ========== STARTING MISSING FIELDS COLLECTION ==========")),console.log("[".concat(c(),"] [collectMissingRequiredFields] Total nodes: ").concat(e.length,", Connected nodes: ").concat(o.size,", Edges: ").concat(s.length)),console.log("[".concat(c(),"] [collectMissingRequiredFields] Connected node IDs: ").concat(Array.from(o).join(", ")));let d=e.find(e=>"StartNode"===e.data.originalType),u=new Set,g=new Map;if(d){let{getDirectlyConnectedNodes:o,getDirectlyConnectedFields:n}=t(56092);u=o(e,s,d.id),g=n(e,s,d.id),console.log("[".concat(c(),"] [collectMissingRequiredFields] Directly connected node IDs: ").concat(Array.from(u).join(", "))),g.forEach((e,o)=>{console.log("[".concat(c(),"] [collectMissingRequiredFields] Node ").concat(o," has directly connected fields: ").concat(Array.from(e).join(", ")))})}let p=[];return e.forEach(e=>{var t,d;if(console.log("[".concat(c(),"] [collectMissingRequiredFields] Examining node: ").concat(e.id," (").concat(e.data.label||"Unnamed",")")),!o.has(e.id)){console.log("[".concat(c(),"] [collectMissingRequiredFields] Skipping node ").concat(e.id," - not connected to start node"));return}if(!(null===(d=e.data)||void 0===d?void 0:null===(t=d.definition)||void 0===t?void 0:t.inputs)){console.log("[".concat(c(),"] [collectMissingRequiredFields] Skipping node ").concat(e.id," - no definition or inputs"));return}let f=e.data.definition.inputs,m=e.data.config||{};console.log("[".concat(c(),"] [collectMissingRequiredFields] Node details:\n      - ID: ").concat(e.id,"\n      - Label: ").concat(e.data.label||"Unnamed","\n      - Type: ").concat(e.data.type,"\n      - Original Type: ").concat(e.data.originalType,"\n      - Position: (").concat(e.position.x,", ").concat(e.position.y,")\n      - Total inputs: ").concat(f.length,"\n      - Config keys: ").concat(Object.keys(m).join(", ")||"none","\n      - Is MCP component: ").concat(a(e)?"YES":"NO")),f.forEach(t=>{var a,d,f,_,y;console.log("[".concat(c(),"] [collectMissingRequiredFields] Examining input: ").concat(t.name," (").concat(t.display_name||t.name,")"));let h=!1;t.is_handle&&((h=r(e.id,t.name,s))?console.log("[".concat(c(),"] [collectMissingRequiredFields] Handle input ").concat(t.name," is connected")):console.log("[".concat(c(),"] [collectMissingRequiredFields] Handle input ").concat(t.name," is not connected, checking if it needs a value"))),console.log("[".concat(c(),"] [collectMissingRequiredFields] Input details:\n        - Name: ").concat(t.name,"\n        - Display Name: ").concat(t.display_name||t.name,"\n        - Type: ").concat(t.input_type,"\n        - Required flag: ").concat(void 0===t.required?"undefined":t.required,"\n        - Is handle: ").concat(t.is_handle?"YES":"NO","\n        - Is connected: ").concat(h?"YES":"NO","\n        - Has info: ").concat(t.info?"YES":"NO","\n        - Current config value: ").concat(JSON.stringify(m[t.name])));let S=i(e,t,h),E=l(m[t.name],t.input_type);console.log("[".concat(c(),"] [collectMissingRequiredFields] Field ").concat(t.name," validation result:\n        - Required: ").concat(S?"YES":"NO","\n        - Is Empty: ").concat(E?"YES":"NO","\n        - Value: ").concat(JSON.stringify(m[t.name])));let N=u.has(e.id),O=(null===(a=g.get(e.id))||void 0===a?void 0:a.has(t.name))||!1;if(console.log("[".concat(c(),"] [collectMissingRequiredFields] Field ").concat(e.data.label||"",".").concat(t.name," direct connection check:\n        - Node directly connected to Start: ").concat(N?"YES":"NO","\n        - Field handle: ").concat(t.name,"\n        - Field directly connected fields map has node: ").concat(g.has(e.id)?"YES":"NO","\n        - Field directly connected to Start: ").concat(O?"YES":"NO","\n        - All directly connected fields for this node: ").concat(g.get(e.id)?Array.from(g.get(e.id)||[]).join(", "):"none")),S&&E||O){let a,i=e.data.label||"Unknown Node",l=t.name||"unnamed_field",r=t.display_name||t.name||"Unnamed Field",s=t.input_type||"string";if(S&&E?console.log("[".concat(c(),"] [collectMissingRequiredFields] FOUND MISSING REQUIRED FIELD: ").concat(e.data.label||"",".").concat(t.name)):O&&console.log("[".concat(c(),"] [collectMissingRequiredFields] FOUND DIRECTLY CONNECTED FIELD: ").concat(e.data.label||"",".").concat(t.name)),console.log("[".concat(c(),"] [collectMissingRequiredFields] Adding field with validated properties:\n          - Node Name: ").concat(i,"\n          - Field Name: ").concat(l,"\n          - Display Name: ").concat(r,"\n          - Input Type: ").concat(s,"\n          - Is Empty: ").concat(E?"YES":"NO","\n          - Current Value: ").concat(JSON.stringify(m[t.name]),"\n          - Directly connected to Start: ").concat(O?"YES":"NO")),"json"===t.input_type||"object"===t.input_type||"dict"===t.input_type)if(null===(y=e.data.definition)||void 0===y?void 0:null===(_=y.mcp_info)||void 0===_?void 0:null===(f=_.input_schema)||void 0===f?void 0:null===(d=f.properties)||void 0===d?void 0:d[t.name]){let o=e.data.definition.mcp_info.input_schema.properties[t.name];a={type:o.type,properties:o.properties||{},required:o.required||[]},console.log("[".concat(c(),"] [collectMissingRequiredFields] Found MCP schema for field ").concat(t.name,":"),a)}else t.properties?(a={type:"object",properties:t.properties,required:t.required_keys||[]},console.log("[".concat(c(),"] [collectMissingRequiredFields] Using input properties as schema for field ").concat(t.name,":"),a)):"keywords"===t.name&&(a={type:"object",properties:{time:{type:"string",description:"Time for the script"},objective:{type:"string",description:"Objective of the script"},audience:{type:"string",description:"Audience for the script"},gender:{type:"string",description:"Gender for the script"},tone:{type:"string",description:"Tone of the script"},speakers:{type:"string",description:"Speaker in the script"}},required:[]},console.log("[".concat(c(),"] [collectMissingRequiredFields] Using predefined schema for keywords field:"),a));console.log("[".concat(c(),"] [collectMissingRequiredFields] Field ").concat(i,".").concat(l," connection status:\n          - Node connected to Start: ").concat(o.has(e.id)?"YES":"NO","\n          - Node directly connected to Start: ").concat(N?"YES":"NO","\n          - Field directly connected to Start: ").concat(O?"YES":"NO")),p.push({nodeId:e.id,nodeName:i,name:l,displayName:r,info:t.info||void 0,inputType:s,is_handle:t.is_handle||!1,is_connected:h,connected_to_start:o.has(e.id),directly_connected_to_start:O,required:S||O,isEmpty:E,currentValue:E?void 0:n(m[t.name]),schema:a,options:t.options})}else S&&!E?console.log("[".concat(c(),"] [collectMissingRequiredFields] Field ").concat(t.name," is required but already has a value, skipping")):console.log("[".concat(c(),"] [collectMissingRequiredFields] Field ").concat(t.name," is not required, skipping"))})}),console.log("[".concat(c(),"] [collectMissingRequiredFields] ========== MISSING FIELDS COLLECTION COMPLETE ==========")),console.log("[".concat(c(),"] [collectMissingRequiredFields] Total missing fields found: ").concat(p.length)),p.length>0&&(console.log("[".concat(c(),"] [collectMissingRequiredFields] Missing fields summary:")),p.forEach((e,o)=>{console.log("[".concat(c(),"] [collectMissingRequiredFields]   ").concat(o+1,". Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType))})),p}t.d(o,{pt:()=>d,vp:()=>s}),t(51456)},69527:(e,o,t)=>{t.r(o),t.d(o,{API_BASE_URL:()=>m,BACKEND_API_URL:()=>_,createCredential:()=>F,debugMCPTools:()=>y,deleteCredential:()=>w,executeWorkflow:()=>N,executeWorkflowWithUserInputs:()=>R,fetchComponents:()=>S,fetchCredentials:()=>A,fetchMCPComponents:()=>E,fetchMCPTools:()=>h,processJsonObject:()=>T,saveWorkflowToServer:()=>O,sendApprovalDecision:()=>C,validateWorkflow:()=>v});var n=t(3201),c=t(89761),a=t(54897),i=t(56092),l=t(58706),r=t(27735);let s="workflow_builder_credentials";function d(){try{let e=localStorage.getItem(s);if(!e)return[];return JSON.parse(e)}catch(e){return console.error("Error reading credentials from localStorage:",e),[]}}function u(e){try{localStorage.setItem(s,JSON.stringify(e))}catch(e){console.error("Error saving credentials to localStorage:",e)}}async function g(){await new Promise(e=>setTimeout(e,300));let e=d();return console.log("Mock: Fetched credentials",e),{credentials:e}}async function p(e){await new Promise(e=>setTimeout(e,500));let o=e.name.toLowerCase().replace(/\s+/g,"_"),t=d();if(t.some(e=>e.id===o))throw Error("Credential with ID '".concat(o,"' already exists"));let n={id:o,name:e.name,type:e.type};u([...t,n]);try{localStorage.setItem("".concat(s,"_").concat(o,"_value"),e.value)}catch(e){console.error("Error saving credential value for ".concat(o,":"),e)}return console.log("Mock: Created credential",n),n}async function f(e){await new Promise(e=>setTimeout(e,400));let o=d();if(!o.some(o=>o.id===e))throw Error("Credential with ID '".concat(e,"' not found"));u(o.filter(o=>o.id!==e));try{localStorage.removeItem("".concat(s,"_").concat(e,"_value"))}catch(o){console.error("Error removing credential value for ".concat(e,":"),o)}return console.log("Mock: Deleted credential",e),{success:!0,message:"Credential '".concat(e,"' deleted successfully")}}let m="https://app-dev.rapidinnovation.dev/api/v1",_=m;async function y(e){try{if(e.node_config&&!e.node_config.selected_tool_name&&e.node_config.inputs){let o=e.node_config.inputs.find(e=>"selected_tool_name"===e.name);o&&o.value&&(e.node_config.selected_tool_name=o.value,console.log("Added selected_tool_name to debug payload: ".concat(e.node_config.selected_tool_name)))}console.log("Sending debug MCP tools request with data:",e);let o=await fetch("".concat(_,"/debug_mcp_tools"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!o.ok)throw Error("API Error: ".concat(o.status," ").concat(o.statusText));let t=await o.json();return console.log("Debug MCP tools response:",t),t}catch(e){return console.error("Failed to debug MCP Tools:",e),{success:!1,error:String(e)}}}async function h(e,o){try{let t={mode:e.mode||"Stdio",command:e.command||"",sse_url:e.sse_url||"",selected_tool_name:e.selected_tool_name||"",connection_status:e.connection_status||"Not Connected"},n=t.mode;console.log("Original mode before operation: ".concat(n)),"fetch_sse_tools"===o?(console.log("SSE URL from config: ".concat(t.sse_url)),t.mode="SSE",console.log("Forced mode to SSE for fetch_sse_tools button"),t.sse_url||console.warn("SSE URL is missing in the config!")):"fetch_stdio_tools"===o?(t.mode="Stdio",console.log("Forced mode to Stdio for fetch_stdio_tools button")):"selected_tool_name"===o&&(console.log("Tool selection action: ".concat(o)),console.log("Preserving current mode: ".concat(t.mode)),console.log("Preserving connection status: ".concat(t.connection_status)),"Connected"!==t.connection_status&&(console.log("Setting connection_status to Connected for tool selection"),t.connection_status="Connected")),t.connection_status||(t.connection_status="Not Connected");let c={config:t};if("selected_tool_name"===o?(c.selected_tool_name=t.selected_tool_name,console.log("Setting selected_tool_name in payload: ".concat(t.selected_tool_name)),"Connected"!==t.connection_status&&(console.log("Ensuring connection_status is Connected for tool selection"),t.connection_status="Connected",c.config.connection_status="Connected")):c[o]=!0,console.log("Preserving mode in payload: ".concat(n)),c.config.mode=n,console.log("Selected tool name from config: ".concat(t.selected_tool_name)),!t.selected_tool_name&&t.inputs){let e=t.inputs.find(e=>"selected_tool_name"===e.name);e&&e.value&&(t.selected_tool_name=e.value,console.log("Found selected_tool_name in inputs: ".concat(t.selected_tool_name)))}t.selected_tool_name&&(c.config.selected_tool_name=t.selected_tool_name,console.log("Added selected_tool_name to payload: ".concat(c.config.selected_tool_name))),console.log("Sending direct MCP tools fetch request with payload:",c);let a=await fetch("".concat(_,"/fetch_mcp_tools"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!a.ok)throw Error("API Error: ".concat(a.status," ").concat(a.statusText));let i=await a.json();console.log("MCP tools fetch response:",i);let l=i.result||i;if(l&&l.inputs){if(console.log("Processing result with ".concat(l.inputs.length," inputs")),"fetch_sse_tools"===o){if(l.mode="SSE",Array.isArray(l.inputs)){let e=l.inputs.find(e=>"mode"===e.name);e&&(console.log("Setting mode input value to SSE"),e.value="SSE")}}else if("fetch_stdio_tools"===o&&(l.mode="Stdio",Array.isArray(l.inputs))){let e=l.inputs.find(e=>"mode"===e.name);e&&(console.log("Setting mode input value to Stdio"),e.value="Stdio")}l._internal_state&&l._internal_state.tool_schemas&&console.log("Storing ".concat(Object.keys(l._internal_state.tool_schemas).length," tool schemas"))}return i.success?l:i}catch(e){return console.error("Failed to fetch MCP tools:",e),{success:!1,error:String(e)}}}async function S(){try{let e=await fetch("".concat(m,"/components"));if(console.log("Sent the request to fetch components with the url as :",e.url),!e.ok)throw Error("API Error: ".concat(e.status," ").concat(e.statusText));let o=await e.json();return console.log("Fetched components:",o),o}catch(e){return console.error("Failed to fetch components:",e),{}}}async function E(){try{let e;e=(0,a.XI)();let o={"Content-Type":"application/json"};e&&(o.Authorization="Bearer ".concat(e));let t=await fetch("".concat(c.Sn.MCPS.LIST),{method:"GET",headers:o});if(console.log("Sent the request to fetch MCP components with the url as:",t.url),!t.ok)throw Error("API Error: ".concat(t.status," ").concat(t.statusText));let n=await t.json();console.log("Fetched MCP response:",n);let i=[];if(Array.isArray(n))console.log("MCP response is an array with",n.length,"items"),i=n;else if(n&&n.data&&Array.isArray(n.data))console.log("MCP response has data property with",n.data.length,"items"),i=n.data;else if(console.error("MCP response is not in expected format"),console.log("MCP response type:",typeof n),console.log("MCP response structure:",Object.keys(n||{})),n&&"object"==typeof n&&n.data)console.log("Using data property from response"),i=Array.isArray(n.data)?n.data:[n.data];else{if(!n||"object"!=typeof n)return console.error("Cannot process MCP response, returning empty MCP category"),{MCP:{}};console.log("Wrapping response object in array"),i=[n]}console.log("Processed MCP data:",i);let l={MCP:{}};return i.forEach(e=>{e.mcp_tools_config&&e.mcp_tools_config.tools&&e.mcp_tools_config.tools.forEach(o=>{let t="MCP_".concat(e.name,"_").concat(o.name).replace(/\s+/g,"_");if(l.MCP[t]={name:t,display_name:"".concat(e.name," - ").concat(o.name),description:o.description||e.description,category:"MCP",icon:"Cloud",beta:!0,inputs:[],outputs:[],is_valid:!0,path:"mcp.".concat(t.toLowerCase()),type:"MCP",mcp_info:{server_id:e.id||"",server_path:e.url||"",tool_name:o.name,input_schema:o.input_schema,output_schema:o.output_schema}},o.input_schema&&o.input_schema.properties){let e=o.input_schema.required||[];Object.entries(o.input_schema.properties).forEach(o=>{let[n,c]=o,a={name:n,display_name:c.title||n.replace(/_/g," "),info:c.description||"",input_type:function(e,o){switch(e){case"string":if("uri"===o.format)return"url";if(o.enum)return"dropdown";return"string";case"number":case"integer":return"number";case"boolean":return"boolean";case"object":return"object";case"array":return"list";default:return"string"}}(c.type,c),input_types:null,required:e.includes(n),is_handle:!0,is_list:"array"===c.type,real_time_refresh:!1,advanced:!1,value:c.default||null,options:c.enum?c.enum:null,visibility_rules:null,visibility_logic:"OR"};l.MCP[t].inputs.push(a)})}o.output_schema&&o.output_schema.properties&&Object.entries(o.output_schema.properties).forEach(e=>{let[o,n]=e,c={name:o,display_name:n.title||o.replace(/_/g," "),output_type:function(e){switch(e){case"string":return"string";case"number":case"integer":return"number";case"boolean":return"boolean";case"object":return"object";case"array":return"list";default:return"any"}}(n.type)};l.MCP[t].outputs.push(c)}),0===l.MCP[t].outputs.length&&l.MCP[t].outputs.push({name:"result",display_name:"Result",output_type:"any"})})}),console.log("Transformed MCP components:",l),l}catch(e){return console.error("Failed to fetch MCP components:",e),{MCP:{}}}}async function N(e){let o=n.LN.getState(),t={nodes:e.nodes.map(e=>{let t=JSON.parse(JSON.stringify(e)),n=o.nodes[e.id];if(console.log("Original node ".concat(e.id," data:"),{config:e.data.config,definition:e.data.definition?{inputs:e.data.definition.inputs}:null}),n){for(let[o,c]of(t.data.component_state=n,n.config&&(t.data.config={...t.data.config,...n.config},console.log("Component state config for node ".concat(e.id,":"),n.config)),Object.entries(n)))"config"!==o&&null!=c&&""!==c&&(t.data.config[o]=c,console.log("Added direct value from component state for node ".concat(e.id,": ").concat(o,"=").concat(c)));console.log("Added component state to node ".concat(e.id,":"),n),console.log("Updated node config:",t.data.config)}else if(t.data.definition&&t.data.definition.inputs)for(let e of t.data.definition.inputs)null!==e.value&&void 0!==e.value&&""!==e.value&&("object"!=typeof e.value||Object.keys(e.value).length>0)&&(t.data.config[e.name]=e.value);return t}),edges:e.edges},{nodes:c,edges:i}=function(e,o){let t=JSON.parse(JSON.stringify(e)),c=JSON.parse(JSON.stringify(o));for(let e=0;e<t.length;e++)t[e]=function(e){let o=JSON.parse(JSON.stringify(e)),t=o.data,c=null==t?void 0:t.type,a=null==t?void 0:t.originalType,i="mcp"===c||"MCPMarketplaceComponent"===c||a&&a.includes("mcp_");if(!i)return o;let l=null==t?void 0:t.definition,r=(null==t?void 0:t.config)||{},s={},d=n.LN.getState().nodes[e.id]||{};if(d){for(let[e,o]of Object.entries(d.config||{}))null!=o&&""!==o&&(s[e]=o);for(let[e,o]of Object.entries(d))"config"!==e&&null!=o&&""!==o&&(s[e]=o)}if(e.data.config)for(let[o,t]of Object.entries(e.data.config))["mode","selected_tool_name","stdio_command","tool_args","node_id","_internal_state","inputs"].includes(o)||null==t||""===t||o in s||(s[o]=t);if(l&&l.inputs)for(let e of l.inputs){let o=e.name,t=e.value;null==t||""===t||"object"==typeof t&&0===Object.keys(t).length||o.endsWith("_handle")||o in s||(s[o]=t)}if(r.tool_args)for(let[e,o]of Object.entries(r.tool_args))null==o||""===o||e in s||(s[e]=o);return console.log("[MCP Transform] Node ID: ".concat(e.id)),console.log("[MCP Transform] Original type: ".concat(a)),console.log("[MCP Transform] Node type: ".concat(c)),console.log("[MCP Transform] Is MCP Marketplace: ".concat(i)),console.log("[MCP Transform] Final input values:",s),("ApiRequestNode"===t.originalType||"ApiRequestNode"===t.type)&&(console.log("[MCP Transform] Preserving API request node configuration"),t.config&&t.config.method&&(console.log("[MCP Transform] Preserving method ".concat(t.config.method," for API request node")),s.method=t.config.method)),t.config=s,o}(t[e]);return{nodes:t,edges:c}}(t.nodes,t.edges),l={nodes:c.map(e=>{if("ApiRequestNode"===e.data.originalType||"ApiRequestNode"===e.data.type){console.log("Processing API request node ".concat(e.id));let o=t.nodes.find(o=>o.id===e.id);o&&o.data.config&&o.data.config.method&&(console.log("Preserving method ".concat(o.data.config.method," for API request node ").concat(e.id)),e.data.config&&(e.data.config.method=o.data.config.method))}return e}),edges:i,workflow_id:e.workflow_id};console.log("Original execution payload:",e),console.log("Payload with state:",t),console.log("Transformed payload:",l);try{localStorage.setItem("original_payload",JSON.stringify(e)),localStorage.setItem("payload_with_state",JSON.stringify(t)),localStorage.setItem("transformed_payload",JSON.stringify(l)),console.log("Saved payloads to localStorage for debugging")}catch(e){console.error("Error saving payloads to localStorage:",e)}let r="".concat(_,"/execute");console.log("Sending execution request to ".concat(r," with payload:"),l);try{let e;e=(0,a.XI)();let o={"Content-Type":"application/json"};e&&(o.Authorization="Bearer ".concat(e));let t=await fetch(r,{method:"POST",headers:o,body:JSON.stringify(l)});if(!t.ok){let e="Unknown execution error";try{let o=await t.json();e=o.detail||JSON.stringify(o)}catch(o){e=t.statusText}throw Error("Execution failed: ".concat(t.status," ").concat(e))}let n=await t.json();if("boolean"!=typeof n.success)throw Error("Invalid response format received from backend.");return console.log("Received execution result:",n),n}catch(e){return console.error("Error executing workflow via API:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}async function O(e){let o=!!e.workflow_id,n="";o&&e.workflow_id&&(n="".concat(c.Sn.WORKFLOWS.UPDATE(e.workflow_id)));let s=o?"PATCH":"POST";console.log("Sending save workflow request to ".concat(n," with method ").concat(s," and payload:"),e);let d=e.nodes.find(e=>"StartNode"===e.data.originalType||"start-node"===e.id||"Start"===e.data.label);if(d){console.log("[WORKFLOW SAVE] Found StartNode with ID: ".concat(d.id)),d.data.config||(console.log("[WORKFLOW SAVE] StartNode has no config object, creating one"),d.data.config={}),d.data.config.collected_parameters||(console.log("[WORKFLOW SAVE] StartNode has no collected_parameters object, creating one"),d.data.config.collected_parameters={}),d.data.config&&(d.data.config.collected_parameters={}),console.log("[WORKFLOW SAVE] Using Run button's graph traversal logic to find all connected nodes");let o=(0,i.getConnectedNodes)(e.nodes,e.edges,d.id);console.log("[WORKFLOW SAVE] Found ".concat(o.size," nodes connected to StartNode: ").concat(Array.from(o).join(", ")));let t=(0,l.vp)(e.nodes,o,e.edges);console.log("[WORKFLOW SAVE] Collected ".concat(t.length," fields from connected nodes")),t.length>0&&(console.log("[WORKFLOW SAVE] Field details:"),t.forEach((e,o)=>{console.log("[WORKFLOW SAVE]   ".concat(o+1,". Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType,", Required: ").concat(e.required?"YES":"NO"))})),t.length>0?(console.log("[WORKFLOW SAVE] Adding fields to StartNode collected_parameters (excluding fields with incoming connections)"),t.forEach(e=>{let o="".concat(e.nodeId,"_").concat(e.name),t=!0===e.is_handle&&!0===e.is_connected,n=!1!==e.required,c=!0===e.directly_connected_to_start,a=void 0!==e.currentValue;console.log("[WORKFLOW SAVE] Field ".concat(o," status:\n          - required: ").concat(n?"YES":"NO","\n          - directly connected to Start: ").concat(c?"YES":"NO","\n          - has configured value: ").concat(a?"YES":"NO","\n          - has incoming connection: ").concat(t?"YES":"NO")),(0,r.K)(n,c,a,t)?d.data.config&&d.data.config.collected_parameters&&(d.data.config.collected_parameters[o]={node_id:e.nodeId,node_name:e.nodeName,input_name:e.name,value:e.currentValue,connected_to_start:!0,required:e.required,input_type:e.inputType,options:e.options},console.log("[WORKFLOW SAVE] Added field ".concat(o," to StartNode collected_parameters"))):console.log("[WORKFLOW SAVE] Skipping field ".concat(o," - excluded by filtering logic"))}),console.log("[WORKFLOW SAVE] Finished adding fields to StartNode collected_parameters")):console.log("[WORKFLOW SAVE] No fields found to add to StartNode collected_parameters"),console.log("[WORKFLOW SAVE] StartNode complete data:",d.data),console.log("[WORKFLOW SAVE] StartNode config:",d.data.config),console.log("[WORKFLOW SAVE] StartNode collected_parameters:",d.data.config.collected_parameters)}else console.log("[WORKFLOW SAVE] No StartNode found in the workflow");let u=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];console.log("[EXTRACT_START_NODE] Starting extraction from ".concat(e.length," nodes"));let t=e.find(e=>"StartNode"===e.data.originalType||"start-node"===e.id||"Start"===e.data.label);if(!t)return console.log("[EXTRACT_START_NODE] No StartNode found in the workflow"),[];if(console.log("[EXTRACT_START_NODE] Found StartNode with ID: ".concat(t.id)),!t.data.config)return console.log("[EXTRACT_START_NODE] StartNode has no config object"),[];if(!t.data.config.collected_parameters)return console.log("[EXTRACT_START_NODE] StartNode has no collected_parameters object"),[];console.log("[EXTRACT_START_NODE] StartNode config:",t.data.config),console.log("[EXTRACT_START_NODE] StartNode collected_parameters:",t.data.config.collected_parameters);let n=[];return Object.entries(t.data.config.collected_parameters).forEach(t=>{var c,a,i;let[l,s]=t;console.log("[EXTRACT_START_NODE] Processing field: ".concat(l),s);let d=s.node_id,u=s.input_name;console.log("[EXTRACT_START_NODE] Extracted nodeId: ".concat(d,", fieldName: ").concat(u));let g=!1!==s.required,p=!0===s.connected_to_start,f=e.find(e=>e.id===d),m=(null==f?void 0:null===(c=f.data)||void 0===c?void 0:c.config)&&void 0!==f.data.config[u],_=function(e,o,t){let n=t.some(t=>t.target===e&&t.targetHandle===o);return console.log("[EXTRACT_START_NODE] All edges (".concat(t.length,"):")),t.forEach((e,o)=>{console.log("[EXTRACT_START_NODE] Edge ".concat(o+1,": source=").concat(e.source,", sourceHandle=").concat(e.sourceHandle,", target=").concat(e.target,", targetHandle=").concat(e.targetHandle))}),console.log("[EXTRACT_START_NODE] Checking if handle ".concat(e,".").concat(o," is connected: ").concat(n?"YES":"NO")),n&&t.filter(t=>t.target===e&&t.targetHandle===o).forEach((e,o)=>{console.log("[EXTRACT_START_NODE] Found connecting edge ".concat(o+1,": source=").concat(e.source,", sourceHandle=").concat(e.sourceHandle,", target=").concat(e.target,", targetHandle=").concat(e.targetHandle))}),n}(d,u,o);if((0,r.J)("ExtractStartNodeData",l,(null==f?void 0:null===(a=f.data)||void 0===a?void 0:a.label)||"Unknown Node",u,g,s.required,p||!1,m||!1,m&&(null==f?void 0:null===(i=f.data)||void 0===i?void 0:i.config)?f.data.config[u]:void 0,_),(0,r.K)(g,p||!1,m||!1,_)){let e,o=s.input_type||"string";if(s.input_type?(o=s.input_type,console.log("[EXTRACT_START_NODE] Found dataType in paramData.input_type: ".concat(o))):console.log("[EXTRACT_START_NODE] No input_type found, using default: ".concat(o)),("json"===o||"object"===o)&&s.value)try{let e="object"==typeof s.value?s.value:JSON.parse(s.value);console.log("[EXTRACT_START_NODE] Processing ".concat(o," type data"));let t=T(e,u,d);console.log("[EXTRACT_START_NODE] Processed object structure:",t),n.push(t);return}catch(e){console.log("[EXTRACT_START_NODE] Error processing ".concat(o,": ").concat(e,". Using as regular string.")),o="string"}"enum"===o&&Array.isArray(s.options)&&(e=s.options,console.log("[EXTRACT_START_NODE] Found enum values in paramData.options")),console.log("[EXTRACT_START_NODE] Determined dataType: ".concat(o)),n.push({field:u,type:o,...e&&{enum:e},transition_id:d})}else console.log("[EXTRACT_START_NODE] Skipping field ".concat(l," (not required, has configured value, or not directly connected)"))}),console.log("[EXTRACT_START_NODE] Final extracted result:",n),n}(e.nodes,e.edges);console.log("[WORKFLOW SAVE] Extracted start_node_data:",u);let g=e.nodes,p=e.edges;if(d){console.log("[WORKFLOW SAVE] Using Run button's graph traversal logic to find all connected nodes");let o=(0,i.getConnectedNodes)(e.nodes,e.edges,d.id);console.log("[WORKFLOW SAVE] Found ".concat(o.size," nodes connected to StartNode: ").concat(Array.from(o).join(", ")));let n=e.nodes.length-o.size;if(n>0){console.log("[WORKFLOW SAVE] Found ".concat(n," disconnected nodes that will be excluded from the saved workflow")),g=e.nodes.filter(e=>o.has(e.id)),p=e.edges.filter(e=>o.has(e.source)&&o.has(e.target)),console.log("[WORKFLOW SAVE] Filtered workflow contains ".concat(g.length," nodes and ").concat(p.length," edges"));try{let{toast:e}=await t.e(6671).then(t.bind(t,56671));e.warning("".concat(n," unconnected ").concat(1===n?"node has":"nodes have"," been excluded from the saved workflow."),{description:"Only nodes connected to the Start node are included in the workflow.",duration:5e3})}catch(e){console.error("Failed to show toast notification:",e)}}}else console.warn("[WORKFLOW SAVE] No StartNode found, using original payload");let f={name:e.filename,description:e.filename,workflow_data:{nodes:g,edges:p},start_node_data:u};try{let e,o;e=(0,a.XI)();let t={"Content-Type":"application/json"};e&&(t.Authorization="Bearer ".concat(e)),console.log("[WORKFLOW SAVE] Final request payload:",f),console.log("[WORKFLOW SAVE] Final request payload JSON:",JSON.stringify(f,null,2));let c=await fetch(n,{method:s,headers:t,body:JSON.stringify(f)});if(!c.ok){let e="Unknown save error";try{let o=await c.json();e=o.detail||JSON.stringify(o)}catch(o){e=c.statusText}throw 400===c.status?e="Invalid workflow data. Please check your workflow configuration.":403===c.status?e="You don't have permission to save this workflow.":404===c.status?e="Workflow not found. It may have been deleted.":500===c.status&&(e="Server error occurred while saving the workflow. Please try again later."),Error("Save failed: ".concat(c.status," ").concat(e))}let i=await c.json();if(i.workflow)o={success:!0,workflow_id:i.workflow.id,message:"Workflow saved successfully",error:void 0};else if("boolean"==typeof i.success)o={success:i.success,workflow_id:i.workflow_id,message:i.message||"Workflow saved successfully",error:i.error};else throw Error("Invalid response format received from backend.");return console.log("Received save result:",o),o}catch(e){return console.error("Error saving workflow via API:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}function T(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(null==e)return{field:o,type:"string",transition_id:t};if("object"!=typeof e||Array.isArray(e))if(Array.isArray(e))if(!(e.length>0)||"object"!=typeof e[0])return{field:o,type:"array",transition_id:t};else{let n=T(e[0],"".concat(o,"Item"),t);return{field:o,type:"array",transition_id:t,properties:[n]}}else{let n=typeof e;return"number"===n&&(n=Number.isInteger(e)?"integer":"number"),{field:o,type:n,transition_id:t}}{let n=[];return Object.entries(e).forEach(e=>{let[o,c]=e;if(null==c)n.push({field:o,type:"string",transition_id:t});else if("object"!=typeof c||Array.isArray(c))if(Array.isArray(c))if(c.length>0&&"object"==typeof c[0]){let e=T(c[0],o,t);n.push({field:o,type:"array",transition_id:t,properties:[e]})}else n.push({field:o,type:"array",transition_id:t});else{let e=typeof c;"number"===e&&(e=Number.isInteger(c)?"integer":"number"),n.push({field:o,type:e,transition_id:t})}else{let e=T(c,o,t);n.push(e)}}),{field:o,type:"object",transition_id:t,properties:n}}}async function v(e){let o="".concat(_,"/validate_workflow");console.log("Sending validation request to ".concat(o," with payload:"),e);try{let t;t=(0,a.XI)();let n={"Content-Type":"application/json"};t&&(n.Authorization="Bearer ".concat(t));let c=await fetch(o,{method:"POST",headers:n,body:JSON.stringify(e)});if(!c.ok){let e="Unknown validation error";try{let o=await c.json();e=o.detail||JSON.stringify(o)}catch(o){e=c.statusText}throw Error("Validation failed: ".concat(c.status," ").concat(e))}let i=await c.json();if("boolean"!=typeof i.is_valid)throw Error("Invalid response format received from backend.");return console.log("Received validation result:",i),i}catch(e){return console.error("Error validating workflow via API:",e),{is_valid:!1,error:e instanceof Error?e.message:String(e)}}}async function A(){return g()}async function F(e){return p(e)}async function w(e){return f(e)}async function R(e){try{let o;console.log("Executing workflow with user inputs:",e),o=(0,a.XI)();let t={"Content-Type":"application/json"};o&&(t.Authorization="Bearer ".concat(o));let n=await fetch(c.Sn.WORKFLOW_EXECUTION.EXECUTE,{method:"POST",headers:t,body:JSON.stringify(e)});if(!n.ok){let e="Unknown execution error";try{let o=await n.json();e=o.detail||o.message||JSON.stringify(o)}catch(o){e=n.statusText}throw Error("Execution failed: ".concat(n.status," ").concat(e))}let i=await n.json();return console.log("Received execution result:",i),{success:!0,correlationId:i.correlationId,message:i.message||"Workflow execution started successfully"}}catch(e){return console.error("Error executing workflow with user inputs:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}async function C(e,o){try{console.log("Sending ".concat(o," decision for correlation ID: ").concat(e));let t=await fetch(c.Sn.WORKFLOW_EXECUTION.APPROVE,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat((0,a.XI)())},body:JSON.stringify({correlationId:e,decision:o})});if(!t.ok){let e="Unknown approval error";try{let o=await t.json();e=o.detail||o.message||JSON.stringify(o)}catch(o){e=t.statusText}throw Error("Approval failed: ".concat(t.status," ").concat(e))}let n=await t.json();return console.log("Received approval result:",n),{success:!0,message:n.message||"Workflow ".concat(o,"d successfully")}}catch(e){return console.error("Error sending approval decision:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}}}]);