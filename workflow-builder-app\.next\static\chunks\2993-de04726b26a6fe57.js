(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2993],{12993:(e,a,r)=>{Promise.resolve().then(r.bind(r,95592))},95592:(e,a,r)=>{"use strict";r.d(a,{default:()=>B});var s=r(95155),t=r(12115),n=r(35695),l=r(54897),i=r(93885),o=r(54361),d=r(30285),c=r(44838),m=r(59409),x=r(62523),h=r(42355),b=r(13052),p=r(5623),u=r(59434);let f=e=>{let{className:a,...r}=e;return(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,u.cn)("mx-auto flex w-full justify-center",a),...r})},w=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,s.jsx)("ul",{ref:a,className:(0,u.cn)("flex flex-row items-center gap-1",r),...t})});w.displayName="PaginationContent";let j=t.forwardRef((e,a)=>{let{className:r,...t}=e;return(0,s.jsx)("li",{ref:a,className:(0,u.cn)("",r),...t})});j.displayName="PaginationItem";let g=e=>{let{className:a,isActive:r,size:t="icon",...n}=e;return(0,s.jsx)("a",{"aria-current":r?"page":void 0,className:(0,u.cn)((0,d.r)({variant:r?"outline":"ghost",size:t}),a),...n})};g.displayName="PaginationLink";let y=e=>{let{className:a,size:r,...t}=e;return(0,s.jsx)(g,{"aria-label":"Go to previous page",className:(0,u.cn)("gap-1 pl-2.5",a),...t,children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})};y.displayName="PaginationPrevious";let N=e=>{let{className:a,size:r,...t}=e;return(0,s.jsx)(g,{"aria-label":"Go to next page",className:(0,u.cn)("gap-1 pr-2.5",a),...t,children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})};N.displayName="PaginationNext";let k=e=>{let{className:a,...r}=e;return(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,u.cn)("flex h-9 w-9 items-center justify-center",a),...r,children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"More pages"})]})};k.displayName="PaginationEllipsis";var v=r(51154),C=r(84616),A=r(66474),_=r(49992),S=r(99890),E=r(83744),P=r(47924),F=r(85339),T=r(40081),W=r(13319),D=r(89917),L=r(62525),I=r(69074),z=r(66695),U=r(90010),M=r(54165),$=r(88539),G=r(85057),R=r(56671);let Z=e=>{try{return(0,W.GP)(new Date(e),"MMM d, yyyy")}catch(e){return"Unknown date"}},O=(0,t.memo)(e=>{let{workflow:a,onClick:r}=e,n="workflow"in a?a.workflow:a,[l,o]=(0,t.useState)(!1),[m,h]=(0,t.useState)(!1),[b,u]=(0,t.useState)(!1),[f,w]=(0,t.useState)(!1),[j,g]=(0,t.useState)((null==n?void 0:n.name)||""),[y,N]=(0,t.useState)((null==n?void 0:n.description)||""),k=async()=>{if(null==n?void 0:n.id){u(!0);try{let e=await (0,i.L6)(n.id);e.success?(R.toast.success(e.message||"Workflow deleted successfully"),setTimeout(()=>{window.location.reload()},1e3)):R.toast.error("Failed to delete workflow")}catch(e){console.error("Error deleting workflow:",e),R.toast.error(e instanceof Error?e.message:"An error occurred while deleting the workflow")}finally{u(!1),o(!1)}}},v=async()=>{if(null==n?void 0:n.id){w(!0);try{await (0,i.s3)(n.id,{name:j,description:y}),R.toast.success("Workflow updated successfully"),setTimeout(()=>{window.location.reload()},1e3)}catch(e){console.error("Error updating workflow:",e),R.toast.error(e instanceof Error?e.message:"An error occurred while updating the workflow")}finally{w(!1),h(!1)}}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(z.Zp,{className:"group relative w-full cursor-pointer transition-all hover:shadow-lg",style:{backgroundColor:"#1E1E1E"},onClick:()=>r(a),onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),r(a))},tabIndex:0,role:"button","aria-label":"Select workflow: ".concat((null==n?void 0:n.name)||"Untitled Workflow"),children:(0,s.jsxs)(z.aR,{className:"relative px-3 py-3",children:[(0,s.jsx)("div",{className:"absolute right-2 z-10",style:{top:"-6px"},onClick:e=>e.stopPropagation(),role:"menu","aria-label":"Workflow options",children:(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{className:"rounded-full p-1.5 transition-colors hover:bg-gray-600",children:(0,s.jsx)(p.A,{className:"h-4 w-4 text-white"})}),(0,s.jsxs)(c.SQ,{align:"end",className:"border-brand-stroke bg-brand-card w-40",children:[(0,s.jsxs)(c._2,{className:"text-brand-primary-font hover:bg-brand-card-hover flex cursor-pointer items-center gap-2 py-2",onClick:e=>{e.stopPropagation(),g((null==n?void 0:n.name)||""),N((null==n?void 0:n.description)||""),h(!0)},children:[(0,s.jsx)(D.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Edit"})]}),(0,s.jsxs)(c._2,{className:"text-brand-unpublish hover:bg-brand-card-hover flex cursor-pointer items-center gap-2 py-2",onClick:e=>{e.stopPropagation(),o(!0)},children:[(0,s.jsx)(L.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Delete"})]})]})]})}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex flex-1 items-center gap-3 pr-4",children:[(0,s.jsx)("div",{className:"bg-brand-primary/20 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full",children:(0,s.jsx)(T.A,{className:"text-brand-primary h-5 w-5"})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsx)(z.ZB,{className:"font-primary text-base font-semibold text-white",children:(null==n?void 0:n.name)||"Untitled Workflow"}),(0,s.jsx)(z.BT,{className:"font-secondary text-xs text-gray-300",children:(null==n?void 0:n.description)||"No description provided"}),(0,s.jsxs)("div",{className:"mt-1 flex items-center gap-1 text-xs text-gray-400",children:[(0,s.jsx)(I.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:(null==n?void 0:n.created_at)?Z(n.created_at):"Unknown"})]})]})]})})]})}),(0,s.jsx)(U.Lt,{open:l,onOpenChange:o,children:(0,s.jsxs)(U.EO,{className:"border-brand-stroke bg-brand-card",children:[(0,s.jsxs)(U.wd,{children:[(0,s.jsx)(U.r7,{className:"font-primary text-brand-primary-font",children:"Delete Workflow"}),(0,s.jsx)(U.$v,{className:"font-secondary text-brand-secondary-font",children:"Are you sure you want to delete this workflow? This action cannot be undone."})]}),(0,s.jsxs)(U.ck,{children:[(0,s.jsx)(U.Zr,{disabled:b,className:"border-brand-stroke text-brand-primary-font hover:bg-brand-clicked",children:"Cancel"}),(0,s.jsx)(U.Rx,{onClick:k,disabled:b,className:"bg-brand-unpublish text-brand-white-text hover:bg-brand-unpublish/90",children:b?"Deleting...":"Delete"})]})]})}),(0,s.jsx)(M.lG,{open:m,onOpenChange:h,children:(0,s.jsxs)(M.Cf,{className:"border-brand-stroke bg-brand-card sm:max-w-[500px]",children:[(0,s.jsxs)(M.c7,{children:[(0,s.jsx)(M.L3,{className:"font-primary text-brand-primary-font",children:"Edit Workflow"}),(0,s.jsx)(M.rr,{className:"font-secondary text-brand-secondary-font",children:"Update the title and description of your workflow."})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(G.J,{htmlFor:"title",className:"text-brand-primary-font",children:"Title"}),(0,s.jsx)(x.p,{id:"title",value:j,onChange:e=>g(e.target.value),className:"border-brand-stroke bg-brand-card-hover text-brand-primary-font",placeholder:"Enter workflow title"})]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(G.J,{htmlFor:"description",className:"text-brand-primary-font",children:"Description"}),(0,s.jsx)($.T,{id:"description",value:y,onChange:e=>N(e.target.value),className:"border-brand-stroke bg-brand-card-hover text-brand-primary-font min-h-[100px]",placeholder:"Enter workflow description"})]})]}),(0,s.jsxs)(M.Es,{children:[(0,s.jsx)(d.$,{variant:"outline",onClick:()=>h(!1),className:"border-brand-stroke text-brand-primary-font hover:bg-brand-clicked",disabled:f,children:"Cancel"}),(0,s.jsx)(d.$,{onClick:v,className:"bg-brand-primary text-brand-white-text hover:bg-brand-primary/90",disabled:f,children:f?"Saving...":"Save changes"})]})]})})]})});var q=r(66766);function B(){let e=(0,n.useRouter)(),a=(0,o.k)(e=>e.user),[r,h]=(0,t.useState)([]),[b,p]=(0,t.useState)(!0),[u,W]=(0,t.useState)(null),[D,L]=(0,t.useState)("updated_desc"),[I,z]=(0,t.useState)(""),[U,M]=(0,t.useState)(!1),[$,G]=(0,t.useState)(1),[R,Z]=(0,t.useState)(1),[B,Q]=(0,t.useState)(10);(0,t.useEffect)(()=>{(async()=>{try{let e=(0,l.XI)();if(!e&&(!a||!a.accessToken)){console.error("Authentication error: No access token available"),W("User not authenticated"),p(!1),setTimeout(()=>{window.location.href="/login"},2e3);return}!e||a&&a.accessToken||(console.log("Found token in cookie but not in store, updating store"),o.k.getState().setUser({accessToken:e}))}catch(e){console.error("Error checking authentication:",e),W("Authentication error"),p(!1)}})()},[a]),(0,t.useEffect)(()=>{(async()=>{let e=null==a?void 0:a.accessToken,r=(0,l.XI)(),s=e||r;if(!s){console.error("No access token available for API request");return}try{p(!0),W(null);let e=await (0,i.vr)($,B,s);h(null==e?void 0:e.data),e.metadata&&(Z(e.metadata.totalPages||1),G(e.metadata.currentPage||1))}catch(e){console.error("Failed to fetch workflows:",e),e instanceof Error?e.message.includes("401")||e.message.includes("403")?W("Authentication failed. Please log in again."):e.message.includes("404")?W("No workflows found for this user."):e.message.includes("500")?W("Server error. Please try again later."):W("Failed to load workflows: ".concat(e.message)):W("Failed to load workflows. Please try again later.")}finally{p(!1)}})()},[a,$,B]);let J=e=>"workflow"in e&&e.workflow?e.workflow:e,V=(0,t.useMemo)(()=>{if(!r)return[];let e=r;if(I){let a=I.toLowerCase();e=r.filter(e=>{var r;let s=J(e);return(null===(r=s.name)||void 0===r?void 0:r.toLowerCase().includes(a))||s.description&&s.description.toLowerCase().includes(a)})}return[...e].sort((e,a)=>{let r=J(e),s=J(a);switch(D){case"updated_desc":default:return new Date(s.updated_at||"").getTime()-new Date(r.updated_at||"").getTime();case"updated_asc":return new Date(r.updated_at||"").getTime()-new Date(s.updated_at||"").getTime();case"name_asc":return(r.name||"").localeCompare(s.name||"");case"name_desc":return(s.name||"").localeCompare(r.name||"")}})},[r,D,I]),X=async()=>{try{M(!0);let a=await (0,i.pr)();console.log("Created new workflow:",a),e.push("/?workflow_id=".concat(a.workflow_id))}catch(e){console.error("Failed to create workflow:",e),W("Failed to create a new workflow. Please try again."),M(!1)}},K=()=>{console.log("Create from template clicked - functionality to be implemented")},Y=()=>{console.log("Import file clicked - functionality to be implemented")},H=(0,t.useCallback)(a=>{let r="workflow"in a&&a.workflow?a.workflow.id:a.id;r&&e.push("/?workflow_id=".concat(r))},[e]);return(0,s.jsxs)("main",{className:"bg-background min-h-screen",children:[(0,s.jsx)("div",{className:"bg-brand-header-bg text-brand-white-text p-6 shadow-md",children:(0,s.jsx)("div",{className:"container",style:{maxWidth:"100%"},children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)(q.default,{src:"/wflogo_white.svg",alt:"Workflow Builder Logo",width:120,height:30,className:"h-8 w-auto"})})}),(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsx)(d.$,{disabled:U,className:"bg-brand-primary hover:bg-brand-primary/90 text-white",children:U?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Create New Workflow",(0,s.jsx)(A.A,{className:"ml-2 h-4 w-4"})]})})}),(0,s.jsxs)(c.SQ,{align:"end",className:"w-56",children:[(0,s.jsxs)(c._2,{onClick:X,disabled:U,className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Create from blank"})]}),(0,s.jsxs)(c._2,{onClick:K,disabled:U,className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(S.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Create from template"})]}),(0,s.jsxs)(c._2,{onClick:Y,disabled:U,className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(E.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Import file"})]})]})]})]})})}),(0,s.jsxs)("div",{className:"bg-brand-background container px-4 py-8",style:{maxWidth:"100%",background:"black",padding:"20px 100px"},children:[(0,s.jsxs)("div",{className:"mb-6 flex flex-col items-start justify-between gap-4 md:flex-row md:items-center",children:[(0,s.jsxs)("div",{className:"relative w-full md:w-64",children:[(0,s.jsx)(P.A,{className:"text-brand-secondary-font absolute top-3 left-3 h-4 w-4","aria-hidden":"true"}),(0,s.jsx)(x.p,{placeholder:"Search workflows...",className:"border-brand-stroke focus-visible:ring-brand-primary/20 pl-10",value:I,onChange:e=>z(e.target.value),"aria-label":"Search workflows",id:"workflow-search",name:"workflow-search"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-brand-secondary-font font-secondary text-sm",children:"Sort by:"}),(0,s.jsxs)(m.l6,{value:D,onValueChange:L,children:[(0,s.jsx)(m.bq,{className:"border-brand-stroke focus:ring-brand-primary/20 w-[180px]",children:(0,s.jsx)(m.yv,{placeholder:"Sort by"})}),(0,s.jsxs)(m.gC,{className:"border-brand-border-color bg-brand-card",children:[(0,s.jsx)(m.eb,{value:"updated_desc",children:"Latest Update"}),(0,s.jsx)(m.eb,{value:"updated_asc",children:"Oldest Update"}),(0,s.jsx)(m.eb,{value:"name_asc",children:"Name (A-Z)"}),(0,s.jsx)(m.eb,{value:"name_desc",children:"Name (Z-A)"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-brand-secondary-font font-secondary text-sm",children:"Show:"}),(0,s.jsxs)(m.l6,{value:B.toString(),onValueChange:e=>{Q(Number(e)),G(1)},children:[(0,s.jsx)(m.bq,{className:"border-brand-stroke focus:ring-brand-primary/20 w-[80px]",children:(0,s.jsx)(m.yv,{placeholder:"Page size"})}),(0,s.jsxs)(m.gC,{className:"border-brand-border-color bg-brand-card",children:[(0,s.jsx)(m.eb,{value:"5",children:"5"}),(0,s.jsx)(m.eb,{value:"10",children:"10"}),(0,s.jsx)(m.eb,{value:"20",children:"20"}),(0,s.jsx)(m.eb,{value:"50",children:"50"})]})]})]})]})]}),b?(0,s.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,s.jsx)(v.A,{className:"text-brand-primary h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"font-primary ml-2 text-lg",children:"Loading workflows..."})]}):u?(0,s.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center text-center",children:[(0,s.jsx)(F.A,{className:"text-brand-unpublish mb-4 h-12 w-12"}),(0,s.jsx)("h3",{className:"font-primary mb-2 text-xl font-semibold",children:"Failed to Load Workflows"}),(0,s.jsx)("p",{className:"text-brand-secondary-font mb-4",children:u}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(d.$,{onClick:()=>window.location.reload(),className:"bg-brand-primary hover:bg-brand-primary/90 text-white",children:"Try Again"}),u.includes("authenticated")&&(0,s.jsx)(d.$,{variant:"outline",onClick:()=>window.location.href="/login",className:"border-brand-border-color text-brand-primary hover:bg-brand-clicked",children:"Go to Login"})]})]}):0===V.length?(0,s.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center text-center",children:[(0,s.jsx)(T.A,{className:"text-brand-secondary mb-4 h-12 w-12"}),(0,s.jsx)("h3",{className:"font-primary mb-2 text-xl font-semibold",children:"No Workflows Found"}),(0,s.jsx)("p",{className:"text-brand-secondary-font mb-4",children:I?"No workflows match your search criteria.":"You don't have any workflows yet. Create your first workflow to get started."}),(0,s.jsxs)(c.rI,{children:[(0,s.jsx)(c.ty,{asChild:!0,children:(0,s.jsx)(d.$,{disabled:U,className:"bg-brand-primary hover:bg-brand-primary/90 text-white",children:U?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Create New Workflow",(0,s.jsx)(A.A,{className:"ml-2 h-4 w-4"})]})})}),(0,s.jsxs)(c.SQ,{align:"center",className:"w-56",children:[(0,s.jsxs)(c._2,{onClick:X,disabled:U,className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(_.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Create from blank"})]}),(0,s.jsxs)(c._2,{onClick:K,disabled:U,className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(S.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Create from template"})]}),(0,s.jsxs)(c._2,{onClick:Y,disabled:U,className:"flex items-center gap-2 py-2",children:[(0,s.jsx)(E.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Import file"})]})]})]})]}):(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:V.map(e=>{let a=J(e).id||Math.random().toString();return(0,s.jsx)(O,{workflow:e,onClick:H},a)})}),R>1&&(0,s.jsx)(f,{className:"mt-6","aria-label":"Workflow pagination",children:(0,s.jsxs)(w,{children:[(0,s.jsx)(j,{children:(0,s.jsx)(y,{size:"default",onClick:()=>G(e=>Math.max(e-1,1)),className:"".concat($<=1?"pointer-events-none opacity-50":""," text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"),"aria-disabled":$<=1})}),$>2&&(0,s.jsx)(j,{children:(0,s.jsx)(g,{size:"default",onClick:()=>G(1),className:"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke",children:"1"})}),$>3&&(0,s.jsx)(j,{children:(0,s.jsx)(k,{className:"text-brand-secondary-font"})}),$>1&&(0,s.jsx)(j,{children:(0,s.jsx)(g,{size:"default",onClick:()=>G($-1),className:"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke",children:$-1})}),(0,s.jsx)(j,{children:(0,s.jsx)(g,{size:"default",isActive:!0,className:"brand-gradient-indicator text-brand-white-text border-none",children:$})}),$<R&&(0,s.jsx)(j,{children:(0,s.jsx)(g,{size:"default",onClick:()=>G($+1),className:"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke",children:$+1})}),$<R-2&&(0,s.jsx)(j,{children:(0,s.jsx)(k,{className:"text-brand-secondary-font"})}),$<R-1&&(0,s.jsx)(j,{children:(0,s.jsx)(g,{size:"default",onClick:()=>G(R),className:"text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke",children:R})}),(0,s.jsx)(j,{children:(0,s.jsx)(N,{size:"default",onClick:()=>G(e=>Math.min(e+1,R)),className:"".concat($>=R?"pointer-events-none opacity-50":""," text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"),"aria-disabled":$>=R})})]})})]})]})]})}}}]);