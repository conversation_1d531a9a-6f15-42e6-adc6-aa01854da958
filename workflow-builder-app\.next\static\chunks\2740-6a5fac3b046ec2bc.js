"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2740],{9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15564:(e,t,n)=>{var r=n(49509);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},defaultHead:function(){return f}});let o=n(88229),i=n(6966),l=n(95155),a=i._(n(12115)),u=o._(n(85029)),s=n(42464),d=n(82830),c=n(17544);function f(e){void 0===e&&(e=!1);let t=[(0,l.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,l.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}n(43230);let v=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:n}=t;return e.reduce(p,[]).reverse().concat(f(n).reverse()).filter(function(){let e=new Set,t=new Set,n=new Set,r={};return o=>{let i=!0,l=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){l=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=v.length;e<t;e++){let t=v[e];if(o.props.hasOwnProperty(t))if("charSet"===t)n.has(t)?i=!1:n.add(t);else{let e=o.props[t],n=r[t]||new Set;("name"!==t||!l)&&n.has(e)?i=!1:(n.add(e),r[t]=n)}}}return i}}()).reverse().map((e,t)=>{let o=e.key||t;if(r.env.__NEXT_OPTIMIZE_FONTS&&!n&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:o})})}let g=function(e){let{children:t}=e,n=(0,a.useContext)(s.AmpStateContext),r=(0,a.useContext)(d.HeadManagerContext);return(0,l.jsx)(u.default,{reduceComponentsToState:m,headManager:r,inAmpMode:(0,c.isInAmpMode)(n),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17544:(e,t)=>{function n(e){let{ampFirst:t=!1,hybrid:n=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||n&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return n}})},32588:(e,t,n)=>{n.d(t,{rc:()=>ez,ZD:()=>eK,UC:()=>eW,VY:()=>eV,hJ:()=>eU,ZL:()=>ek,bL:()=>eF,hE:()=>eB});var r,o=n(12115),i=n.t(o,2),l=n(46081),a=n(6101),u=n(85185),s=n(61285),d=n(52712),c=i[" useInsertionEffect ".trim().toString()]||d.N,f=(Symbol("RADIX:SYNC_STATE"),n(47650)),p=n(99708),v=n(95155),m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,p.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,v.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),g=n(39033),h=n(51595),y="dismissableLayer.update",w=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:f,onDismiss:p,...b}=e,C=o.useContext(w),[R,D]=o.useState(null),N=null!==(i=null==R?void 0:R.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,O]=o.useState({}),P=(0,a.s)(t,e=>D(e)),j=Array.from(C.layers),[S]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),_=j.indexOf(S),M=R?j.indexOf(R):-1,T=C.layersWithOutsidePointerEventsDisabled.size>0,I=M>=_,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,g.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));!I||n||(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},N),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,g.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...C.branches].some(e=>e.contains(t))||(null==c||c(e),null==f||f(e),e.defaultPrevented||null==p||p())},N);return(0,h.U)(e=>{M===C.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},N),o.useEffect(()=>{if(R)return l&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(R)),C.layers.add(R),E(),()=>{l&&1===C.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[R,N,l,C]),o.useEffect(()=>()=>{R&&(C.layers.delete(R),C.layersWithOutsidePointerEventsDisabled.delete(R),E())},[R,C]),o.useEffect(()=>{let e=()=>O({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,v.jsx)(m.div,{...b,ref:P,style:{pointerEvents:T?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,u.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,u.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function E(){let e=new CustomEvent(y);document.dispatchEvent(e)}function x(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&f.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(w),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,v.jsx)(m.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var C="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",D={bubbles:!1,cancelable:!0},N=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...u}=e,[s,d]=o.useState(null),c=(0,g.c)(i),f=(0,g.c)(l),p=o.useRef(null),h=(0,a.s)(t,e=>d(e)),y=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(y.paused||!s)return;let t=e.target;s.contains(t)?p.current=t:j(p.current,{select:!0})},t=function(e){if(y.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||j(p.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&j(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,y.paused]),o.useEffect(()=>{if(s){S.add(y);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(C,D);s.addEventListener(C,c),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(j(r,{select:t}),document.activeElement!==n)return}(O(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&j(s))}return()=>{s.removeEventListener(C,c),setTimeout(()=>{let t=new CustomEvent(R,D);s.addEventListener(R,f),s.dispatchEvent(t),t.defaultPrevented||j(null!=e?e:document.body,{select:!0}),s.removeEventListener(R,f),S.remove(y)},0)}}},[s,c,f,y]);let w=o.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=O(e);return[P(t,e),P(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&j(i,{select:!0})):(e.preventDefault(),n&&j(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,v.jsx)(m.div,{tabIndex:-1,...u,ref:h,onKeyDown:w})});function O(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function P(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function j(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}N.displayName="FocusScope";var S=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=_(e,t)).unshift(t)},remove(t){var n;null===(n=(e=_(e,t))[0])||void 0===n||n.resume()}}}();function _(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var M=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);(0,d.N)(()=>u(!0),[]);let s=i||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?f.createPortal((0,v.jsx)(m.div,{...l,ref:t}),s):null});M.displayName="Portal";var T=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=I(l.current);u.current="mounted"===s?e:"none"},[s]),(0,d.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=I(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,d.N)(()=>{if(r){var e;let t,n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=I(l.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=I(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=(0,a.s)(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function I(e){return(null==e?void 0:e.animationName)||"none"}T.displayName="Presence";var A=n(92293),L=n(93795),F=n(38168),k="Dialog",[U,W]=(0,l.A)(k),[z,K]=U(k),B=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:l,modal:a=!0}=e,u=o.useRef(null),d=o.useRef(null),[f,p]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return c(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}({prop:r,defaultProp:null!=i&&i,onChange:l,caller:k});return(0,v.jsx)(z,{scope:t,triggerRef:u,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:a,children:n})};B.displayName=k;var V="DialogTrigger",H=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(V,n),i=(0,a.s)(t,o.triggerRef);return(0,v.jsx)(m.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ec(o.open),...r,ref:i,onClick:(0,u.m)(e.onClick,o.onOpenToggle)})});H.displayName=V;var G="DialogPortal",[$,q]=U(G,{forceMount:void 0}),X=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,l=K(G,t);return(0,v.jsx)($,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,v.jsx)(T,{present:n||l.open,children:(0,v.jsx)(M,{asChild:!0,container:i,children:e})}))})};X.displayName=G;var Y="DialogOverlay",Z=o.forwardRef((e,t)=>{let n=q(Y,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=K(Y,e.__scopeDialog);return i.modal?(0,v.jsx)(T,{present:r||i.open,children:(0,v.jsx)(Q,{...o,ref:t})}):null});Z.displayName=Y;var J=(0,p.TL)("DialogOverlay.RemoveScroll"),Q=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(Y,n);return(0,v.jsx)(L.A,{as:J,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(m.div,{"data-state":ec(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ee="DialogContent",et=o.forwardRef((e,t)=>{let n=q(ee,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=K(ee,e.__scopeDialog);return(0,v.jsx)(T,{present:r||i.open,children:i.modal?(0,v.jsx)(en,{...o,ref:t}):(0,v.jsx)(er,{...o,ref:t})})});et.displayName=ee;var en=o.forwardRef((e,t)=>{let n=K(ee,e.__scopeDialog),r=o.useRef(null),i=(0,a.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,F.Eq)(e)},[]),(0,v.jsx)(eo,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,u.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,u.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,u.m)(e.onFocusOutside,e=>e.preventDefault())})}),er=o.forwardRef((e,t)=>{let n=K(ee,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,v.jsx)(eo,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eo=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,s=K(ee,n),d=o.useRef(null),c=(0,a.s)(t,d);return(0,A.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(N,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,v.jsx)(b,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":ec(s.open),...u,ref:c,onDismiss:()=>s.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(em,{titleId:s.titleId}),(0,v.jsx)(eg,{contentRef:d,descriptionId:s.descriptionId})]})]})}),ei="DialogTitle",el=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(ei,n);return(0,v.jsx)(m.h2,{id:o.titleId,...r,ref:t})});el.displayName=ei;var ea="DialogDescription",eu=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(ea,n);return(0,v.jsx)(m.p,{id:o.descriptionId,...r,ref:t})});eu.displayName=ea;var es="DialogClose",ed=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(es,n);return(0,v.jsx)(m.button,{type:"button",...r,ref:t,onClick:(0,u.m)(e.onClick,()=>o.onOpenChange(!1))})});function ec(e){return e?"open":"closed"}ed.displayName=es;var ef="DialogTitleWarning",[ep,ev]=(0,l.q)(ef,{contentName:ee,titleName:ei,docsSlug:"dialog"}),em=e=>{let{titleId:t}=e,n=ev(ef),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},eg=e=>{let{contentRef:t,descriptionId:n}=e,r=ev("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},eh="AlertDialog",[ey,ew]=(0,l.A)(eh,[W]),eb=W(),eE=e=>{let{__scopeAlertDialog:t,...n}=e,r=eb(t);return(0,v.jsx)(B,{...r,...n,modal:!0})};eE.displayName=eh,o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=eb(n);return(0,v.jsx)(H,{...o,...r,ref:t})}).displayName="AlertDialogTrigger";var ex=e=>{let{__scopeAlertDialog:t,...n}=e,r=eb(t);return(0,v.jsx)(X,{...r,...n})};ex.displayName="AlertDialogPortal";var eC=o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=eb(n);return(0,v.jsx)(Z,{...o,...r,ref:t})});eC.displayName="AlertDialogOverlay";var eR="AlertDialogContent",[eD,eN]=ey(eR),eO=(0,p.Dc)("AlertDialogContent"),eP=o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:r,...i}=e,l=eb(n),s=o.useRef(null),d=(0,a.s)(t,s),c=o.useRef(null);return(0,v.jsx)(ep,{contentName:eR,titleName:ej,docsSlug:"alert-dialog",children:(0,v.jsx)(eD,{scope:n,cancelRef:c,children:(0,v.jsxs)(et,{role:"alertdialog",...l,...i,ref:d,onOpenAutoFocus:(0,u.m)(i.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=c.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,v.jsx)(eO,{children:r}),(0,v.jsx)(eL,{contentRef:s})]})})})});eP.displayName=eR;var ej="AlertDialogTitle",eS=o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=eb(n);return(0,v.jsx)(el,{...o,...r,ref:t})});eS.displayName=ej;var e_="AlertDialogDescription",eM=o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=eb(n);return(0,v.jsx)(eu,{...o,...r,ref:t})});eM.displayName=e_;var eT=o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=eb(n);return(0,v.jsx)(ed,{...o,...r,ref:t})});eT.displayName="AlertDialogAction";var eI="AlertDialogCancel",eA=o.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=eN(eI,n),i=eb(n),l=(0,a.s)(t,o);return(0,v.jsx)(ed,{...i,...r,ref:l})});eA.displayName=eI;var eL=e=>{let{contentRef:t}=e,n="`".concat(eR,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(eR,"` by passing a `").concat(e_,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(eR,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return o.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},eF=eE,ek=ex,eU=eC,eW=eP,ez=eT,eK=eA,eB=eS,eV=eM},33063:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let r=n(88229),o=n(6966),i=n(95155),l=o._(n(12115)),a=r._(n(47650)),u=r._(n(15564)),s=n(38883),d=n(95840),c=n(86752);n(43230);let f=n(70901),p=r._(n(51193)),v=n(6654),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,n,r,o,i,l){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==n?void 0:n.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,o=!1;n.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function h(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}let y=(0,l.forwardRef)((e,t)=>{let{src:n,srcSet:r,sizes:o,height:a,width:u,decoding:s,className:d,style:c,fetchPriority:f,placeholder:p,loading:m,unoptimized:y,fill:w,onLoadRef:b,onLoadingCompleteRef:E,setBlurComplete:x,setShowAltText:C,sizesInput:R,onLoad:D,onError:N,...O}=e,P=(0,l.useCallback)(e=>{e&&(N&&(e.src=e.src),e.complete&&g(e,p,b,E,x,y,R))},[n,p,b,E,x,N,y,R]),j=(0,v.useMergedRef)(t,P);return(0,i.jsx)("img",{...O,...h(f),loading:m,width:u,height:a,decoding:s,"data-nimg":w?"fill":"1",className:d,style:c,sizes:o,srcSet:r,src:n,ref:j,onLoad:e=>{g(e.currentTarget,p,b,E,x,y,R)},onError:e=>{C(!0),"empty"!==p&&x(!0),N&&N(e)}})});function w(e){let{isAppRouter:t,imgAttributes:n}=e,r={as:"image",imageSrcSet:n.srcSet,imageSizes:n.sizes,crossOrigin:n.crossOrigin,referrerPolicy:n.referrerPolicy,...h(n.fetchPriority)};return t&&a.default.preload?(a.default.preload(n.src,r),null):(0,i.jsx)(u.default,{children:(0,i.jsx)("link",{rel:"preload",href:n.srcSet?void 0:n.src,...r},"__nimg-"+n.src+n.srcSet+n.sizes)})}let b=(0,l.forwardRef)((e,t)=>{let n=(0,l.useContext)(f.RouterContext),r=(0,l.useContext)(c.ImageConfigContext),o=(0,l.useMemo)(()=>{var e;let t=m||r||d.imageConfigDefault,n=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:n,deviceSizes:o,qualities:i}},[r]),{onLoad:a,onLoadingComplete:u}=e,v=(0,l.useRef)(a);(0,l.useEffect)(()=>{v.current=a},[a]);let g=(0,l.useRef)(u);(0,l.useEffect)(()=>{g.current=u},[u]);let[h,b]=(0,l.useState)(!1),[E,x]=(0,l.useState)(!1),{props:C,meta:R}=(0,s.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:h,showAltText:E});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(y,{...C,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:v,onLoadingCompleteRef:g,setBlurComplete:b,setShowAltText:x,sizesInput:e.sizes,ref:t}),R.priority?(0,i.jsx)(w,{isAppRouter:!n,imgAttributes:C}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35695:(e,t,n)=>{var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},38883:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),n(43230);let r=n(75100),o=n(95840);function i(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var n,a;let u,s,d,{src:c,sizes:f,unoptimized:p=!1,priority:v=!1,loading:m,className:g,quality:h,width:y,height:w,fill:b=!1,style:E,overrideSrc:x,onLoad:C,onLoadingComplete:R,placeholder:D="empty",blurDataURL:N,fetchPriority:O,decoding:P="async",layout:j,objectFit:S,objectPosition:_,lazyBoundary:M,lazyRoot:T,...I}=e,{imgConf:A,showAltText:L,blurComplete:F,defaultLoader:k}=t,U=A||o.imageConfigDefault;if("allSizes"in U)u=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),r=null==(n=U.qualities)?void 0:n.sort((e,t)=>e-t);u={...U,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===k)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let W=I.loader||k;delete I.loader,delete I.srcSet;let z="__next_img_default"in W;if(z){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=W;W=t=>{let{config:n,...r}=t;return e(r)}}if(j){"fill"===j&&(b=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[j];e&&(E={...E,...e});let t={responsive:"100vw",fill:"100vw"}[j];t&&!f&&(f=t)}let K="",B=l(y),V=l(w);if((a=c)&&"object"==typeof a&&(i(a)||void 0!==a.src)){let e=i(c)?c.default:c;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(s=e.blurWidth,d=e.blurHeight,N=N||e.blurDataURL,K=e.src,!b)if(B||V){if(B&&!V){let t=B/e.width;V=Math.round(e.height*t)}else if(!B&&V){let t=V/e.height;B=Math.round(e.width*t)}}else B=e.width,V=e.height}let H=!v&&("lazy"===m||void 0===m);(!(c="string"==typeof c?c:K)||c.startsWith("data:")||c.startsWith("blob:"))&&(p=!0,H=!1),u.unoptimized&&(p=!0),z&&!u.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(p=!0);let G=l(h),$=Object.assign(b?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:S,objectPosition:_}:{},L?{}:{color:"transparent"},E),q=F||"empty"===D?null:"blur"===D?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:B,heightInt:V,blurWidth:s,blurHeight:d,blurDataURL:N||"",objectFit:$.objectFit})+'")':'url("'+D+'")',X=q?{backgroundSize:$.objectFit||"cover",backgroundPosition:$.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:q}:{},Y=function(e){let{config:t,src:n,unoptimized:r,width:o,quality:i,sizes:l,loader:a}=e;if(r)return{src:n,srcSet:void 0,sizes:void 0};let{widths:u,kind:s}=function(e,t,n){let{deviceSizes:r,allSizes:o}=e;if(n){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(n);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,l),d=u.length-1;return{sizes:l||"w"!==s?l:"100vw",srcSet:u.map((e,r)=>a({config:t,src:n,quality:i,width:e})+" "+("w"===s?e:r+1)+s).join(", "),src:a({config:t,src:n,quality:i,width:u[d]})}}({config:u,src:c,unoptimized:p,width:B,quality:G,sizes:f,loader:W});return{props:{...I,loading:H?"lazy":m,fetchPriority:O,width:B,height:V,decoding:P,className:g,style:{...$,...X},sizes:Y.sizes,srcSet:Y.srcSet,src:x||Y.src},meta:{unoptimized:p,priority:v,placeholder:D,fill:b}}}},42464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return r}});let r=n(88229)._(n(12115)).default.createContext({})},47924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},51154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51193:(e,t)=>{function n(e){var t;let{config:n,src:r,width:o,quality:i}=e,l=i||(null==(t=n.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return n.path+"?url="+encodeURIComponent(r)+"&w="+o+"&q="+l+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),n.__next_img_default=!0;let r=n},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59154:(e,t,n)=>{n.d(t,{bm:()=>eN,UC:()=>eC,VY:()=>eD,hJ:()=>ex,ZL:()=>eE,bL:()=>ew,hE:()=>eR,l9:()=>eb});var r,o=n(12115),i=n.t(o,2),l=n(85185),a=n(6101),u=n(46081),s=n(61285),d=n(52712),c=i[" useInsertionEffect ".trim().toString()]||d.N,f=(Symbol("RADIX:SYNC_STATE"),n(47650)),p=n(95155);function v(e){let t=function(e){let t=o.forwardRef((e,t)=>{var n,r,i;let l,u,{children:s,...d}=e,c=o.isValidElement(s)?(u=(l=null===(r=Object.getOwnPropertyDescriptor((n=s).props,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in l&&l.isReactWarning)?n.ref:(u=(l=null===(i=Object.getOwnPropertyDescriptor(n,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in l&&l.isReactWarning)?n.props.ref:n.props.ref||n.ref:void 0,f=(0,a.s)(c,t);if(o.isValidElement(s)){let e=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=i(...t);return o(...t),r}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(d,s.props);return s.type!==o.Fragment&&(e.ref=f),o.cloneElement(s,e)}return o.Children.count(s)>1?o.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),a=l.find(g);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,p.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,p.jsx)(t,{...i,ref:n,children:r})});return n.displayName="".concat(e,".Slot"),n}var m=Symbol("radix.slottable");function g(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=v(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,p.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),y=n(39033),w=n(51595),b="dismissableLayer.update",E=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:f,onDismiss:v,...m}=e,g=o.useContext(E),[x,D]=o.useState(null),N=null!==(i=null==x?void 0:x.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,O]=o.useState({}),P=(0,a.s)(t,e=>D(e)),j=Array.from(g.layers),[S]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),_=j.indexOf(S),M=x?j.indexOf(x):-1,T=g.layersWithOutsidePointerEventsDisabled.size>0,I=M>=_,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,y.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){R("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));!I||n||(null==d||d(e),null==f||f(e),e.defaultPrevented||null==v||v())},N),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,y.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&R("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...g.branches].some(e=>e.contains(t))||(null==c||c(e),null==f||f(e),e.defaultPrevented||null==v||v())},N);return(0,w.U)(e=>{M===g.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},N),o.useEffect(()=>{if(x)return u&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(x)),g.layers.add(x),C(),()=>{u&&1===g.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[x,N,u,g]),o.useEffect(()=>()=>{x&&(g.layers.delete(x),g.layersWithOutsidePointerEventsDisabled.delete(x),C())},[x,g]),o.useEffect(()=>{let e=()=>O({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,p.jsx)(h.div,{...m,ref:P,style:{pointerEvents:T?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function C(){let e=new CustomEvent(b);document.dispatchEvent(e)}function R(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&f.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}x.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(E),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(h.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var D="focusScope.autoFocusOnMount",N="focusScope.autoFocusOnUnmount",O={bubbles:!1,cancelable:!0},P=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...u}=e,[s,d]=o.useState(null),c=(0,y.c)(i),f=(0,y.c)(l),v=o.useRef(null),m=(0,a.s)(t,e=>d(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(g.paused||!s)return;let t=e.target;s.contains(t)?v.current=t:_(v.current,{select:!0})},t=function(e){if(g.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||_(v.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&_(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,g.paused]),o.useEffect(()=>{if(s){M.add(g);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(D,O);s.addEventListener(D,c),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(_(r,{select:t}),document.activeElement!==n)return}(j(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&_(s))}return()=>{s.removeEventListener(D,c),setTimeout(()=>{let t=new CustomEvent(N,O);s.addEventListener(N,f),s.dispatchEvent(t),t.defaultPrevented||_(null!=e?e:document.body,{select:!0}),s.removeEventListener(N,f),M.remove(g)},0)}}},[s,c,f,g]);let w=o.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=j(e);return[S(t,e),S(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&_(i,{select:!0})):(e.preventDefault(),n&&_(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,p.jsx)(h.div,{tabIndex:-1,...u,ref:m,onKeyDown:w})});function j(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function S(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function _(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}P.displayName="FocusScope";var M=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=T(e,t)).unshift(t)},remove(t){var n;null===(n=(e=T(e,t))[0])||void 0===n||n.resume()}}}();function T(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var I=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);(0,d.N)(()=>u(!0),[]);let s=i||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?f.createPortal((0,p.jsx)(h.div,{...l,ref:t}),s):null});I.displayName="Portal";var A=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=L(l.current);u.current="mounted"===s?e:"none"},[s]),(0,d.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=L(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,d.N)(()=>{if(r){var e;let t,n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=L(l.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=L(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=(0,a.s)(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function L(e){return(null==e?void 0:e.animationName)||"none"}A.displayName="Presence";var F=n(92293),k=n(93795),U=n(38168),W="Dialog",[z,K]=(0,u.A)(W),[B,V]=z(W),H=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:l,modal:a=!0}=e,u=o.useRef(null),d=o.useRef(null),[f,v]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return c(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}({prop:r,defaultProp:null!=i&&i,onChange:l,caller:W});return(0,p.jsx)(B,{scope:t,triggerRef:u,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:v,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),modal:a,children:n})};H.displayName=W;var G="DialogTrigger",$=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=V(G,n),i=(0,a.s)(t,o.triggerRef);return(0,p.jsx)(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ep(o.open),...r,ref:i,onClick:(0,l.m)(e.onClick,o.onOpenToggle)})});$.displayName=G;var q="DialogPortal",[X,Y]=z(q,{forceMount:void 0}),Z=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,l=V(q,t);return(0,p.jsx)(X,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,p.jsx)(A,{present:n||l.open,children:(0,p.jsx)(I,{asChild:!0,container:i,children:e})}))})};Z.displayName=q;var J="DialogOverlay",Q=o.forwardRef((e,t)=>{let n=Y(J,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=V(J,e.__scopeDialog);return i.modal?(0,p.jsx)(A,{present:r||i.open,children:(0,p.jsx)(et,{...o,ref:t})}):null});Q.displayName=J;var ee=v("DialogOverlay.RemoveScroll"),et=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=V(J,n);return(0,p.jsx)(k.A,{as:ee,allowPinchZoom:!0,shards:[o.contentRef],children:(0,p.jsx)(h.div,{"data-state":ep(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),en="DialogContent",er=o.forwardRef((e,t)=>{let n=Y(en,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=V(en,e.__scopeDialog);return(0,p.jsx)(A,{present:r||i.open,children:i.modal?(0,p.jsx)(eo,{...o,ref:t}):(0,p.jsx)(ei,{...o,ref:t})})});er.displayName=en;var eo=o.forwardRef((e,t)=>{let n=V(en,e.__scopeDialog),r=o.useRef(null),i=(0,a.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,U.Eq)(e)},[]),(0,p.jsx)(el,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault())})}),ei=o.forwardRef((e,t)=>{let n=V(en,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,p.jsx)(el,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),el=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,s=V(en,n),d=o.useRef(null),c=(0,a.s)(t,d);return(0,F.Oh)(),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(P,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,p.jsx)(x,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":ep(s.open),...u,ref:c,onDismiss:()=>s.onOpenChange(!1)})}),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(eh,{titleId:s.titleId}),(0,p.jsx)(ey,{contentRef:d,descriptionId:s.descriptionId})]})]})}),ea="DialogTitle",eu=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=V(ea,n);return(0,p.jsx)(h.h2,{id:o.titleId,...r,ref:t})});eu.displayName=ea;var es="DialogDescription",ed=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=V(es,n);return(0,p.jsx)(h.p,{id:o.descriptionId,...r,ref:t})});ed.displayName=es;var ec="DialogClose",ef=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=V(ec,n);return(0,p.jsx)(h.button,{type:"button",...r,ref:t,onClick:(0,l.m)(e.onClick,()=>o.onOpenChange(!1))})});function ep(e){return e?"open":"closed"}ef.displayName=ec;var ev="DialogTitleWarning",[em,eg]=(0,u.q)(ev,{contentName:en,titleName:ea,docsSlug:"dialog"}),eh=e=>{let{titleId:t}=e,n=eg(ev),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},ey=e=>{let{contentRef:t,descriptionId:n}=e,r=eg("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(i)},[i,t,n]),null},ew=H,eb=$,eE=Z,ex=Q,eC=er,eR=eu,eD=ed,eN=ef},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66766:(e,t,n)=>{n.d(t,{default:()=>o.a});var r=n(71469),o=n.n(r)},70901:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(88229)._(n(12115)).default.createContext(null)},71469:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return u},getImageProps:function(){return a}});let r=n(88229),o=n(38883),i=n(33063),l=r._(n(51193));function a(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let u=i.Image},75100:(e,t)=>{function n(e){let{widthInt:t,heightInt:n,blurWidth:r,blurHeight:o,blurDataURL:i,objectFit:l}=e,a=r?40*r:t,u=o?40*o:n,s=a&&u?"viewBox='0 0 "+a+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},83744:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("file-up",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85029:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=n(12115),o=r.useLayoutEffect,i=r.useEffect;function l(e){let{headManager:t,reduceComponentsToState:n}=e;function l(){if(t&&t.mountedInstances){let o=r.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(n(o,e))}}return o(()=>{var n;return null==t||null==(n=t.mountedInstances)||n.add(e.children),()=>{var n;null==t||null==(n=t.mountedInstances)||n.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},86752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let r=n(88229)._(n(12115)),o=n(95840),i=r.default.createContext(o.imageConfigDefault)},87560:(e,t,n)=>{n.d(t,{H_:()=>t6,UC:()=>t3,YJ:()=>t8,q7:()=>t4,VF:()=>nt,JU:()=>t9,ZL:()=>t5,z6:()=>t7,hN:()=>ne,bL:()=>t1,wv:()=>nn,Pb:()=>nr,G5:()=>ni,ZP:()=>no,l9:()=>t2});var r,o,i=n(12115),l=n.t(i,2),a=n(85185),u=n(6101),s=n(46081),d=n(52712),c=l[" useInsertionEffect ".trim().toString()]||d.N;function f({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),l=i.useRef(t);return c(()=>{l.current=t},[t]),i.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}Symbol("RADIX:SYNC_STATE");var p=n(47650),v=n(99708),m=n(95155),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,v.TL)(`Primitive.${t}`),r=i.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,m.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function h(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function y(e,t){var n=h(e,t,"get");return n.get?n.get.call(e):n.value}function w(e,t,n){var r=h(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}function b(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[o,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=i.useRef(null),l=i.useRef(new Map).current;return(0,m.jsx)(o,{scope:t,itemMap:l,collectionRef:r,children:n})};a.displayName=t;let d=e+"CollectionSlot",c=(0,v.TL)(d),f=i.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(d,n),i=(0,u.s)(t,o.collectionRef);return(0,m.jsx)(c,{ref:i,children:r})});f.displayName=d;let p=e+"CollectionItemSlot",g="data-radix-collection-item",h=(0,v.TL)(p),y=i.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=i.useRef(null),s=(0,u.s)(t,a),d=l(p,n);return i.useEffect(()=>(d.itemMap.set(a,{ref:a,...o}),()=>void d.itemMap.delete(a))),(0,m.jsx)(h,{[g]:"",ref:s,children:r})});return y.displayName=p,[{Provider:a,Slot:f,ItemSlot:y},function(t){let n=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var E=new WeakMap;function x(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=C(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function C(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;var R=n(94315),D=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,v.TL)(`Primitive.${t}`),r=i.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,m.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function N(e,t){e&&p.flushSync(()=>e.dispatchEvent(t))}var O=n(39033),P=n(51595),j="dismissableLayer.update",S=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),_=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:f,onDismiss:p,...v}=e,g=i.useContext(S),[h,y]=i.useState(null),w=null!==(r=null==h?void 0:h.ownerDocument)&&void 0!==r?r:null===(n=globalThis)||void 0===n?void 0:n.document,[,b]=i.useState({}),E=(0,u.s)(t,e=>y(e)),x=Array.from(g.layers),[C]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),R=x.indexOf(C),N=h?x.indexOf(h):-1,_=g.layersWithOutsidePointerEventsDisabled.size>0,I=N>=R,A=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,O.c)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){T("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...g.branches].some(e=>e.contains(t));!I||n||(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},w),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,O.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&T("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...g.branches].some(e=>e.contains(t))||(null==c||c(e),null==f||f(e),e.defaultPrevented||null==p||p())},w);return(0,P.U)(e=>{N===g.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},w),i.useEffect(()=>{if(h)return l&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(o=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(h)),g.layers.add(h),M(),()=>{l&&1===g.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=o)}},[h,w,l,g]),i.useEffect(()=>()=>{h&&(g.layers.delete(h),g.layersWithOutsidePointerEventsDisabled.delete(h),M())},[h,g]),i.useEffect(()=>{let e=()=>b({});return document.addEventListener(j,e),()=>document.removeEventListener(j,e)},[]),(0,m.jsx)(D.div,{...v,ref:E,style:{pointerEvents:_?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});function M(){let e=new CustomEvent(j);document.dispatchEvent(e)}function T(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?N(i,l):i.dispatchEvent(l)}_.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(S),r=i.useRef(null),o=(0,u.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,m.jsx)(D.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var I=n(92293),A="focusScope.autoFocusOnMount",L="focusScope.autoFocusOnUnmount",F={bubbles:!1,cancelable:!0},k=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:l,...a}=e,[s,d]=i.useState(null),c=(0,O.c)(o),f=(0,O.c)(l),p=i.useRef(null),v=(0,u.s)(t,e=>d(e)),g=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(g.paused||!s)return;let t=e.target;s.contains(t)?p.current=t:z(p.current,{select:!0})},t=function(e){if(g.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||z(p.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&z(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,g.paused]),i.useEffect(()=>{if(s){K.add(g);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(A,F);s.addEventListener(A,c),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(z(r,{select:t}),document.activeElement!==n)return}(U(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&z(s))}return()=>{s.removeEventListener(A,c),setTimeout(()=>{let t=new CustomEvent(L,F);s.addEventListener(L,f),s.dispatchEvent(t),t.defaultPrevented||z(null!=e?e:document.body,{select:!0}),s.removeEventListener(L,f),K.remove(g)},0)}}},[s,c,f,g]);let h=i.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=U(e);return[W(t,e),W(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&z(i,{select:!0})):(e.preventDefault(),n&&z(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,m.jsx)(D.div,{tabIndex:-1,...a,ref:v,onKeyDown:h})});function U(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function W(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function z(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}k.displayName="FocusScope";var K=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=B(e,t)).unshift(t)},remove(t){var n;null===(n=(e=B(e,t))[0])||void 0===n||n.resume()}}}();function B(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var V=n(61285),H=n(84945),G=n(22475),$=i.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,m.jsx)(D.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,m.jsx)("polygon",{points:"0,0 30,0 15,10"})})});$.displayName="Arrow";var q=n(11275),X="Popper",[Y,Z]=(0,s.A)(X),[J,Q]=Y(X),ee=e=>{let{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return(0,m.jsx)(J,{scope:t,anchor:r,onAnchorChange:o,children:n})};ee.displayName=X;var et="PopperAnchor",en=i.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,l=Q(et,n),a=i.useRef(null),s=(0,u.s)(t,a);return i.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,m.jsx)(D.div,{...o,ref:s})});en.displayName=et;var er="PopperContent",[eo,ei]=Y(er),el=i.forwardRef((e,t)=>{var n,r,o,l,a,s,c,f;let{__scopePopper:p,side:v="bottom",sideOffset:g=0,align:h="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:E=[],collisionPadding:x=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:N="optimized",onPlaced:P,...j}=e,S=Q(er,p),[_,M]=i.useState(null),T=(0,u.s)(t,e=>M(e)),[I,A]=i.useState(null),L=(0,q.X)(I),F=null!==(c=null==L?void 0:L.width)&&void 0!==c?c:0,k=null!==(f=null==L?void 0:L.height)&&void 0!==f?f:0,U="number"==typeof x?x:{top:0,right:0,bottom:0,left:0,...x},W=Array.isArray(E)?E:[E],z=W.length>0,K={padding:U,boundary:W.filter(ed),altBoundary:z},{refs:B,floatingStyles:V,placement:$,isPositioned:X,middlewareData:Y}=(0,H.we)({strategy:"fixed",placement:v+("center"!==h?"-"+h:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,G.ll)(...t,{animationFrame:"always"===N})},elements:{reference:S.anchor},middleware:[(0,H.cY)({mainAxis:g+k,alignmentAxis:y}),b&&(0,H.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?(0,H.ER)():void 0,...K}),b&&(0,H.UU)({...K}),(0,H.Ej)({...K,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),I&&(0,H.UE)({element:I,padding:w}),ec({arrowWidth:F,arrowHeight:k}),R&&(0,H.jD)({strategy:"referenceHidden",...K})]}),[Z,J]=ef($),ee=(0,O.c)(P);(0,d.N)(()=>{X&&(null==ee||ee())},[X,ee]);let et=null===(n=Y.arrow)||void 0===n?void 0:n.x,en=null===(r=Y.arrow)||void 0===r?void 0:r.y,ei=(null===(o=Y.arrow)||void 0===o?void 0:o.centerOffset)!==0,[el,ea]=i.useState();return(0,d.N)(()=>{_&&ea(window.getComputedStyle(_).zIndex)},[_]),(0,m.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:X?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:el,"--radix-popper-transform-origin":[null===(l=Y.transformOrigin)||void 0===l?void 0:l.x,null===(a=Y.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(s=Y.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,m.jsx)(eo,{scope:p,placedSide:Z,onArrowChange:A,arrowX:et,arrowY:en,shouldHideArrow:ei,children:(0,m.jsx)(D.div,{"data-side":Z,"data-align":J,...j,ref:T,style:{...j.style,animation:X?void 0:"none"}})})})});el.displayName=er;var ea="PopperArrow",eu={top:"bottom",right:"left",bottom:"top",left:"right"},es=i.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ei(ea,n),i=eu[o.placedSide];return(0,m.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,m.jsx)($,{...r,ref:t,style:{...r.style,display:"block"}})})});function ed(e){return null!==e}es.displayName=ea;var ec=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,d=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=ef(a),m={start:"0%",center:"50%",end:"100%"}[v],g=(null!==(i=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+c/2,h=(null!==(l=null===(o=s.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=d?m:"".concat(g,"px"),w="".concat(-f,"px")):"top"===p?(y=d?m:"".concat(g,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=d?m:"".concat(h,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=d?m:"".concat(h,"px")),{data:{x:y,y:w}}}});function ef(e){let[t,n="center"]=e.split("-");return[t,n]}var ep=i.forwardRef((e,t)=>{var n,r;let{container:o,...l}=e,[a,u]=i.useState(!1);(0,d.N)(()=>u(!0),[]);let s=o||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?p.createPortal((0,m.jsx)(D.div,{...l,ref:t}),s):null});ep.displayName="Portal";var ev=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=i.useState(),l=i.useRef(null),a=i.useRef(e),u=i.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return i.useEffect(()=>{let e=em(l.current);u.current="mounted"===s?e:"none"},[s]),(0,d.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=em(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,d.N)(()=>{if(r){var e;let t,n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=em(l.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=em(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:i.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):i.Children.only(n),l=(0,u.s)(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?i.cloneElement(o,{ref:l}):null};function em(e){return(null==e?void 0:e.animationName)||"none"}ev.displayName="Presence";var eg=l[" useInsertionEffect ".trim().toString()]||d.N,eh=(Symbol("RADIX:SYNC_STATE"),"rovingFocusGroup.onEntryFocus"),ey={bubbles:!1,cancelable:!0},ew="RovingFocusGroup",[eb,eE,ex]=b(ew),[eC,eR]=(0,s.A)(ew,[ex]),[eD,eN]=eC(ew),eO=i.forwardRef((e,t)=>(0,m.jsx)(eb.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(eb.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(eP,{...e,ref:t})})}));eO.displayName=ew;var eP=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:l,currentTabStopId:s,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:c,onEntryFocus:f,preventScrollOnEntryFocus:p=!1,...v}=e,g=i.useRef(null),h=(0,u.s)(t,g),y=(0,R.jH)(l),[w,b]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),l=i.useRef(t);return eg(()=>{l.current=t},[t]),i.useEffect(()=>{o.current!==n&&(l.current?.(n),o.current=n)},[n,o]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,i.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}({prop:s,defaultProp:null!=d?d:null,onChange:c,caller:ew}),[E,x]=i.useState(!1),C=(0,O.c)(f),N=eE(n),P=i.useRef(!1),[j,S]=i.useState(0);return i.useEffect(()=>{let e=g.current;if(e)return e.addEventListener(eh,C),()=>e.removeEventListener(eh,C)},[C]),(0,m.jsx)(eD,{scope:n,orientation:r,dir:y,loop:o,currentTabStopId:w,onItemFocus:i.useCallback(e=>b(e),[b]),onItemShiftTab:i.useCallback(()=>x(!0),[]),onFocusableItemAdd:i.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>S(e=>e-1),[]),children:(0,m.jsx)(D.div,{tabIndex:E||0===j?-1:0,"data-orientation":r,...v,ref:h,style:{outline:"none",...e.style},onMouseDown:(0,a.m)(e.onMouseDown,()=>{P.current=!0}),onFocus:(0,a.m)(e.onFocus,e=>{let t=!P.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(eh,ey);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);eM([e.find(e=>e.active),e.find(e=>e.id===w),...e].filter(Boolean).map(e=>e.ref.current),p)}}P.current=!1}),onBlur:(0,a.m)(e.onBlur,()=>x(!1))})})}),ej="RovingFocusGroupItem",eS=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:l,children:u,...s}=e,d=(0,V.B)(),c=l||d,f=eN(ej,n),p=f.currentTabStopId===c,v=eE(n),{onFocusableItemAdd:g,onFocusableItemRemove:h,currentTabStopId:y}=f;return i.useEffect(()=>{if(r)return g(),()=>h()},[r,g,h]),(0,m.jsx)(eb.ItemSlot,{scope:n,id:c,focusable:r,active:o,children:(0,m.jsx)(D.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...s,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{r?f.onItemFocus(c):e.preventDefault()}),onFocus:(0,a.m)(e.onFocus,()=>f.onItemFocus(c)),onKeyDown:(0,a.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return e_[o]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>eM(n))}}),children:"function"==typeof u?u({isCurrentTabStop:p,hasTabStop:null!=y}):u})})});eS.displayName=ej;var e_={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function eM(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var eT=n(38168),eI=n(93795),eA=["Enter"," "],eL=["ArrowUp","PageDown","End"],eF=["ArrowDown","PageUp","Home",...eL],ek={ltr:[...eA,"ArrowRight"],rtl:[...eA,"ArrowLeft"]},eU={ltr:["ArrowLeft"],rtl:["ArrowRight"]},eW="Menu",[ez,eK,eB]=b(eW),[eV,eH]=(0,s.A)(eW,[eB,Z,eR]),eG=Z(),e$=eR(),[eq,eX]=eV(eW),[eY,eZ]=eV(eW),eJ=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:l,modal:a=!0}=e,u=eG(t),[s,d]=i.useState(null),c=i.useRef(!1),f=(0,O.c)(l),p=(0,R.jH)(o);return i.useEffect(()=>{let e=()=>{c.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>c.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,m.jsx)(ee,{...u,children:(0,m.jsx)(eq,{scope:t,open:n,onOpenChange:f,content:s,onContentChange:d,children:(0,m.jsx)(eY,{scope:t,onClose:i.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:c,dir:p,modal:a,children:r})})})};eJ.displayName=eW;var eQ=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eG(n);return(0,m.jsx)(en,{...o,...r,ref:t})});eQ.displayName="MenuAnchor";var e0="MenuPortal",[e1,e2]=eV(e0,{forceMount:void 0}),e5=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=eX(e0,t);return(0,m.jsx)(e1,{scope:t,forceMount:n,children:(0,m.jsx)(ev,{present:n||i.open,children:(0,m.jsx)(ep,{asChild:!0,container:o,children:r})})})};e5.displayName=e0;var e3="MenuContent",[e8,e9]=eV(e3),e4=i.forwardRef((e,t)=>{let n=e2(e3,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=eX(e3,e.__scopeMenu),l=eZ(e3,e.__scopeMenu);return(0,m.jsx)(ez.Provider,{scope:e.__scopeMenu,children:(0,m.jsx)(ev,{present:r||i.open,children:(0,m.jsx)(ez.Slot,{scope:e.__scopeMenu,children:l.modal?(0,m.jsx)(e6,{...o,ref:t}):(0,m.jsx)(e7,{...o,ref:t})})})})}),e6=i.forwardRef((e,t)=>{let n=eX(e3,e.__scopeMenu),r=i.useRef(null),o=(0,u.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return(0,eT.Eq)(e)},[]),(0,m.jsx)(tt,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),e7=i.forwardRef((e,t)=>{let n=eX(e3,e.__scopeMenu);return(0,m.jsx)(tt,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),te=(0,v.TL)("MenuContent.ScrollLock"),tt=i.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:c,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:v,onInteractOutside:g,onDismiss:h,disableOutsideScroll:y,...w}=e,b=eX(e3,n),E=eZ(e3,n),x=eG(n),C=e$(n),R=eK(n),[D,N]=i.useState(null),O=i.useRef(null),P=(0,u.s)(t,O,b.onContentChange),j=i.useRef(0),S=i.useRef(""),M=i.useRef(0),T=i.useRef(null),A=i.useRef("right"),L=i.useRef(0),F=y?eI.A:i.Fragment,U=e=>{var t,n;let r=S.current+e,o=R().filter(e=>!e.disabled),i=document.activeElement,l=null===(t=o.find(e=>e.ref.current===i))||void 0===t?void 0:t.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,l),u=null===(n=o.find(e=>e.textValue===a))||void 0===n?void 0:n.ref.current;!function e(t){S.current=t,window.clearTimeout(j.current),""!==t&&(j.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};i.useEffect(()=>()=>window.clearTimeout(j.current),[]),(0,I.Oh)();let W=i.useCallback(e=>{var t,n;return A.current===(null===(t=T.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,d=a.x,c=a.y;s>r!=c>r&&n<(d-u)*(r-s)/(c-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null===(n=T.current)||void 0===n?void 0:n.area)},[]);return(0,m.jsx)(e8,{scope:n,searchRef:S,onItemEnter:i.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:i.useCallback(e=>{var t;W(e)||(null===(t=O.current)||void 0===t||t.focus(),N(null))},[W]),onTriggerLeave:i.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:M,onPointerGraceIntentChange:i.useCallback(e=>{T.current=e},[]),children:(0,m.jsx)(F,{...y?{as:te,allowPinchZoom:!0}:void 0,children:(0,m.jsx)(k,{asChild:!0,trapped:o,onMountAutoFocus:(0,a.m)(l,e=>{var t;e.preventDefault(),null===(t=O.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,m.jsx)(_,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:v,onInteractOutside:g,onDismiss:h,children:(0,m.jsx)(eO,{asChild:!0,...C,dir:E.dir,orientation:"vertical",loop:r,currentTabStopId:D,onCurrentTabStopIdChange:N,onEntryFocus:(0,a.m)(c,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,m.jsx)(el,{role:"menu","aria-orientation":"vertical","data-state":tj(b.open),"data-radix-menu-content":"",dir:E.dir,...x,...w,ref:P,style:{outline:"none",...w.style},onKeyDown:(0,a.m)(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&U(e.key));let o=O.current;if(e.target!==o||!eF.includes(e.key))return;e.preventDefault();let i=R().filter(e=>!e.disabled).map(e=>e.ref.current);eL.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,a.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(j.current),S.current="")}),onPointerMove:(0,a.m)(e.onPointerMove,tM(e=>{let t=e.target,n=L.current!==e.clientX;e.currentTarget.contains(t)&&n&&(A.current=e.clientX>L.current?"right":"left",L.current=e.clientX)}))})})})})})})});e4.displayName=e3;var tn=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,m.jsx)(D.div,{role:"group",...r,ref:t})});tn.displayName="MenuGroup";var tr=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,m.jsx)(D.div,{...r,ref:t})});tr.displayName="MenuLabel";var to="MenuItem",ti="menu.itemSelect",tl=i.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...o}=e,l=i.useRef(null),s=eZ(to,e.__scopeMenu),d=e9(to,e.__scopeMenu),c=(0,u.s)(t,l),f=i.useRef(!1);return(0,m.jsx)(ta,{...o,ref:c,disabled:n,onClick:(0,a.m)(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(ti,{bubbles:!0,cancelable:!0});e.addEventListener(ti,e=>null==r?void 0:r(e),{once:!0}),N(e,t),t.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),f.current=!0},onPointerUp:(0,a.m)(e.onPointerUp,e=>{var t;f.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&eA.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});tl.displayName=to;var ta=i.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:o,...l}=e,s=e9(to,n),d=e$(n),c=i.useRef(null),f=(0,u.s)(t,c),[p,v]=i.useState(!1),[g,h]=i.useState("");return i.useEffect(()=>{let e=c.current;if(e){var t;h((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[l.children]),(0,m.jsx)(ez.ItemSlot,{scope:n,disabled:r,textValue:null!=o?o:g,children:(0,m.jsx)(eS,{asChild:!0,...d,focusable:!r,children:(0,m.jsx)(D.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...l,ref:f,onPointerMove:(0,a.m)(e.onPointerMove,tM(e=>{r?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,a.m)(e.onPointerLeave,tM(e=>s.onItemLeave(e))),onFocus:(0,a.m)(e.onFocus,()=>v(!0)),onBlur:(0,a.m)(e.onBlur,()=>v(!1))})})})}),tu=i.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,m.jsx)(tg,{scope:e.__scopeMenu,checked:n,children:(0,m.jsx)(tl,{role:"menuitemcheckbox","aria-checked":tS(n)?"mixed":n,...o,ref:t,"data-state":t_(n),onSelect:(0,a.m)(o.onSelect,()=>null==r?void 0:r(!!tS(n)||!n),{checkForDefaultPrevented:!1})})})});tu.displayName="MenuCheckboxItem";var ts="MenuRadioGroup",[td,tc]=eV(ts,{value:void 0,onValueChange:()=>{}}),tf=i.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,O.c)(r);return(0,m.jsx)(td,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,m.jsx)(tn,{...o,ref:t})})});tf.displayName=ts;var tp="MenuRadioItem",tv=i.forwardRef((e,t)=>{let{value:n,...r}=e,o=tc(tp,e.__scopeMenu),i=n===o.value;return(0,m.jsx)(tg,{scope:e.__scopeMenu,checked:i,children:(0,m.jsx)(tl,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":t_(i),onSelect:(0,a.m)(r.onSelect,()=>{var e;return null===(e=o.onValueChange)||void 0===e?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});tv.displayName=tp;var tm="MenuItemIndicator",[tg,th]=eV(tm,{checked:!1}),ty=i.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=th(tm,n);return(0,m.jsx)(ev,{present:r||tS(i.checked)||!0===i.checked,children:(0,m.jsx)(D.span,{...o,ref:t,"data-state":t_(i.checked)})})});ty.displayName=tm;var tw=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,m.jsx)(D.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});tw.displayName="MenuSeparator";var tb=i.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eG(n);return(0,m.jsx)(es,{...o,...r,ref:t})});tb.displayName="MenuArrow";var tE="MenuSub",[tx,tC]=eV(tE),tR=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,l=eX(tE,t),a=eG(t),[u,s]=i.useState(null),[d,c]=i.useState(null),f=(0,O.c)(o);return i.useEffect(()=>(!1===l.open&&f(!1),()=>f(!1)),[l.open,f]),(0,m.jsx)(ee,{...a,children:(0,m.jsx)(eq,{scope:t,open:r,onOpenChange:f,content:d,onContentChange:c,children:(0,m.jsx)(tx,{scope:t,contentId:(0,V.B)(),triggerId:(0,V.B)(),trigger:u,onTriggerChange:s,children:n})})})};tR.displayName=tE;var tD="MenuSubTrigger",tN=i.forwardRef((e,t)=>{let n=eX(tD,e.__scopeMenu),r=eZ(tD,e.__scopeMenu),o=tC(tD,e.__scopeMenu),l=e9(tD,e.__scopeMenu),s=i.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=l,f={__scopeMenu:e.__scopeMenu},p=i.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return i.useEffect(()=>p,[p]),i.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,m.jsx)(eQ,{asChild:!0,...f,children:(0,m.jsx)(ta,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":tj(n.open),...e,ref:(0,u.t)(t,o.onTriggerChange),onClick:t=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,a.m)(e.onPointerMove,tM(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||s.current||(l.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,a.m)(e.onPointerLeave,tM(e=>{var t,r;p();let o=null===(t=n.content)||void 0===t?void 0:t.getBoundingClientRect();if(o){let t=null===(r=n.content)||void 0===r?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],u=o[i?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,a.m)(e.onKeyDown,t=>{let o=""!==l.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&ek[r.dir].includes(t.key)){var i;n.onOpenChange(!0),null===(i=n.content)||void 0===i||i.focus(),t.preventDefault()}})})})});tN.displayName=tD;var tO="MenuSubContent",tP=i.forwardRef((e,t)=>{let n=e2(e3,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,l=eX(e3,e.__scopeMenu),s=eZ(e3,e.__scopeMenu),d=tC(tO,e.__scopeMenu),c=i.useRef(null),f=(0,u.s)(t,c);return(0,m.jsx)(ez.Provider,{scope:e.__scopeMenu,children:(0,m.jsx)(ev,{present:r||l.open,children:(0,m.jsx)(ez.Slot,{scope:e.__scopeMenu,children:(0,m.jsx)(tt,{id:d.contentId,"aria-labelledby":d.triggerId,...o,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null===(t=c.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,a.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=eU[s.dir].includes(e.key);if(t&&n){var r;l.onOpenChange(!1),null===(r=d.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function tj(e){return e?"open":"closed"}function tS(e){return"indeterminate"===e}function t_(e){return tS(e)?"indeterminate":e?"checked":"unchecked"}function tM(e){return t=>"mouse"===t.pointerType?e(t):void 0}tP.displayName=tO;var tT="DropdownMenu",[tI,tA]=(0,s.A)(tT,[eH]),tL=eH(),[tF,tk]=tI(tT),tU=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:l,onOpenChange:a,modal:u=!0}=e,s=tL(t),d=i.useRef(null),[c,p]=f({prop:o,defaultProp:null!=l&&l,onChange:a,caller:tT});return(0,m.jsx)(tF,{scope:t,triggerId:(0,V.B)(),triggerRef:d,contentId:(0,V.B)(),open:c,onOpenChange:p,onOpenToggle:i.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,m.jsx)(eJ,{...s,open:c,onOpenChange:p,dir:r,modal:u,children:n})})};tU.displayName=tT;var tW="DropdownMenuTrigger",tz=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,i=tk(tW,n),l=tL(n);return(0,m.jsx)(eQ,{asChild:!0,...l,children:(0,m.jsx)(g.button,{type:"button",id:i.triggerId,"aria-haspopup":"menu","aria-expanded":i.open,"aria-controls":i.open?i.contentId:void 0,"data-state":i.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,u.t)(t,i.triggerRef),onPointerDown:(0,a.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(i.onOpenToggle(),i.open||e.preventDefault())}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&i.onOpenToggle(),"ArrowDown"===e.key&&i.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});tz.displayName=tW;var tK=e=>{let{__scopeDropdownMenu:t,...n}=e,r=tL(t);return(0,m.jsx)(e5,{...r,...n})};tK.displayName="DropdownMenuPortal";var tB="DropdownMenuContent",tV=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tk(tB,n),l=tL(n),u=i.useRef(!1);return(0,m.jsx)(e4,{id:o.contentId,"aria-labelledby":o.triggerId,...l,...r,ref:t,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;u.current||null===(t=o.triggerRef.current)||void 0===t||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,a.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!o.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tV.displayName=tB;var tH=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tn,{...o,...r,ref:t})});tH.displayName="DropdownMenuGroup";var tG=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tr,{...o,...r,ref:t})});tG.displayName="DropdownMenuLabel";var t$=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tl,{...o,...r,ref:t})});t$.displayName="DropdownMenuItem";var tq=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tu,{...o,...r,ref:t})});tq.displayName="DropdownMenuCheckboxItem";var tX=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tf,{...o,...r,ref:t})});tX.displayName="DropdownMenuRadioGroup";var tY=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tv,{...o,...r,ref:t})});tY.displayName="DropdownMenuRadioItem";var tZ=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(ty,{...o,...r,ref:t})});tZ.displayName="DropdownMenuItemIndicator";var tJ=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tw,{...o,...r,ref:t})});tJ.displayName="DropdownMenuSeparator",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tb,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var tQ=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tN,{...o,...r,ref:t})});tQ.displayName="DropdownMenuSubTrigger";var t0=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=tL(n);return(0,m.jsx)(tP,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});t0.displayName="DropdownMenuSubContent";var t1=tU,t2=tz,t5=tK,t3=tV,t8=tH,t9=tG,t4=t$,t6=tq,t7=tX,ne=tY,nt=tZ,nn=tJ,nr=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,l=tL(t),[a,u]=f({prop:r,defaultProp:null!=i&&i,onChange:o,caller:"DropdownMenuSub"});return(0,m.jsx)(tR,{...l,open:a,onOpenChange:u,children:n})},no=tQ,ni=t0},95840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return r}});let n=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}}}]);