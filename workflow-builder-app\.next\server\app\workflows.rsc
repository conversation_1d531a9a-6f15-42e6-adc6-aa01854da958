1:"$Sreact.fragment"
2:I[77890,["3291","static/chunks/3291-50bd181800545647.js","6671","static/chunks/6671-a8c26f670d082532.js","4766","static/chunks/4766-7031d698975a3144.js","4720","static/chunks/4720-b1b7051d8a12e717.js","7997","static/chunks/7997-275fa46732e6b474.js","7177","static/chunks/app/layout-91ca3da93030ddbf.js"],"ThemeProvider"]
3:I[70139,["3291","static/chunks/3291-50bd181800545647.js","6671","static/chunks/6671-a8c26f670d082532.js","4766","static/chunks/4766-7031d698975a3144.js","4720","static/chunks/4720-b1b7051d8a12e717.js","7997","static/chunks/7997-275fa46732e6b474.js","7177","static/chunks/app/layout-91ca3da93030ddbf.js"],"QueryProvider"]
4:I[94819,["3291","static/chunks/3291-50bd181800545647.js","6671","static/chunks/6671-a8c26f670d082532.js","4766","static/chunks/4766-7031d698975a3144.js","4720","static/chunks/4720-b1b7051d8a12e717.js","7997","static/chunks/7997-275fa46732e6b474.js","7177","static/chunks/app/layout-91ca3da93030ddbf.js"],"AuthProvider"]
5:I[42618,["3291","static/chunks/3291-50bd181800545647.js","6671","static/chunks/6671-a8c26f670d082532.js","4766","static/chunks/4766-7031d698975a3144.js","4720","static/chunks/4720-b1b7051d8a12e717.js","7997","static/chunks/7997-275fa46732e6b474.js","7177","static/chunks/app/layout-91ca3da93030ddbf.js"],"ToastProvider"]
6:I[87555,[],""]
7:I[31295,[],""]
8:I[95592,["9352","static/chunks/9352-7a620227c8bd8866.js","3291","static/chunks/3291-50bd181800545647.js","6671","static/chunks/6671-a8c26f670d082532.js","8095","static/chunks/8095-94d0b070cc35e144.js","2740","static/chunks/2740-6a5fac3b046ec2bc.js","204","static/chunks/204-05f124ed4ea71a4b.js","7764","static/chunks/7764-5a5c577cb8574a2a.js","7907","static/chunks/7907-9f4e62eee9a19a6a.js","2993","static/chunks/2993-de04726b26a6fe57.js","7143","static/chunks/app/workflows/page-c626f63bfc9efa3c.js"],"default"]
9:I[59665,[],"OutletBoundary"]
c:I[59665,[],"ViewportBoundary"]
e:I[59665,[],"MetadataBoundary"]
10:I[26614,[],""]
:HL["/_next/static/css/940018d4805e35d7.css","style"]
0:{"P":null,"b":"1QAyynrQ8SZ0fgEBA9EXT","p":"","c":["","workflows"],"i":false,"f":[[["",{"children":["workflows",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/940018d4805e35d7.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 __variable_fb6e91 __variable_3a3c2d antialiased","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":["$","$L3",null,{"children":["$","$L4",null,{"children":[["$","$L5",null,{}],["$","$L6",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L7",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]]}]}]}]}]}]]}],{"children":["workflows",["$","$1","c",{"children":[null,["$","div",null,{"className":"min-h-screen bg-background","children":["$","$L6",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L7",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L8",null,{}],"$undefined",null,["$","$L9",null,{"children":["$La","$Lb",null]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","Ta0pjgI62nV-zX19gz38y",{"children":[["$","$Lc",null,{"children":"$Ld"}],null]}],["$","$Le",null,{"children":"$Lf"}]]}],false]],"m":"$undefined","G":["$10","$undefined"],"s":false,"S":true}
d:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
b:null
f:[["$","title","0",{"children":"Workflow Builder"}],["$","meta","1",{"name":"description","content":"Visual workflow builder for automation"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","link","3",{"rel":"icon","href":"/favicon.svg","type":"image/svg+xml"}]]
