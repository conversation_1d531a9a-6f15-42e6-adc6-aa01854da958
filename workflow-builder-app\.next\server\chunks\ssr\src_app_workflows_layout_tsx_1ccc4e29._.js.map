{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/app/workflows/layout.tsx"], "sourcesContent": ["/**\r\n * Workflows Layout - Provides layout for all workflow pages\r\n */\r\n\r\nimport { ReactNode } from 'react';\r\n\r\ninterface WorkflowsLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport default function WorkflowsLayout({ children }: WorkflowsLayoutProps) {\r\n  return (\r\n    <div className=\"min-h-screen bg-background\">\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAQc,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP", "debugId": null}}]}