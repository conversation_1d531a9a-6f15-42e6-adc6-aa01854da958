{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "ade5c750812d4e584b08982ce6fb1550", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d60879c13f765d4cdc44d2433a27d06244eed726a71f32ca7742402ad4232014", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1318c4e061b4e4a2f2fb601c312c28445f4710125ef995e22b5865f9daa9189e"}}}, "sortedMiddleware": ["/"], "functions": {}}