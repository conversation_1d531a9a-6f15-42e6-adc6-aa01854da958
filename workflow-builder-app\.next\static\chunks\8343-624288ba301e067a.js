"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8343],{5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},11275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(12115),o=n(52712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},12874:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]])},39952:(e,t,n)=>{n.d(t,{C1:()=>R,bL:()=>O});var r=n(12115),o=n.t(r,2),i=n(6101),u=n(46081),l=n(85185),a=n(52712),c=o[" useInsertionEffect ".trim().toString()]||a.N,s=(Symbol("RADIX:SYNC_STATE"),n(45503)),d=n(11275),f=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef(null),l=r.useRef(e),c=r.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=m(u.current);c.current="mounted"===s?e:"none"},[s]),(0,a.N)(()=>{let t=u.current,n=l.current;if(n!==e){let r=c.current,o=m(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),(0,a.N)(()=>{if(o){var e;let t,n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=m(u.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!l.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=m(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(t),u="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),l=(0,i.s)(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||o.isPresent?r.cloneElement(u,{ref:l}):null};function m(e){return(null==e?void 0:e.animationName)||"none"}f.displayName="Presence",n(47650);var p=n(99708),v=n(95155),y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,p.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,v.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),h="Checkbox",[b,N]=(0,u.A)(h),[k,w]=b(h),g=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:o,checked:u,defaultChecked:a,required:s,disabled:d,value:f="on",onCheckedChange:m,form:p,...b}=e,[N,w]=r.useState(null),g=(0,i.s)(t,e=>w(e)),x=r.useRef(!1),A=!N||p||!!N.closest("form"),[O,R]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){let[i,u,l]=function({defaultProp:e,onChange:t}){let[n,o]=r.useState(e),i=r.useRef(n),u=r.useRef(t);return c(()=>{u.current=t},[t]),r.useEffect(()=>{i.current!==n&&(u.current?.(n),i.current=n)},[n,i]),[n,o,u]}({defaultProp:t,onChange:n}),a=void 0!==e,s=a?e:i;{let t=r.useRef(void 0!==e);r.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,o])}return[s,r.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else u(t)},[a,e,u,l])]}({prop:u,defaultProp:null!=a&&a,onChange:m,caller:h}),T=r.useRef(O);return r.useEffect(()=>{let e=null==N?void 0:N.form;if(e){let t=()=>R(T.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[N,R]),(0,v.jsxs)(k,{scope:n,state:O,disabled:d,children:[(0,v.jsx)(y.button,{type:"button",role:"checkbox","aria-checked":M(O)?"mixed":O,"aria-required":s,"data-state":C(O),"data-disabled":d?"":void 0,disabled:d,value:f,...b,ref:g,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{R(e=>!!M(e)||!e),A&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),A&&(0,v.jsx)(E,{control:N,bubbles:!x.current,name:o,value:f,checked:O,required:s,disabled:d,form:p,style:{transform:"translateX(-100%)"},defaultChecked:!M(a)&&a})]})});g.displayName=h;var x="CheckboxIndicator",A=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=w(x,n);return(0,v.jsx)(f,{present:r||M(i.state)||!0===i.state,children:(0,v.jsx)(y.span,{"data-state":C(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});A.displayName=x;var E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,control:o,checked:u,bubbles:l=!0,defaultChecked:a,...c}=e,f=r.useRef(null),m=(0,i.s)(f,t),p=(0,s.Z)(u),h=(0,d.X)(o);r.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==u&&t){let n=new Event("click",{bubbles:l});e.indeterminate=M(u),t.call(e,!M(u)&&u),e.dispatchEvent(n)}},[p,u,l]);let b=r.useRef(!M(u)&&u);return(0,v.jsx)(y.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=a?a:b.current,...c,tabIndex:-1,ref:m,style:{...c.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function M(e){return"indeterminate"===e}function C(e){return M(e)?"indeterminate":e?"checked":"unchecked"}E.displayName="CheckboxBubbleInput";var O=g,R=A},40646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46081:(e,t,n)=>{n.d(t,{A:()=>u,q:()=>i});var r=n(12115),o=n(95155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,u=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:u,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function u(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let u=r.createContext(i),l=n.length;n=[...n,i];let a=t=>{let{scope:n,children:i,...a}=t,c=n?.[e]?.[l]||u,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:i})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[l]||u,c=r.useContext(a);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},85339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);