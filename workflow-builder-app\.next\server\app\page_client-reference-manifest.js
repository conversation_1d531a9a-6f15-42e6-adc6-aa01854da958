globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"async":false},"[project]/src/components/providers/theme-provider.tsx <module evaluation>":{"id":"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/components/providers/theme-provider.tsx":{"id":"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/components/providers/auth-provider.tsx <module evaluation>":{"id":"[project]/src/components/providers/auth-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/components/providers/auth-provider.tsx":{"id":"[project]/src/components/providers/auth-provider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/providers/QueryProvider.tsx <module evaluation>":{"id":"[project]/src/providers/QueryProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/providers/QueryProvider.tsx":{"id":"[project]/src/providers/QueryProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/providers/ToastProvider.tsx <module evaluation>":{"id":"[project]/src/providers/ToastProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/providers/ToastProvider.tsx":{"id":"[project]/src/providers/ToastProvider.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"async":false},"[project]/src/app/page.tsx <module evaluation>":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js","static/chunks/[root of the server]__f8cce6bf._.js","static/chunks/src_lib_0525c592._.js","static/chunks/src_components_13b0e1cb._.js","static/chunks/src_c3d91abc._.js","static/chunks/node_modules_next_c367bc0b._.js","static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","static/chunks/node_modules_@reactflow_core_dist_esm_index_mjs_fc9e5a1a._.js","static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","static/chunks/node_modules_@radix-ui_0461657c._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_24910780._.js","static/chunks/src_app_page_tsx_1174d750._.js"],"async":false},"[project]/src/app/page.tsx":{"id":"[project]/src/app/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js","static/chunks/[root of the server]__f8cce6bf._.js","static/chunks/src_lib_0525c592._.js","static/chunks/src_components_13b0e1cb._.js","static/chunks/src_c3d91abc._.js","static/chunks/node_modules_next_c367bc0b._.js","static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","static/chunks/node_modules_@reactflow_core_dist_esm_index_mjs_fc9e5a1a._.js","static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","static/chunks/node_modules_@radix-ui_0461657c._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_24910780._.js","static/chunks/src_app_page_tsx_1174d750._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/theme-provider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/src_lib_authApi_ts_b0530198._.js","server/chunks/ssr/node_modules_8ead631a._.js","server/chunks/ssr/[root of the server]__f4a6ca17._.js"],"async":false}},"[project]/src/components/providers/auth-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/auth-provider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/src_lib_authApi_ts_b0530198._.js","server/chunks/ssr/node_modules_8ead631a._.js","server/chunks/ssr/[root of the server]__f4a6ca17._.js"],"async":false}},"[project]/src/providers/QueryProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/QueryProvider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/src_lib_authApi_ts_b0530198._.js","server/chunks/ssr/node_modules_8ead631a._.js","server/chunks/ssr/[root of the server]__f4a6ca17._.js"],"async":false}},"[project]/src/providers/ToastProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/ToastProvider.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/src_lib_authApi_ts_b0530198._.js","server/chunks/ssr/node_modules_8ead631a._.js","server/chunks/ssr/[root of the server]__f4a6ca17._.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/src_lib_authApi_ts_b0530198._.js","server/chunks/ssr/node_modules_8ead631a._.js","server/chunks/ssr/[root of the server]__f4a6ca17._.js","server/chunks/ssr/[root of the server]__df46c8e2._.js","server/chunks/ssr/src_lib_abeaf831._.js","server/chunks/ssr/src_components_3d577ee8._.js","server/chunks/ssr/src_422d232e._.js","server/chunks/ssr/node_modules_next_01095d5f._.js","server/chunks/ssr/node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js","server/chunks/ssr/node_modules_@reactflow_core_dist_esm_index_mjs_7cdd66ff._.js","server/chunks/ssr/node_modules_zod_lib_index_mjs_f9af4762._.js","server/chunks/ssr/node_modules_@radix-ui_9ef0f7f7._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_4d04470c._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/providers/theme-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/theme-provider.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/providers/auth-provider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/providers/auth-provider.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/providers/QueryProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/QueryProvider.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/providers/ToastProvider.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/providers/ToastProvider.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root of the server]__8c478a37._.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/[root of the server]__8c478a37._.css","inlined":false},{"path":"static/chunks/node_modules_reactflow_dist_style_1392350c.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a61e115._.js"],"[project]/src/app/layout":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js"],"[project]/src/app/page":["static/chunks/src_lib_authApi_ts_3c2a9f63._.js","static/chunks/node_modules_82971598._.js","static/chunks/src_9e4fce59._.js","static/chunks/src_app_layout_tsx_b4714161._.js","static/chunks/[root of the server]__f8cce6bf._.js","static/chunks/src_lib_0525c592._.js","static/chunks/src_components_13b0e1cb._.js","static/chunks/src_c3d91abc._.js","static/chunks/node_modules_next_c367bc0b._.js","static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js","static/chunks/node_modules_@reactflow_core_dist_esm_index_mjs_fc9e5a1a._.js","static/chunks/node_modules_zod_lib_index_mjs_ee760afb._.js","static/chunks/node_modules_@radix-ui_0461657c._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_24910780._.js","static/chunks/src_app_page_tsx_1174d750._.js"]}}
