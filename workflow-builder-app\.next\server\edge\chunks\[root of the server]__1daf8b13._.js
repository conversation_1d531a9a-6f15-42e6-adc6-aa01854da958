(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root of the server]__1daf8b13._.js", {

"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/src/shared/routes.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Shared route definitions for the application
 * Matches the implementation in ruh-app-fe for consistency.
 */ // Authentication routes
__turbopack_context__.s({
    "aboutRoute": (()=>aboutRoute),
    "authRoute": (()=>authRoute),
    "contactRoute": (()=>contactRoute),
    "credentialsRoute": (()=>credentialsRoute),
    "homeRoute": (()=>homeRoute),
    "loginRoute": (()=>loginRoute),
    "protectedRoutes": (()=>protectedRoutes),
    "publicRoutes": (()=>publicRoutes),
    "settingsRoute": (()=>settingsRoute),
    "signupRoute": (()=>signupRoute),
    "updatePasswordRoute": (()=>updatePasswordRoute),
    "verifyEmailRoute": (()=>verifyEmailRoute),
    "workflowsRoute": (()=>workflowsRoute)
});
const loginRoute = "/login";
const signupRoute = "/signup";
const verifyEmailRoute = "/verify-email";
const updatePasswordRoute = "/reset-password";
const authRoute = `${("TURBOPACK compile-time value", "http://localhost:3001/")}?redirect_url=${("TURBOPACK compile-time value", "http://localhost:3000/")}`;
const homeRoute = "/workflows"; // Updated to point to workflows instead of /home
const workflowsRoute = "/workflows";
const settingsRoute = "/settings";
const credentialsRoute = "/credentials";
const aboutRoute = "/about";
const contactRoute = "/contact";
const publicRoutes = [
    loginRoute,
    signupRoute,
    verifyEmailRoute,
    updatePasswordRoute,
    aboutRoute,
    contactRoute
];
const protectedRoutes = [
    workflowsRoute,
    workflowsRoute,
    settingsRoute,
    credentialsRoute
];
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Next.js middleware for route protection
 * Matches the implementation in ruh-app-fe for consistency.
 */ __turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/shared/routes.ts [middleware-edge] (ecmascript)");
;
;
function middleware(request) {
    // TEMPORARY: Uncomment this line to bypass all auth checks for testing
    // return NextResponse.next();
    const path = request.nextUrl.pathname;
    // Check if the current path is public
    const isPublicRoute = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["publicRoutes"].some((route)=>// For exact matches (like login, signup)
        path === route || route.startsWith("/") && path.startsWith(route));
    // Check if the user has an auth token
    const hasAuthTokens = request.cookies.has("accessToken") || request.cookies.has("refreshToken");
    // If user is logged in and trying to access auth/public pages, redirect to workflows page
    if (isPublicRoute && hasAuthTokens) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["workflowsRoute"], request.url));
    }
    // If user is not logged in and trying to access any non-public route, redirect to login
    if (!isPublicRoute && !hasAuthTokens) {
        // Add the current path as a callback URL for post-login redirection
        const loginUrl = new URL(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$shared$2f$routes$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["loginRoute"], request.url);
        loginUrl.searchParams.set("callbackUrl", path);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__1daf8b13._.js.map