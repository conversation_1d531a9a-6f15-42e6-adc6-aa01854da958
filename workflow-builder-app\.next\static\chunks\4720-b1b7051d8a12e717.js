(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4477,4720],{13096:e=>{e.exports={style:{fontFamily:"'Sora', 'Sora Fallback'",fontStyle:"normal"},className:"__className_fb6e91",variable:"__variable_fb6e91"}},21716:(e,t,s)=>{"use strict";s.d(t,{E:()=>g});var i=s(52020),a=s(7165),r=s(45780),n=s(57948),o=class extends n.k{#e;#t;#s;#i;#a;#r;#n;constructor(e){super(),this.#n=!1,this.#r=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#i=e.client,this.#s=this.#i.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#e=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,s=void 0!==t,i=s?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#a?.promise}setOptions(e){this.options={...this.#r,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#s.remove(this)}setData(e,t){let s=(0,i.pl)(this.state.data,e,this.options);return this.#o({data:s,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),s}setState(e,t){this.#o({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#a?.promise;return this.#a?.cancel(e),t?t.then(i.lQ).catch(i.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(e=>!1!==(0,i.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===i.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,i.j3)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#a&&(this.#n?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#s.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#a)return this.#a.continueRetry(),this.#a.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let s=new AbortController,a=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#n=!0,s.signal)})},n={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#i,state:this.state,fetchFn:()=>{let e=(0,i.ZM)(this.options,t),s={client:this.#i,queryKey:this.queryKey,meta:this.meta};return(a(s),this.#n=!1,this.options.persister)?this.options.persister(e,s,this):e(s)}};a(n),this.options.behavior?.onFetch(n,this),this.#t=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#o({type:"fetch",meta:n.fetchOptions?.meta});let o=e=>{(0,r.wm)(e)&&e.silent||this.#o({type:"error",error:e}),(0,r.wm)(e)||(this.#s.config.onError?.(e,this),this.#s.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#a=(0,r.II)({initialPromise:t?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:e=>{if(void 0===e){o(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){o(e);return}this.#s.config.onSuccess?.(e,this),this.#s.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#a.start()}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var s,i;return{...t,...(s=t.data,i=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,r.v_)(i.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let a=e.error;if((0,r.wm)(a)&&a.revert&&this.#t)return{...this.#t,fetchStatus:"idle"};return{...t,error:a,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),a.jG.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#s.notify({query:this,type:"updated",action:e})})}},u=s(25910),l=class extends u.Q{constructor(e={}){super(),this.config=e,this.#u=new Map}#u;build(e,t,s){let a=t.queryKey,r=t.queryHash??(0,i.F$)(a,t),n=this.get(r);return n||(n=new o({client:e,queryKey:a,queryHash:r,options:e.defaultQueryOptions(t),state:s,defaultOptions:e.getQueryDefaults(a)}),this.add(n)),n}add(e){this.#u.has(e.queryHash)||(this.#u.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#u.get(e.queryHash);t&&(e.destroy(),t===e&&this.#u.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){a.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#u.get(e)}getAll(){return[...this.#u.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,i.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,i.MK)(e,t)):t}notify(e){a.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){a.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){a.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},h=s(34560),c=class extends u.Q{constructor(e={}){super(),this.config=e,this.#l=new Set,this.#h=new Map,this.#c=0}#l;#h;#c;build(e,t,s){let i=new h.s({mutationCache:this,mutationId:++this.#c,options:e.defaultMutationOptions(t),state:s});return this.add(i),i}add(e){this.#l.add(e);let t=d(e);if("string"==typeof t){let s=this.#h.get(t);s?s.push(e):this.#h.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#l.delete(e)){let t=d(e);if("string"==typeof t){let s=this.#h.get(t);if(s)if(s.length>1){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}else s[0]===e&&this.#h.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=d(e);if("string"!=typeof t)return!0;{let s=this.#h.get(t),i=s?.find(e=>"pending"===e.state.status);return!i||i===e}}runNext(e){let t=d(e);if("string"!=typeof t)return Promise.resolve();{let s=this.#h.get(t)?.find(t=>t!==e&&t.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){a.jG.batch(()=>{this.#l.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#l.clear(),this.#h.clear()})}getAll(){return Array.from(this.#l)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,i.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,i.nJ)(e,t))}notify(e){a.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return a.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(i.lQ))))}};function d(e){return e.options.scope?.id}var f=s(50920),y=s(21239);function p(e){return{onFetch:(t,s)=>{let a=t.options,r=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,h=async()=>{let s=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?s=!0:t.signal.addEventListener("abort",()=>{s=!0}),t.signal)})},c=(0,i.ZM)(t.options,t.fetchOptions),d=async(e,a,r)=>{if(s)return Promise.reject();if(null==a&&e.pages.length)return Promise.resolve(e);let n={client:t.client,queryKey:t.queryKey,pageParam:a,direction:r?"backward":"forward",meta:t.options.meta};h(n);let o=await c(n),{maxPages:u}=t.options,l=r?i.ZZ:i.y9;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,a,u)}};if(r&&n.length){let e="backward"===r,t={pages:n,pageParams:o},s=(e?function(e,{pages:t,pageParams:s}){return t.length>0?e.getPreviousPageParam?.(t[0],t,s[0],s):void 0}:m)(a,t);u=await d(t,s,e)}else{let t=e??n.length;do{let e=0===l?o[0]??a.initialPageParam:m(a,u);if(l>0&&null==e)break;u=await d(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s):t.fetchFn=h}}}function m(e,{pages:t,pageParams:s}){let i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}var g=class{#d;#f;#r;#y;#p;#m;#g;#v;constructor(e={}){this.#d=e.queryCache||new l,this.#f=e.mutationCache||new c,this.#r=e.defaultOptions||{},this.#y=new Map,this.#p=new Map,this.#m=0}mount(){this.#m++,1===this.#m&&(this.#g=f.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#d.onFocus())}),this.#v=y.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#d.onOnline())}))}unmount(){this.#m--,0===this.#m&&(this.#g?.(),this.#g=void 0,this.#v?.(),this.#v=void 0)}isFetching(e){return this.#d.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#f.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#d.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),s=this.#d.build(this,t),a=s.state.data;return void 0===a?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime((0,i.d2)(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(a))}getQueriesData(e){return this.#d.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,s){let a=this.defaultQueryOptions({queryKey:e}),r=this.#d.get(a.queryHash),n=r?.state.data,o=(0,i.Zw)(t,n);if(void 0!==o)return this.#d.build(this,a).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return a.jG.batch(()=>this.#d.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,s)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#d.get(t.queryHash)?.state}removeQueries(e){let t=this.#d;a.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let s=this.#d;return a.jG.batch(()=>(s.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let s={revert:!0,...t};return Promise.all(a.jG.batch(()=>this.#d.findAll(e).map(e=>e.cancel(s)))).then(i.lQ).catch(i.lQ)}invalidateQueries(e,t={}){return a.jG.batch(()=>(this.#d.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let s={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(a.jG.batch(()=>this.#d.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,s);return s.throwOnError||(t=t.catch(i.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let s=this.#d.build(this,t);return s.isStaleByTime((0,i.d2)(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i.lQ).catch(i.lQ)}fetchInfiniteQuery(e){return e.behavior=p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i.lQ).catch(i.lQ)}ensureInfiniteQueryData(e){return e.behavior=p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return y.t.isOnline()?this.#f.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#d}getMutationCache(){return this.#f}getDefaultOptions(){return this.#r}setDefaultOptions(e){this.#r=e}setQueryDefaults(e,t){this.#y.set((0,i.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#y.values()],s={};return t.forEach(t=>{(0,i.Cp)(e,t.queryKey)&&Object.assign(s,t.defaultOptions)}),s}setMutationDefaults(e,t){this.#p.set((0,i.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#p.values()],s={};return t.forEach(t=>{(0,i.Cp)(e,t.mutationKey)&&Object.assign(s,t.defaultOptions)}),s}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#r.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,i.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===i.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#r.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#d.clear(),this.#f.clear()}}},34477:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{callServer:function(){return i.callServer},createServerReference:function(){return r},findSourceMapURL:function(){return a.findSourceMapURL}});let i=s(53806),a=s(31818),r=s(34979).createServerReference},50116:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},51362:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,N:()=>h});var i=s(12115),a=(e,t,s,i,a,r,n,o)=>{let u=document.documentElement,l=["light","dark"];function h(t){var s;(Array.isArray(e)?e:[e]).forEach(e=>{let s="class"===e,i=s&&r?a.map(e=>r[e]||e):a;s?(u.classList.remove(...i),u.classList.add(r&&r[t]?r[t]:t)):u.setAttribute(e,t)}),s=t,o&&l.includes(s)&&(u.style.colorScheme=s)}if(i)h(i);else try{let e=localStorage.getItem(t)||s,i=n&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;h(i)}catch(e){}},r=["light","dark"],n="(prefers-color-scheme: dark)",o=i.createContext(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=i.useContext(o))?e:u},h=e=>i.useContext(o)?i.createElement(i.Fragment,null,e.children):i.createElement(d,{...e}),c=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:s=!1,enableSystem:a=!0,enableColorScheme:u=!0,storageKey:l="theme",themes:h=c,defaultTheme:d=a?"system":"light",attribute:g="data-theme",value:v,children:b,nonce:C,scriptProps:S}=e,[q,O]=i.useState(()=>y(l,d)),[w,Q]=i.useState(()=>"system"===q?m():q),F=v?Object.values(v):h,A=i.useCallback(e=>{let t=e;if(!t)return;"system"===e&&a&&(t=m());let i=v?v[t]:t,n=s?p(C):null,o=document.documentElement,l=e=>{"class"===e?(o.classList.remove(...F),i&&o.classList.add(i)):e.startsWith("data-")&&(i?o.setAttribute(e,i):o.removeAttribute(e))};if(Array.isArray(g)?g.forEach(l):l(g),u){let e=r.includes(d)?d:null,s=r.includes(t)?t:e;o.style.colorScheme=s}null==n||n()},[C]),P=i.useCallback(e=>{let t="function"==typeof e?e(q):e;O(t);try{localStorage.setItem(l,t)}catch(e){}},[q]),D=i.useCallback(e=>{Q(m(e)),"system"===q&&a&&!t&&A("system")},[q,t]);i.useEffect(()=>{let e=window.matchMedia(n);return e.addListener(D),D(e),()=>e.removeListener(D)},[D]),i.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?O(e.newValue):P(d))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[P]),i.useEffect(()=>{A(null!=t?t:q)},[t,q]);let M=i.useMemo(()=>({theme:q,setTheme:P,forcedTheme:t,resolvedTheme:"system"===q?w:q,themes:a?[...h,"system"]:h,systemTheme:a?w:void 0}),[q,P,t,w,a,h]);return i.createElement(o.Provider,{value:M},i.createElement(f,{forcedTheme:t,storageKey:l,attribute:g,enableSystem:a,enableColorScheme:u,defaultTheme:d,value:v,themes:h,nonce:C,scriptProps:S}),b)},f=i.memo(e=>{let{forcedTheme:t,storageKey:s,attribute:r,enableSystem:n,enableColorScheme:o,defaultTheme:u,value:l,themes:h,nonce:c,scriptProps:d}=e,f=JSON.stringify([r,s,u,t,h,l,n,o]).slice(1,-1);return i.createElement("script",{...d,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(a.toString(),")(").concat(f,")")}})}),y=(e,t)=>{let s;try{s=localStorage.getItem(e)||void 0}catch(e){}return s||t},p=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},m=e=>(e||(e=window.matchMedia(n)),e.matches?"dark":"light")},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},91223:e=>{e.exports={style:{fontFamily:"'Jost', 'Jost Fallback'",fontStyle:"normal"},className:"__className_3a3c2d",variable:"__variable_3a3c2d"}}}]);