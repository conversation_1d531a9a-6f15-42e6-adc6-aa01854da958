(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{2910:(e,t,n)=>{"use strict";n.r(t),n.d(t,{createNodeConfigSchema:()=>s,createSchemaForInput:()=>i,validateAllInputs:()=>r,validateInput:()=>a});var o=n(55594);function a(e,t){if(!e.required&&(null==t||""===t))return{isValid:!0,message:""};switch(e.input_type){case"string":var n=e,o=t;if("string"!=typeof o)return{isValid:!1,message:"Must be a string"};let a=n.min_length||0,r=n.max_length||Number.MAX_SAFE_INTEGER;if(o.length<a)return{isValid:!1,message:"Must be at least ".concat(a," characters")};if(o.length>r)return{isValid:!1,message:"Must be at most ".concat(r," characters")};if(n.pattern)try{if(!new RegExp(n.pattern).test(o))return{isValid:!1,message:n.pattern_error||"Invalid format"}}catch(e){console.error("Invalid regex pattern:",n.pattern)}return{isValid:!0,message:"Valid input"};case"int":case"float":case"number":var i=e,s=t;let l=Number(s);if(isNaN(l))return{isValid:!1,message:"Must be a number"};let c=void 0!==i.min_value?Number(i.min_value):Number.MIN_SAFE_INTEGER,d=void 0!==i.max_value?Number(i.max_value):Number.MAX_SAFE_INTEGER;return l<c?{isValid:!1,message:"Must be at least ".concat(c)}:l>d?{isValid:!1,message:"Must be at most ".concat(d)}:{isValid:!0,message:"Valid number"};case"list":case"array":var u=e,p=t;let m=p;if("string"==typeof p)try{m=JSON.parse(p)}catch(e){return{isValid:!1,message:"Invalid JSON format"}}if(!Array.isArray(m))return{isValid:!1,message:"Must be an array"};let f=u.min_items||0,g=u.max_items||Number.MAX_SAFE_INTEGER;return m.length<f?{isValid:!1,message:"Must have at least ".concat(f," items")}:m.length>g?{isValid:!1,message:"Must have at most ".concat(g," items")}:{isValid:!0,message:"Valid list"};case"dict":case"json":case"object":var h=e,x=t;let v=x;if("string"==typeof x)try{v=JSON.parse(x)}catch(e){return{isValid:!1,message:"Invalid JSON format"}}if("object"!=typeof v||null===v||Array.isArray(v))return{isValid:!1,message:"Must be an object"};for(let e of h.required_keys||[])if(!(e in v))return{isValid:!1,message:"Missing required key: ".concat(e)};return{isValid:!0,message:"Valid object"};default:return{isValid:!0,message:""}}}function r(e,t){let n={};return e.forEach(e=>{if(e.is_handle)return;let o=t[e.name];n[e.name]=a(e,o)}),n}function i(e){switch(e.input_type){case"string":let t=o.z.string();return t=e.required?t.min(1,{message:"This field is required"}):t.optional(),e.min_length&&(t=t.min(e.min_length,{message:"Must be at least ".concat(e.min_length," characters")})),e.max_length&&(t=t.max(e.max_length,{message:"Must be at most ".concat(e.max_length," characters")})),e.pattern&&(t=t.regex(new RegExp(e.pattern),{message:e.pattern_error||"Invalid format"})),t;case"int":let n=o.z.coerce.number().int();return e.required||(n=n.optional()),void 0!==e.min_value&&(n=n.min(Number(e.min_value),{message:"Must be at least ".concat(e.min_value)})),void 0!==e.max_value&&(n=n.max(Number(e.max_value),{message:"Must be at most ".concat(e.max_value)})),n;case"float":case"number":let a=o.z.coerce.number();return e.required||(a=a.optional()),void 0!==e.min_value&&(a=a.min(Number(e.min_value),{message:"Must be at least ".concat(e.min_value)})),void 0!==e.max_value&&(a=a.max(Number(e.max_value),{message:"Must be at most ".concat(e.max_value)})),a;case"bool":return e.required?o.z.boolean():o.z.boolean().optional();case"list":case"array":let r=o.z.array(o.z.any());return e.required||(r=r.optional()),void 0!==e.min_items&&(r=r.min(e.min_items,{message:"Must have at least ".concat(e.min_items," items")})),void 0!==e.max_items&&(r=r.max(e.max_items,{message:"Must have at most ".concat(e.max_items," items")})),r;case"dict":case"json":case"object":let i=o.z.record(o.z.any());return e.required||(i=i.optional()),e.required_keys&&e.required_keys.length>0&&(i=i.refine(t=>!!t&&e.required_keys.every(e=>e in t),{message:"Missing required keys: ".concat(e.required_keys.join(", "))})),i;default:return e.required?o.z.any():o.z.any().optional()}}function s(e){let t={};return e.forEach(e=>{e.is_handle||(t[e.name]=i(e))}),o.z.object(t)}},10979:(e,t,n)=>{Promise.resolve().then(n.bind(n,12415))},12415:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>nw});var o=n(95155);n(11687);var a=n(12115),r=n(35695),i=n(6874),s=n.n(i),l=n(23478),c=n(66474),d=n(59434);function u(e){let{...t}=e;return(0,o.jsx)(l.bL,{"data-slot":"accordion",...t})}function p(e){let{className:t,...n}=e;return(0,o.jsx)(l.q7,{"data-slot":"accordion-item",className:(0,d.cn)("",t),...n})}function m(e){let{className:t,children:n,...a}=e;return(0,o.jsx)(l.Y9,{className:"flex",children:(0,o.jsxs)(l.l9,{"data-slot":"accordion-trigger",className:(0,d.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",t),...a,children:[n,(0,o.jsx)(c.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function f(e){let{className:t,children:n,...a}=e;return(0,o.jsx)(l.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...a,children:(0,o.jsx)("div",{className:(0,d.cn)("pt-0 pb-4",t),children:n})})}let g=(0,a.forwardRef)((e,t)=>{let{className:n,children:a,...r}=e;return(0,o.jsx)("div",{ref:t,className:(0,d.cn)("custom-scrollbar relative overflow-auto",n),...r,children:a})});g.displayName="CustomScrollArea";var h=n(62523),x=n(74466);let v=(0,x.F)("focus:ring-ring inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:ring-2 focus:ring-offset-2 focus:outline-none",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/80 border-transparent shadow",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/80 border-transparent shadow",outline:"text-foreground",success:"border-transparent bg-green-500/20 text-green-700 shadow dark:text-green-300",warning:"border-transparent bg-[#3F72AF]/20 text-[#3F72AF] shadow dark:text-[#1DCD9F]",info:"border-transparent bg-blue-500/20 text-blue-700 shadow dark:text-blue-300"}},defaultVariants:{variant:"default"}});function w(e){let{className:t,variant:n,...a}=e;return(0,o.jsx)("div",{className:(0,d.cn)(v({variant:n}),t),...a})}var b=n(70306),y=n(54213),N=n(73314),j=n(76356),k=n(40081),S=n(57434),_=n(29621),E=n(49376),C=n(50589),I=n(5937),A=n(17051),D=n(47924),O=n(1936);let T=(e,t,n)=>{let o=JSON.stringify({nodeType:t,definition:n});e.dataTransfer.setData("application/reactflow",o),e.dataTransfer.effectAllowed="move"},F=e=>{switch(e.toLowerCase()){case"io":return b.A;case"data":return y.A;case"processing":return N.A;case"api":return j.A;case"control flow":return k.A;case"text":return S.A;case"code":return _.A;case"ai":return E.A;case"mcp":return C.A;case"marketplace":return I.A;default:return A.A}},R=e=>{switch(e.toLowerCase()){case"io":return"Input/Output";case"ai":return"AI/LLM";case"mcp":return"MCP Marketplace";default:return e}},W=a.memo(function(e){let{components:t,collapsed:n=!1,onToggleCollapse:r}=e;console.log("Sidebar component categories:",Object.keys(t)),t.MCP?console.log("MCP category exists with components:",Object.keys(t.MCP)):console.log("MCP category does not exist in components");let i=Object.keys(t).sort(),[s,l]=(0,a.useState)(""),[c,d]=(0,a.useState)(i.filter(e=>["io","ai","data","mcp"].includes(e.toLowerCase()))),x=(0,a.useCallback)(()=>{if(!s.trim())return t;let e={},n=s.toLowerCase();return Object.entries(t).forEach(t=>{let[o,a]=t,r=Object.values(a).filter(e=>e.display_name.toLowerCase().includes(n)||e.description.toLowerCase().includes(n));r.length>0&&(e[o]=Object.fromEntries(r.map(e=>[e.name,e])))}),e},[t,s]),v=e=>{d(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},b=(0,a.useMemo)(()=>x(),[x]),y=(0,a.useMemo)(()=>Object.keys(b).filter(e=>"io"!==e.toLowerCase()).sort((e,t)=>"ai"===e.toLowerCase()?-1:"ai"===t.toLowerCase()?1:"mcp"===e.toLowerCase()?-1:"mcp"===t.toLowerCase()?1:"data"===e.toLowerCase()?-1:"data"===t.toLowerCase()?1:e.localeCompare(t)),[b]);return(0,o.jsxs)("aside",{className:"bg-sidebar border-brand-stroke relative flex h-full shrink-0 flex-col overflow-hidden border-r shadow-md transition-all duration-300 ".concat(n?"w-16":"w-80"),children:[(0,o.jsx)("div",{className:"pointer-events-none absolute inset-0 z-0 bg-black/5 dark:bg-black/20"}),(0,o.jsxs)("div",{className:"relative z-10 flex-shrink-0 bg-[#FEFEFE] dark:bg-black ".concat(n?"p-3":"p-5"),children:[!n&&(0,o.jsxs)("div",{className:"relative flex items-center",children:[(0,o.jsxs)("div",{className:"relative flex-grow",children:[(0,o.jsx)(D.A,{className:"text-brand-secondary absolute top-3 left-3 h-5 w-5"}),(0,o.jsx)(h.p,{placeholder:"Search components...",value:s,onChange:e=>{let t=e.target.value;l(t),t.trim()&&d(Object.keys(x()))},className:"border-brand-stroke text-brand-primary-font placeholder:text-brand-secondary-font focus-visible:ring-brand-primary/30 dark:text-brand-white-text dark:placeholder:text-brand-secondary-font h-11 rounded-md bg-white/90 pl-10 text-sm dark:border-[#3F3F46] dark:bg-[#18181B]"})]}),(0,o.jsx)("button",{onClick:r,className:"text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary ml-2 flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]","aria-label":"Collapse sidebar",title:"Collapse sidebar (Alt+S)",children:(0,o.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M15 6L9 12L15 18",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),n&&(0,o.jsx)("div",{className:"flex items-center justify-center",children:(0,o.jsx)("button",{onClick:r,className:"text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]","aria-label":"Expand sidebar",title:"Expand sidebar (Alt+S)",children:(0,o.jsx)("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M3 12H21M3 6H21M3 18H21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})})]}),(0,o.jsx)(g,{className:"custom-scrollbar relative z-10 flex-grow bg-[#FEFEFE] dark:bg-black",children:n?(0,o.jsx)("div",{className:"flex flex-col items-center gap-3 px-1 py-6",children:y.map(e=>{let t=F(e);return(0,o.jsxs)("div",{className:"group mb-1 flex flex-col items-center",title:R(e),children:[(0,o.jsx)("button",{className:"flex h-10 w-10 items-center justify-center rounded-full shadow-sm ".concat("io"===e.toLowerCase()||"data"===e.toLowerCase()?"bg-brand-primary/10 text-brand-primary dark:bg-brand-primary/20 dark:text-brand-secondary":"bg-brand-card-hover text-brand-primary-font dark:bg-brand-card dark:text-brand-white-text"," transition-all hover:scale-110 hover:shadow-md"),onClick:()=>{r&&r(),setTimeout(()=>v(e),300)},"aria-label":"Open ".concat(R(e)," category"),children:(0,o.jsx)(t,{className:"h-5 w-5"})}),(0,o.jsx)("span",{className:"text-brand-secondary-font mt-1 text-[10px] font-medium opacity-80",children:R(e).substring(0,3)})]},e)})}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(u,{type:"multiple",className:"w-full space-y-3 px-5 py-4",value:c,children:y.map(e=>{let t=F(e);return(0,o.jsxs)(p,{value:e,className:"overflow-hidden rounded-lg bg-white shadow-md hover:bg-[#F9F9F9] dark:bg-[#1E1E1E] dark:hover:bg-[#212121]",children:[(0,o.jsx)(m,{className:"font-primary px-4 py-4 text-base font-semibold transition-all duration-200 hover:bg-[#F9F9F9] hover:no-underline dark:hover:bg-[#212121]",onClick:()=>v(e),children:(0,o.jsxs)("div",{className:"flex w-full items-center gap-2",children:[(0,o.jsx)(t,{className:"text-brand-primary dark:text-brand-secondary h-6 w-6"}),(0,o.jsx)("span",{className:"text-brand-primary-font dark:text-brand-white-text",children:R(e)})]})}),(0,o.jsx)(f,{className:"accordion-content-animation px-3 pb-4",children:(0,o.jsx)("div",{className:"max-h-[300px] space-y-4 overflow-y-auto pt-3 pr-2",children:Object.values(b[e]).sort((e,t)=>e.display_name.localeCompare(t.display_name)).map(e=>(0,o.jsxs)("div",{className:"group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]",onDragStart:t=>T(t,e.name,e),draggable:!0,children:[(0,o.jsx)("div",{className:"absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80",children:(0,o.jsx)(O.A,{className:"text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4"})}),(0,o.jsxs)("div",{className:"font-primary mb-2 flex items-center gap-2 font-medium",children:[(0,o.jsx)("span",{className:"text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80",children:e.display_name}),"MCPMarketplaceComponent"===e.type&&(0,o.jsx)(w,{className:"ml-1 bg-blue-500 text-[10px] dark:bg-blue-600",children:"MCP"})]}),(0,o.jsx)("div",{className:"mb-2 pt-2"}),(0,o.jsx)("p",{className:"font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight",children:e.description})]},e.name))})})]},e)})}),0===y.length&&(0,o.jsxs)("div",{className:"border-brand-stroke bg-brand-card-hover text-brand-secondary-font dark:border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font m-4 rounded-lg border p-6 text-center text-sm",children:[(0,o.jsx)(D.A,{className:"text-brand-primary/70 dark:text-brand-secondary/70 mx-auto mb-3 h-6 w-6"}),"No components match your search."]})]})})]})});var V=n(87489);function L(e){let{className:t,orientation:n="horizontal",decorative:a=!0,...r}=e;return(0,o.jsx)(V.b,{"data-slot":"separator-root",decorative:a,orientation:n,className:(0,d.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...r})}var P=n(66766),B=n(74211),M=n(92657),q=n(85690),U=n(51456);function J(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"error",o=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;return{code:e,message:t,severity:n,nodeId:o,fieldId:a}}var G=n(56092),z=n(58706);function H(e,t){let n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{validateConnectivity:!0,collectMissingFields:!0,validateFieldTypes:!0,validateCycles:!0},a=new Date().toISOString().replace("T"," ").substring(0,19);if(console.log("[".concat(a,"] [validateWorkflow] Starting workflow validation with ").concat((null==e?void 0:e.length)||0," nodes and ").concat((null==t?void 0:t.length)||0," edges")),!e||!Array.isArray(e))return console.error("[".concat(a,"] [validateWorkflow] Nodes array is invalid or undefined")),{isValid:!1,errors:[J(U.h.WORKFLOW_MISSING_NODES,"Workflow nodes array is invalid or undefined")],warnings:[],infos:[]};if(console.log("[".concat(a,"] [validateWorkflow] Nodes array:"),e.map(e=>{var t,n,o;return{id:e.id,type:e.type,dataType:null===(t=e.data)||void 0===t?void 0:t.type,originalType:null===(n=e.data)||void 0===n?void 0:n.originalType,label:null===(o=e.data)||void 0===o?void 0:o.label}})),!t||!Array.isArray(t))return console.error("[".concat(a,"] [validateWorkflow] Edges array is invalid or undefined")),{isValid:!1,errors:[J(U.h.WORKFLOW_MISSING_EDGES,"Workflow edges array is invalid or undefined")],warnings:[],infos:[]};if(0===e.length)return console.warn("[".concat(a,"] [validateWorkflow] Workflow is empty (no nodes)")),{isValid:!1,errors:[J(U.h.WORKFLOW_EMPTY,"Workflow is empty. Please add at least a Start node.")],warnings:[],infos:[]};console.log("[".concat(a,"] [validateWorkflow] Node types in workflow:"),e.map(e=>{var t,n,o,a,r;return{id:e.id,type:e.type,dataType:null===(t=e.data)||void 0===t?void 0:t.type,originalType:null===(n=e.data)||void 0===n?void 0:n.originalType,definitionName:null===(a=e.data)||void 0===a?void 0:null===(o=a.definition)||void 0===o?void 0:o.name,label:null===(r=e.data)||void 0===r?void 0:r.label}})),console.log("[".concat(a,"] [validateWorkflow] Step 1: Validating node structure"));let r=function(e){let t=[],n=[],o=[];if(!Array.isArray(e))return t.push(J(U.h.WORKFLOW_MISSING_NODES,'Workflow must contain a "nodes" array')),{isValid:!1,errors:t,warnings:n,infos:o};e.forEach((e,n)=>{let o=function(e,t){let n=[];return(e.id||n.push(J(U.h.NODE_MISSING_ID,"Node at index ".concat(t," is missing an ID"))),e.type||n.push(J(U.h.NODE_MISSING_TYPE,"Node ".concat(e.id||"at index ".concat(t)," is missing a type"),"error",e.id)),e.position?("object"!=typeof e.position||"number"!=typeof e.position.x||"number"!=typeof e.position.y)&&n.push(J(U.h.NODE_INVALID_POSITION,"Node ".concat(e.id||"at index ".concat(t)," has an invalid position"),"error",e.id)):n.push(J(U.h.NODE_MISSING_POSITION,"Node ".concat(e.id||"at index ".concat(t)," is missing a position"),"error",e.id)),e.data)?(e.data.type||n.push(J(U.h.NODE_MISSING_DATA_TYPE,"Node ".concat(e.id||"at index ".concat(t)," is missing data.type"),"error",e.id)),e.data.label||n.push(J(U.h.NODE_MISSING_DATA_LABEL,"Node ".concat(e.id||"at index ".concat(t)," is missing data.label"),"error",e.id)),e.data.definition||n.push(J(U.h.NODE_MISSING_DATA_DEFINITION,"Node ".concat(e.id||"at index ".concat(t)," is missing data.definition"),"error",e.id))):n.push(J(U.h.NODE_MISSING_DATA,"Node ".concat(e.id||"at index ".concat(t)," is missing data"),"error",e.id)),n}(e,n);t.push(...o)});let a=function(e){let t=[],n=new Set,o=new Set;return e.forEach(e=>{e.id&&(n.has(e.id)?o.add(e.id):n.add(e.id))}),o.forEach(e=>{t.push(J(U.h.NODE_DUPLICATE_ID,"Duplicate node ID: ".concat(e),"error",e))}),t}(e);return t.push(...a),{isValid:0===t.length,errors:t,warnings:n,infos:o}}(e);console.log("[".concat(a,"] [validateWorkflow] Step 2: Validating edge structure"));let i=function(e,t){let n=[],o=[],a=[];if(!Array.isArray(e))return n.push(J(U.h.WORKFLOW_MISSING_EDGES,'Workflow must contain an "edges" array')),{isValid:!1,errors:n,warnings:o,infos:a};e.forEach((e,o)=>{let a=function(e,t,n){let o=[];return e.id||o.push(J(U.h.EDGE_MISSING_ID,"Edge at index ".concat(n," is missing an ID"))),e.source||o.push(J(U.h.EDGE_MISSING_SOURCE,"Edge ".concat(e.id||"at index ".concat(n)," is missing a source"),"error",void 0,e.id)),e.target||o.push(J(U.h.EDGE_MISSING_TARGET,"Edge ".concat(e.id||"at index ".concat(n)," is missing a target"),"error",void 0,e.id)),e.source&&e.target&&(t.find(t=>t.id===e.source)||o.push(J(U.h.EDGE_SOURCE_NOT_FOUND,"Edge ".concat(e.id||"at index ".concat(n)," has a non-existent source node: ").concat(e.source),"error",void 0,e.id)),t.find(t=>t.id===e.target)||o.push(J(U.h.EDGE_TARGET_NOT_FOUND,"Edge ".concat(e.id||"at index ".concat(n)," has a non-existent target node: ").concat(e.target),"error",void 0,e.id)),e.source===e.target&&o.push(J(U.h.EDGE_SELF_REFERENCE,"Edge ".concat(e.id||"at index ".concat(n)," is self-referencing: ").concat(e.source," -> ").concat(e.target),"error",void 0,e.id))),o}(e,t,o);n.push(...a)});let r=function(e){let t=[],n=new Set,o=new Set;return e.forEach(e=>{e.id&&(n.has(e.id)?o.add(e.id):n.add(e.id))}),o.forEach(e=>{t.push(J(U.h.EDGE_DUPLICATE_ID,"Duplicate edge ID: ".concat(e),"error",void 0,e))}),t}(e);return n.push(...r),{isValid:0===n.length,errors:n,warnings:o,infos:a}}(t,e),s=[...r.errors,...i.errors],l=[...r.warnings,...i.warnings],c=[...r.infos,...i.infos];if(s.length>0)return console.warn("[".concat(a,"] [validateWorkflow] Found ").concat(s.length," structural errors, returning early")),{isValid:!1,errors:s,warnings:l,infos:c};console.log("[".concat(a,"] [validateWorkflow] Step 3: Validating StartNode presence"));let d=function(e){let t=[],n=[],o=[],a=new Date().toISOString().replace("T"," ").substring(0,19);if(console.log("[".concat(a,"] [validateStartNode] Starting StartNode validation with ").concat(e.length," nodes")),!e||!Array.isArray(e)||0===e.length)return console.warn("[".concat(a,"] [validateStartNode] Nodes array is empty or invalid")),t.push(J(U.h.WORKFLOW_EMPTY,"Workflow is empty or not properly initialized")),{isValid:!1,errors:t,warnings:n,infos:o};let r=(0,G.findStartNode)(e);if(!r){console.warn("[".concat(a,"] [validateStartNode] No StartNode found using standard detection methods"));let r=e.filter(e=>{var t,n,o;let a=((null===(t=e.data)||void 0===t?void 0:t.label)||"").toLowerCase(),r=((null===(n=e.data)||void 0===n?void 0:n.type)||"").toLowerCase(),i=((null===(o=e.data)||void 0===o?void 0:o.originalType)||"").toLowerCase();return a.includes("start")||r.includes("start")||i.includes("start")});if(r.length>0){var i;let e=r[0];return console.log("[".concat(a,"] [validateStartNode] Found potential StartNode using fallback detection: ").concat(e.id)),n.push(J(U.h.WORKFLOW_USING_FALLBACK_START_NODE,"Using ".concat((null===(i=e.data)||void 0===i?void 0:i.label)||e.id," as a fallback Start node"),"warning")),{isValid:!0,errors:t,warnings:n,infos:o,startNodeId:e.id}}return console.error("[".concat(a,"] [validateStartNode] No StartNode found, validation failed")),t.push(J(U.h.WORKFLOW_MISSING_START_NODE,"Workflow must have a Start node")),{isValid:!1,errors:t,warnings:n,infos:o}}return console.log("[".concat(a,"] [validateStartNode] StartNode validation successful, found node: ").concat(r.id)),{isValid:!0,errors:t,warnings:n,infos:o,startNodeId:r.id}}(e);if(s.push(...d.errors),l.push(...d.warnings),c.push(...d.infos),!d.isValid)return console.warn("[".concat(a,"] [validateWorkflow] No StartNode found, validation failed")),{isValid:!1,errors:s,warnings:l,infos:c};if(console.log("[".concat(a,"] [validateWorkflow] Found StartNode with ID: ").concat(d.startNodeId)),o.validateConnectivity){console.log("[".concat(a,"] [validateWorkflow] Step 4: Validating node connectivity from StartNode"));let o=function(e,t,n){let o=[],a=[],r=(0,G.getConnectedNodes)(e,t,n),i=e.filter(e=>!r.has(e.id));if(i.length>0){i.map(e=>e.id).join(", ");let e=i.map(e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.label)||e.id}).join(", ");o.push(J(U.h.WORKFLOW_DISCONNECTED_NODES,"The following nodes are not connected to the Start node and will not be saved or executed: ".concat(e),"warning")),a.push(J(U.h.WORKFLOW_INFO,"Only nodes connected to the Start node will be included in the workflow execution and saving.","info"))}return{isValid:!0,errors:[],warnings:o,infos:a,connectedNodes:r}}(e,t,d.startNodeId);s.push(...o.errors),l.push(...o.warnings),c.push(...o.infos),(n=o.connectedNodes)?console.log("[".concat(a,"] [validateWorkflow] Found ").concat(n.size," nodes connected to StartNode: ").concat(Array.from(n).join(", "))):console.warn("[".concat(a,"] [validateWorkflow] No connected nodes found from StartNode"))}else console.log("[".concat(a,"] [validateWorkflow] Skipping connectivity validation (disabled in options)"));if(o.validateCycles){console.log("[".concat(a,"] [validateWorkflow] Step 5: Detecting cycles in workflow"));let n=function(e,t){let n=[],o=(0,G.detectCycles)(e,t);return o.length>0&&n.push(J(U.h.WORKFLOW_CYCLE_DETECTED,"Workflow contains cycles involving the following nodes: ".concat(o.join(", ")),"warning")),{isValid:0===o.length,errors:[],warnings:n,infos:[]}}(e,t);s.push(...n.errors),l.push(...n.warnings),c.push(...n.infos),n.warnings.length>0?console.warn("[".concat(a,"] [validateWorkflow] Found cycles in workflow")):console.log("[".concat(a,"] [validateWorkflow] No cycles detected in workflow"))}else console.log("[".concat(a,"] [validateWorkflow] Skipping cycle detection (disabled in options)"));let u=[];o.collectMissingFields&&n?(console.log("[".concat(a,"] [validateWorkflow] Step 6: Collecting missing required fields from ").concat(n.size," connected nodes")),(u=(0,z.pt)(e,n,t)).length>0?(console.log("[".concat(a,"] [validateWorkflow] Found ").concat(u.length," missing required fields")),u.forEach((e,t)=>{console.log("[".concat(a,"] [validateWorkflow] Missing field ").concat(t+1,": Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType))})):console.log("[".concat(a,"] [validateWorkflow] No missing required fields found"))):console.log("[".concat(a,"] [validateWorkflow] Skipping missing fields collection (disabled in options or no connected nodes)"));let p=0===s.length;return console.log("[".concat(a,"] [validateWorkflow] Validation complete. Result: ").concat(p?"VALID":"INVALID")),console.log("[".concat(a,"] [validateWorkflow] Errors: ").concat(s.length,", Warnings: ").concat(l.length,", Missing Fields: ").concat(u.length)),{isValid:p,errors:s,warnings:l,infos:c,missingFields:u,startNodeId:d.startNodeId,connectedNodes:n}}let K={FRONTEND_VALIDATION:!0,BACKEND_VALIDATION:!1,HYBRID_VALIDATION:!1,VALIDATION_DEBUG:"true"===n(49509).env.NEXT_PUBLIC_VALIDATION_DEBUG,VALIDATE_ON_EDIT:!1,VALIDATE_ON_SAVE:!0,VALIDATE_ON_EXECUTE:!0};async function Y(e,t,n){return H(e,t,n)}async function X(e,t){return K.VALIDATE_ON_SAVE?Y(e,t,{validateConnectivity:!0,collectMissingFields:!1,validateFieldTypes:!0,validateCycles:!0}):{isValid:!0,errors:[],warnings:[],infos:[]}}async function $(e,t){return K.VALIDATE_ON_EXECUTE?Y(e,t,{validateConnectivity:!0,collectMissingFields:!0,validateFieldTypes:!0,validateCycles:!0}):{isValid:!0,errors:[],warnings:[],infos:[]}}async function Z(e,t){return K.VALIDATE_ON_EDIT?Y(e,t,{validateConnectivity:!1,collectMissingFields:!1,validateFieldTypes:!0,validateCycles:!1}):{isValid:!0,errors:[],warnings:[],infos:[]}}async function Q(e,t,o,a){try{let i=e.find(e=>"StartNode"===e.data.originalType);if(i?(console.log("Found StartNode before updating:",i),i.data.config||(i.data.config={}),i.data.config.collected_parameters||(i.data.config.collected_parameters={}),i.data.config.collected_parameters&&Object.keys(i.data.config.collected_parameters).forEach(e=>{let t=i.data.config.collected_parameters[e];void 0===t.required&&(console.log("Setting required=true for parameter ".concat(e," in StartNode")),t.required=!0)}),console.log("StartNode config after ensuring structure:",i.data.config)):console.warn("No StartNode found in the workflow, this may cause issues"),o){let n={};if(Object.entries(o).forEach(e=>{let[t,o]=e,[a,...r]=t.split("_"),i=r.join("_");n[a]||(n[a]={}),n[a][i]=o}),e=e.map(e=>{if(n[e.id]){console.log("Updating node ".concat(e.id," config with values:"),n[e.id]);let t=JSON.parse(JSON.stringify(e));return t.data.config||(t.data.config={}),Object.entries(n[e.id]).forEach(e=>{let[n,o]=e;t.data.config[n]=o}),console.log("Node ".concat(e.id," config after update:"),t.data.config),t}return e}),i){var r;let n={...(null===(r=i.data.config)||void 0===r?void 0:r.collected_parameters)||{}};Object.entries(o).forEach(o=>{let[a,r]=o;if(!a.includes("_"))return;let[s,...l]=a.split("_"),c=l.join("_"),d=e.find(e=>e.id===s);if(!d)return;let u=r;if("string"==typeof r&&(r.trim().startsWith("{")||r.trim().startsWith("[")))try{u=JSON.parse(r),console.log("Successfully parsed JSON for ".concat(c,":"),u)}catch(e){console.error("Failed to parse JSON for ".concat(c," field:"),e)}let p=t.some(e=>{let t=e.source===i.id,n=e.target===s,o=!1;return e.targetHandle&&(e.targetHandle===c?o=!0:e.targetHandle==="input_".concat(c)?o=!0:e.targetHandle.includes(c)&&(o=!0)),t&&n&&o});n[a]={node_id:s,node_name:d.data.label||"Unknown Node",input_name:c,value:u,connected_to_start:p}}),i.data.config={...i.data.config,collected_parameters:n},console.log("StartNode config after updating collected parameters:",i.data.config),window.startNodeCollectedParameters=i.data.config.collected_parameters,console.log("Updated global startNodeCollectedParameters:",window.startNodeCollectedParameters)}}let s=e,l=t;if(i){console.log("[WORKFLOW EXECUTE] Filtering out unconnected nodes before execution");let{getConnectedNodes:o}=await Promise.resolve().then(n.bind(n,56092)),a=o(e,t,i.id);console.log("[WORKFLOW EXECUTE] Found ".concat(a.size," nodes connected to StartNode: ").concat(Array.from(a).join(", ")));let r=e.length-a.size;if(r>0){console.log("[WORKFLOW EXECUTE] Found ".concat(r," disconnected nodes that will be excluded from execution")),s=e.filter(e=>a.has(e.id)),l=t.filter(e=>a.has(e.source)&&a.has(e.target)),console.log("[WORKFLOW EXECUTE] Filtered workflow contains ".concat(s.length," nodes and ").concat(l.length," edges"));try{let{toast:e}=await n.e(6671).then(n.bind(n,56671));e.warning("".concat(r," unconnected ").concat(1===r?"node has":"nodes have"," been excluded from execution."),{description:"Only nodes connected to the Start node are included in the workflow execution.",duration:5e3})}catch(e){console.error("Failed to show toast notification:",e)}}}console.log("Executing workflow with filtered nodes:",s),console.log("StartNode config before execution:",null==i?void 0:i.data.config),s.forEach(e=>{("ScriptGenerateNode"===e.data.originalType||e.id.includes("generate-script"))&&console.log("Script Generate Node ".concat(e.id," config:"),e.data.config)});let c=await fetch("".concat("https://app-dev.rapidinnovation.dev/api/v1","/execute"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({nodes:s,edges:l,workflow_id:a})});if(!c.ok)throw Error("Error executing workflow: ".concat(c.statusText));return await c.json()}catch(e){return console.error("Error executing workflow:",e),{success:!1,error:e instanceof Error?e.message:String(e)}}}var ee=n(65453);let et=(0,ee.v)()((e,t)=>({isValid:!0,errors:[],warnings:[],infos:[],missingFields:[],isValidating:!1,hasValidated:!1,validateWorkflow:(t,n,o)=>{e({isValidating:!0});let a=H(t,n,o);return e({isValid:a.isValid,errors:a.errors,warnings:a.warnings,infos:a.infos||[],missingFields:a.missingFields||[],startNodeId:a.startNodeId,connectedNodes:a.connectedNodes,isValidating:!1,hasValidated:!0}),a},validateWorkflowSmart:async(t,n,o)=>{e({isValidating:!0});let a=await Y(t,n,o);return e({isValid:a.isValid,errors:a.errors,warnings:a.warnings,infos:a.infos||[],missingFields:a.missingFields||[],startNodeId:a.startNodeId,connectedNodes:a.connectedNodes,isValidating:!1,hasValidated:!0}),a},validateBeforeSave:async(t,n)=>{e({isValidating:!0});let o=await X(t,n);return e({isValid:o.isValid,errors:o.errors,warnings:o.warnings,infos:o.infos||[],missingFields:o.missingFields||[],startNodeId:o.startNodeId,connectedNodes:o.connectedNodes,isValidating:!1,hasValidated:!0}),o},validateBeforeExecution:async(t,n)=>{e({isValidating:!0});let o=await $(t,n);return e({isValid:o.isValid,errors:o.errors,warnings:o.warnings,infos:o.infos||[],missingFields:o.missingFields||[],startNodeId:o.startNodeId,connectedNodes:o.connectedNodes,isValidating:!1,hasValidated:!0}),o},validateDuringEditing:async(t,n)=>{e({isValidating:!0});let o=await Z(t,n);return e({isValid:o.isValid,errors:o.errors,warnings:o.warnings,infos:o.infos||[],missingFields:o.missingFields||[],startNodeId:o.startNodeId,connectedNodes:o.connectedNodes,isValidating:!1,hasValidated:!0}),o},clearValidation:()=>{e({isValid:!0,errors:[],warnings:[],infos:[],missingFields:[],startNodeId:void 0,connectedNodes:void 0,hasValidated:!1})}}));var en=n(54165),eo=n(85057),ea=n(88539),er=n(66695),ei=n(30064);function es(e){let{className:t,...n}=e;return(0,o.jsx)(ei.bL,{"data-slot":"tabs",className:(0,d.cn)("",t),...n})}function el(e){let{className:t,...n}=e;return(0,o.jsx)(ei.B8,{"data-slot":"tabs-list",className:(0,d.cn)("bg-muted text-muted-foreground inline-flex h-9 items-center justify-center rounded-lg p-1 text-sm font-medium",t),...n})}function ec(e){let{className:t,...n}=e;return(0,o.jsx)(ei.l9,{"data-slot":"tabs-trigger",className:(0,d.cn)("focus-visible:ring-ring/50 data-[state=active]:bg-background data-[state=active]:text-foreground inline-flex items-center justify-center rounded-md px-3 py-1 text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow",t),...n})}function ed(e){let{className:t,...n}=e;return(0,o.jsx)(ei.UC,{"data-slot":"tabs-content",className:(0,d.cn)("focus-visible:ring-ring/50 mt-2 outline-none focus-visible:ring-2",t),...n})}var eu=n(41684),ep=n(85339),em=n(40646),ef=n(91788),eg=n(18979),eh=n(81284),ex=n(54861),ev=n(51154),ew=n(90232),eb=n(46786);let ey=(0,ee.v)()((0,eb.Zr)((e,t)=>({isDialogOpen:!1,setDialogOpen:t=>e(e=>({isDialogOpen:t,hasActiveExecution:!t&&(e.hasActiveExecution||e.isStreaming)})),missingFields:[],setMissingFields:t=>e({missingFields:t}),activeTab:"parameters",setActiveTab:t=>e({activeTab:t}),fieldValues:{},setFieldValues:t=>e({fieldValues:t}),updateFieldValue:(t,n)=>e(e=>({fieldValues:{...e.fieldValues,[t]:n}})),errors:{},setErrors:t=>e({errors:t}),isFormValid:!1,setIsFormValid:t=>e({isFormValid:t}),logs:[],setLogs:t=>e({logs:t}),addLog:t=>e(e=>({logs:[...e.logs,t]})),clearLogs:()=>e({logs:[]}),isExecuting:!1,setIsExecuting:t=>e({isExecuting:t}),correlationId:null,setCorrelationId:t=>e({correlationId:t}),isStreaming:!1,setIsStreaming:t=>e(e=>({isStreaming:t,hasActiveExecution:!e.isDialogOpen&&!!t||(!e.hasActiveExecution||!!t)&&e.hasActiveExecution})),hasActiveExecution:!1,setHasActiveExecution:t=>e({hasActiveExecution:t}),stopExecution:()=>{let n=t(),o=new Date().toISOString().substring(11,19),a=n.correlationId?" (Correlation ID: ".concat(n.correlationId,")"):"",r="[".concat(o,"] ⚠️ Workflow execution manually stopped by user").concat(a),i="[".concat(o,"] \uD83D\uDCE1 Stop request sent to backend server");e(e=>({logs:[...e.logs,r,i],isStreaming:!1,hasActiveExecution:!1}))},viewExecution:()=>{console.log("Reopening execution dialog and resetting field values"),e(e=>({isDialogOpen:!0,activeTab:"logs",hasActiveExecution:!1,fieldValues:{},errors:{},isFormValid:!1}))},processFieldValues:()=>{let e=t(),n={};return console.log("Processing field values from store:",e.fieldValues),e.missingFields.forEach(t=>{let o,a="".concat(t.nodeId,"_").concat(t.name);if(!1===t.isEmpty&&void 0!==t.currentValue?(o=t.currentValue,console.log("Using pre-configured value for field ".concat(a,":"),o)):(o=e.fieldValues[a],console.log("Using form value for field ".concat(a,":"),o)),console.log("Processing field ".concat(a," with value:"),o),("object"===t.inputType||"dict"===t.inputType||"json"===t.inputType||"array"===t.inputType||"list"===t.inputType)&&"string"==typeof o)try{o=JSON.parse(o),console.log("Successfully parsed JSON for field ".concat(a,":"),o)}catch(e){console.error("Failed to parse JSON for field ".concat(a,":"),e)}("number"===t.inputType||"int"===t.inputType||"float"===t.inputType)&&"string"==typeof o&&(o=Number(o),console.log("Converted number field ".concat(a," to:"),o)),n[t.nodeId]||(n[t.nodeId]={}),n[t.nodeId][t.name]=o}),console.log("Final processed values for execution:",n),n},resetState:()=>{console.log("Resetting execution store state - clearing all field values"),e({activeTab:"parameters",fieldValues:{},errors:{},isFormValid:!1,logs:[],isExecuting:!1,missingFields:[],correlationId:null,isStreaming:!1,hasActiveExecution:!1})}}),{name:"execution-store",partialize:e=>({logs:e.logs})}));var eN=n(45215);function ej(e){let{logs:t=[],showStreamingStatus:n=!0}=e,r=(0,a.useRef)(null),{isStreaming:i}=ey(),s=Array.isArray(t)?t:[],l=(()=>{for(let e=s.length-1;e>=0;e--){let t=s[e].toLowerCase(),n=s[e];if(t.includes("workflow_status")&&t.includes("failed"))return{status:"failed",message:"Execution failed"};if(t.includes("workflow_status")&&t.includes("completed"))return{status:"completed",message:"Execution completed successfully"};if(t.includes("workflow_status")&&t.includes("cancelled"))return{status:"cancelled",message:"Execution was cancelled"};else if(t.includes("workflow_status")&&t.includes("waiting_for_approval"))try{let e=JSON.parse(n);if(!0===e.approval_required&&"paused"===e.status)return console.log("Found valid approval request:",e),{status:"waiting_for_approval",message:"Waiting for approval",nodeId:e.node_id||void 0,nodeName:e.node_name||e.node_id||"Unknown Node"}}catch(e){console.error("Error parsing log data for approval:",e)}}return{status:"in_progress",message:"Execution in progress..."}})(),{correlationId:c}=ey();(0,a.useEffect)(()=>{r.current&&r.current.scrollIntoView({behavior:"smooth"})},[s]),(0,a.useEffect)(()=>{"waiting_for_approval"===l.status&&c&&l.nodeId&&(console.log("LogDisplay: Detected valid waiting_for_approval status, dispatching event"),l.nodeId&&"unknown"!==l.nodeId?((0,eN.dispatchApprovalNeededEvent)(c,l.nodeId,l.nodeName||l.nodeId),window._pendingApproval={correlationId:c,nodeId:l.nodeId,nodeName:l.nodeName||l.nodeId,timestamp:Date.now()},setTimeout(()=>{window.dispatchEvent(new CustomEvent("approval-ui-update"))},200),console.log("Dispatched approval event for:",{correlationId:c,nodeId:l.nodeId,nodeName:l.nodeName||l.nodeId})):console.log("Skipping approval event dispatch due to missing node ID"))},[l.status,c,l.nodeId,l.nodeName]);let d=e=>{let t="info",n=(0,o.jsx)(eh.A,{className:"h-4 w-4 flex-shrink-0 text-blue-500"}),a="text-blue-800 bg-blue-50 border-blue-100";return e.toLowerCase().includes("error")||e.toLowerCase().includes("exception")||e.toLowerCase().includes("failed")?(t="error",n=(0,o.jsx)(ex.A,{className:"h-4 w-4 flex-shrink-0 text-red-500"}),a="text-red-800 bg-red-50 border-red-100"):e.toLowerCase().includes("success")||e.toLowerCase().includes("completed")||e.toLowerCase().includes("finished")?(t="success",n=(0,o.jsx)(em.A,{className:"h-4 w-4 flex-shrink-0 text-green-500"}),a="text-green-800 bg-green-50 border-green-100"):(e.toLowerCase().includes("warning")||e.toLowerCase().includes("warn"))&&(t="warning",n=(0,o.jsx)(ep.A,{className:"h-4 w-4 flex-shrink-0 text-yellow-500"}),a="text-yellow-800 bg-yellow-50 border-yellow-100"),{type:t,icon:n,className:a}},u=s.reduce((e,t,n)=>(t.startsWith("⏸️ Workflow is waiting for approval")&&n>0&&s[n-1].startsWith("⏸️ Workflow is waiting for approval")||e.push(t),e),[]);return(0,o.jsx)(g,{className:"h-[300px] p-0",children:(0,o.jsxs)("div",{className:"space-y-1 p-4 font-mono text-xs",children:[0===u.length?(0,o.jsxs)("div",{className:"text-muted-foreground py-8 text-center",children:[(0,o.jsx)(eh.A,{className:"mx-auto mb-2 h-8 w-8 opacity-50"}),(0,o.jsx)("p",{children:"No logs available. Run the workflow to see execution logs."})]}):(0,o.jsxs)(o.Fragment,{children:[u.map((e,t)=>{let{icon:n,className:a}=d(e);return(0,o.jsxs)("div",{className:"flex items-start gap-2 rounded border p-2 ".concat(a),children:[n,(0,o.jsx)("span",{className:"break-all whitespace-pre-wrap",children:e})]},t)}),n&&i&&(0,o.jsxs)("div",{className:"flex items-center gap-2 rounded border border-blue-100 bg-blue-50 p-2 text-blue-800",children:[(0,o.jsx)(ev.A,{className:"h-4 w-4 animate-spin text-blue-500"}),(0,o.jsx)("span",{children:"Streaming logs in real-time..."})]}),n&&!i&&s.length>0&&"in_progress"!==l.status&&(0,o.jsxs)("div",{className:"flex items-center gap-2 rounded border p-2 ".concat("failed"===l.status?"border-red-100 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300":"completed"===l.status?"border-green-100 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300":"cancelled"===l.status?"border-yellow-100 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300":"waiting_for_approval"===l.status?"border-blue-100 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300":"border-green-100 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"),children:["failed"===l.status?(0,o.jsx)(ex.A,{className:"h-4 w-4 text-red-500"}):"completed"===l.status?(0,o.jsx)(em.A,{className:"h-4 w-4 text-green-500"}):"cancelled"===l.status?(0,o.jsx)(ep.A,{className:"h-4 w-4 text-yellow-500"}):"waiting_for_approval"===l.status?(0,o.jsx)(ew.A,{className:"h-4 w-4 text-blue-500"}):(0,o.jsx)(em.A,{className:"h-4 w-4 text-green-500"}),(0,o.jsx)("span",{children:l.message})]})]}),(0,o.jsx)("div",{ref:r})]})})}var ek=n(30285),eS=n(69527);function e_(e){let{correlationId:t,nodeId:n,nodeName:r,onApprovalSent:i}=e,[s,l]=a.useState(!1),[c,d]=a.useState(null);(0,a.useEffect)(()=>(console.log("ApprovalRequest component mounted with:",{correlationId:t,nodeId:n,nodeName:r}),window.dispatchEvent(new CustomEvent("approval-ui-update")),()=>{console.log("ApprovalRequest component unmounted")}),[t,n,r]);let u=async e=>{l(!0),d(null);try{let n=await (0,eS.sendApprovalDecision)(t,e);n.success?(console.log("Successfully sent ".concat(e," decision for correlation ID: ").concat(t)),i()):d(n.error||"Failed to send ".concat(e," decision"))}catch(e){d("Failed to send approval: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{l(!1)}};return(0,o.jsxs)("div",{className:"mb-4 rounded-md border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20",children:[(0,o.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,o.jsx)(ep.A,{className:"h-5 w-5 text-yellow-500"}),(0,o.jsx)("h3",{className:"font-medium",children:"Approval Required"})]}),(0,o.jsxs)("p",{className:"mb-3 text-sm",children:["Node ",(0,o.jsx)("span",{className:"font-medium",children:r})," requires your approval to continue execution.",n&&(0,o.jsxs)("span",{className:"block mt-1 text-xs text-gray-500",children:["Node ID: ",n]}),t&&(0,o.jsxs)("span",{className:"block text-xs text-gray-500",children:["Correlation ID: ",t]})]}),c&&(0,o.jsx)("div",{className:"mb-3 rounded border border-red-100 bg-red-50 p-2 text-sm text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300",children:c}),(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsxs)(ek.$,{onClick:()=>u("approve"),disabled:s,className:"bg-green-600 text-white hover:bg-green-700",children:[s?(0,o.jsx)(ev.A,{className:"mr-1 h-4 w-4 animate-spin"}):(0,o.jsx)(em.A,{className:"mr-1 h-4 w-4"}),"Approve"]}),(0,o.jsxs)(ek.$,{onClick:()=>u("reject"),disabled:s,variant:"outline",className:"border-red-300 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-300 dark:hover:bg-red-900/20",children:[s?(0,o.jsx)(ev.A,{className:"mr-1 h-4 w-4 animate-spin"}):(0,o.jsx)(ex.A,{className:"mr-1 h-4 w-4"}),"Reject"]})]})]})}var eE=n(89761),eC=n(49509);class eI{connect(){let e=new Date().toISOString();if(!this.correlationId){console.error("[".concat(e,"] SSEClient: Cannot connect without Correlation ID.")),this.options.onError&&this.options.onError(Error("[".concat(e,"] Cannot connect without Correlation ID")));return}if(this.isConnected()){console.log("[".concat(e,"] SSEClient: Connection attempt skipped. Already connected."));return}this.reset(),this.closeInternal(!1);try{console.log("[".concat(e,"] SSEClient: Connecting to ").concat(this.url," for correlation ID: ").concat(this.correlationId)),this.eventSource=new EventSource(this.url),console.log("[".concat(e,"] SSEClient: EventSource instance created. Waiting for connection to open...")),this.eventSource.onopen=()=>{let e=new Date().toISOString();console.log("[".concat(e,"] SSEClient: Connection opened successfully for ").concat(this.correlationId,".")),this.reconnectAttempts=0,this.options.onOpen&&this.options.onOpen()},this.eventSource.onmessage=e=>{let t=new Date().toISOString();try{let t=JSON.parse(e.data);this.checkAndHandleTerminalStatus(t,"generic message"),this.options.onMessage&&this.options.onMessage(e)}catch(n){console.error("[".concat(t,"] SSEClient: Error parsing generic message data:"),n,e.data)}},this.eventSource.onerror=e=>{var t;let n=new Date().toISOString();console.error("[".concat(n,"] SSEClient: Connection error occurred."),e);let o=null===(t=this.eventSource)||void 0===t?void 0:t.readyState,a=this.getReadyStateText(o);console.log("[".concat(n,"] SSEClient: EventSource readyState on error: ").concat(a));let r=this.explicitlyClosed,i=this.receivedServerErrorEvent,s=this.receivedTerminalStatus;if(this.closeInternal(!0),r){console.log("[".concat(n,"] SSEClient: Connection error occurred after explicit close. No reconnection attempt."));return}if(i){console.log("[".concat(n,"] SSEClient: Connection error occurred after receiving a server error event. No reconnection attempt.")),this.options.onClose&&this.options.onClose(!0);return}if(s){console.log("[".concat(n,"] SSEClient: Connection error occurred after receiving a terminal workflow status. No reconnection attempt.")),this.options.onClose&&this.options.onClose(!0);return}this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("[".concat(n,"] SSEClient: Attempting to reconnect (").concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,") in ").concat(this.reconnectDelay/1e3,"s...")),setTimeout(()=>this.connect(),this.reconnectDelay)):(console.error("[".concat(n,"] SSEClient: Max reconnect attempts (").concat(this.maxReconnectAttempts,") reached for ").concat(this.correlationId,". Giving up.")),this.options.onError&&this.options.onError(Error("[".concat(n,"] Failed to reconnect after ").concat(this.maxReconnectAttempts," attempts."))),this.options.onClose&&this.options.onClose(!0)),this.options.onError&&this.options.onError(Error("[".concat(n,"] SSE connection error (readyState: ").concat(a,")")))};let t=["error","fatal_error"];["connected","log","warning","error","info","success","complete","fatal_error"].forEach(e=>{this.eventSource&&this.eventSource.addEventListener(e,n=>{let o=new Date().toISOString();try{let a=n.data?JSON.parse(n.data):null;t.includes(e)&&(console.warn("[".concat(o,"] SSEClient: Received terminal server error event [").concat(e,"]. Flagging to prevent reconnect.")),this.receivedServerErrorEvent=!0),this.checkAndHandleTerminalStatus(a,"event [".concat(e,"]")),this.options.onCustomEvent&&this.options.onCustomEvent(e,a)}catch(t){console.error("[".concat(o,"] SSEClient: Error parsing event [").concat(e,"] data:"),t,n.data)}})})}catch(t){let e=new Date().toISOString();console.error("[".concat(e,"] SSEClient: Failed to create EventSource instance:"),t),this.eventSource=null,this.options.onError&&this.options.onError(t instanceof Error?t:Error("[".concat(e,"] Failed to initialize SSE connection")))}}checkAndHandleTerminalStatus(e,t){let o=new Date().toISOString();if(e&&"object"==typeof e&&e.hasOwnProperty("workflow_status")){let a=e.workflow_status;if((null==a?void 0:a.toLowerCase())==="waiting_for_approval"&&!0===e.approval_required&&"paused"===e.status){console.log("[".concat(o,"] SSEClient: Received valid approval request in ").concat(t,"."),e),e.node_id&&"unknown"!==e.node_id?Promise.resolve().then(n.bind(n,45215)).then(t=>{let{dispatchApprovalNeededEvent:n}=t;n(this.correlationId,e.node_id,e.node_name||e.node_id),window._pendingApproval={correlationId:this.correlationId,nodeId:e.node_id,nodeName:e.node_name||e.node_id,timestamp:Date.now()},setTimeout(()=>{console.log("[".concat(o,"] SSEClient: Dispatching direct approval-ui-update event")),window.dispatchEvent(new CustomEvent("approval-ui-update"))},500)}).catch(t=>{console.error("[".concat(o,"] SSEClient: Error importing approvalUtils:"),t);let n=Date.now(),a=new CustomEvent("workflow-approval-needed",{detail:{correlationId:this.correlationId,nodeId:e.node_id,nodeName:e.node_name||e.node_id,timestamp:n,force:!0}});window.dispatchEvent(a),window._pendingApproval={correlationId:this.correlationId,nodeId:e.node_id,nodeName:e.node_name||e.node_id,timestamp:n}}):console.log("[".concat(o,"] SSEClient: Skipping approval event due to missing node ID"));return}if((null==a?void 0:a.toLowerCase())==="waiting_for_approval"&&console.log("[".concat(o,"] SSEClient: Received waiting_for_approval status but it didn't meet criteria for approval UI:"),e),["completed","cancelled","failed"].includes(null==a?void 0:a.toLowerCase())){console.log("[".concat(o,'] SSEClient: Received terminal workflow status "').concat(a,'" in ').concat(t,". Flagging to prevent reconnect.")),this.receivedTerminalStatus=!0,console.log("[".concat(o,"] SSEClient: Workflow status: ").concat(a,", Node ID: ").concat(e.node_id||"unknown",", Result: ").concat(e.result||"none")),console.log("[".concat(o,'] SSEClient: Closing connection shortly due to terminal status "').concat(a,'".'));let n=new CustomEvent("workflow-terminal-status",{detail:{status:a,nodeId:e.node_id,result:e.result}});window.dispatchEvent(n),(null==a?void 0:a.toLowerCase())==="complete"?(console.log("[".concat(o,'] SSEClient: Immediately closing connection for "complete" status.')),this.close()):setTimeout(()=>this.close(),500)}}}close(){let e=new Date().toISOString();this.eventSource?(console.log("[".concat(e,"] SSEClient: Explicitly closing connection for ").concat(this.correlationId,"...")),this.explicitlyClosed=!0,this.reconnectAttempts=this.maxReconnectAttempts,this.closeInternal(!1)):console.log("[".concat(e,"] SSEClient: Close called, but no active connection."))}closeInternal(e){this.eventSource&&(this.eventSource.close(),this.eventSource=null,!e&&this.explicitlyClosed&&this.options.onClose&&this.options.onClose(!1))}isConnected(){return null!==this.eventSource&&this.eventSource.readyState===EventSource.OPEN&&!this.explicitlyClosed}getReadyState(){var e,t;return null!==(t=null===(e=this.eventSource)||void 0===e?void 0:e.readyState)&&void 0!==t?t:null}getReadyStateText(e){switch(e){case EventSource.CONNECTING:return"CONNECTING (0)";case EventSource.OPEN:return"OPEN (1)";case EventSource.CLOSED:return"CLOSED (2)";case null:return"NULL (Not Initialized)";case void 0:return"UNDEFINED (Error State)";default:return"UNKNOWN (".concat(e,")")}}reset(){console.log("SSEClient: Resetting internal state flags for ".concat(this.correlationId)),this.explicitlyClosed=!1,this.receivedServerErrorEvent=!1,this.receivedTerminalStatus=!1,this.reconnectAttempts=0}constructor(e,t={},n={}){this.eventSource=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=2e3,this.explicitlyClosed=!1,this.receivedServerErrorEvent=!1,this.receivedTerminalStatus=!1,this.correlationId=e,this.options=t;let o=n.baseUrl||eC.env.NEXT_PUBLIC_SSE_URL||eE.Sn.WORKFLOW_EXECUTION.STREAM;if(this.url="".concat(o,"/").concat(this.correlationId),this.config={...n,baseUrl:o},!this.correlationId&&(console.error("SSEClient: Correlation ID is required."),this.options.onError)){let e=new Date().toISOString();this.options.onError(Error("[".concat(e,"] Correlation ID is required")))}}}var eA=n(27735);function eD(e){let{onClose:t,onStopExecution:r}=e,{isDialogOpen:i,missingFields:s,activeTab:l,setActiveTab:c,fieldValues:d,setFieldValues:u,updateFieldValue:p,errors:m,setErrors:f,isFormValid:x,setIsFormValid:v,logs:b,addLog:y,isExecuting:N,setIsExecuting:j,isStreaming:k,setIsStreaming:E,correlationId:C,setCorrelationId:I}=ey(),A=(0,a.useRef)(null),D=(0,a.useRef)(null),O=(0,a.useCallback)((e,t)=>{let n=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(n,"] [validateForm] ========== VALIDATING FORM ==========")),console.log("[".concat(n,"] [validateForm] Total fields to validate: ").concat(s.length)),console.log("[".concat(n,"] [validateForm] Total values provided: ").concat(Object.keys(e).length)),console.log("[".concat(n,"] [validateForm] Total errors: ").concat(Object.keys(t).length));let o=!0,a=0,r=0;return s.forEach(i=>{let s="".concat(i.nodeId,"_").concat(i.name),l="boolean"===i.inputType?void 0!==e[s]:!!e[s],c=!!t[s],d=!1!==i.required;console.log("[".concat(n,"] [validateForm] Checking field: ").concat(s)),console.log("[".concat(n,"] [validateForm] - Field type: ").concat(i.inputType)),console.log("[".concat(n,"] [validateForm] - Is required: ").concat(d?"YES":"NO"," (required !== false: ").concat(!1!==i.required,")")),console.log("[".concat(n,"] [validateForm] - Has value: ").concat(l?"YES":"NO")),console.log("[".concat(n,"] [validateForm] - Current value: ").concat(JSON.stringify(e[s]))),console.log("[".concat(n,"] [validateForm] - Has error: ").concat(c?"YES":"NO")),c&&console.log("[".concat(n,"] [validateForm] - Error message: ").concat(t[s])),d&&a++;let u=!d||l&&!c;console.log("[".concat(n,"] [validateForm] - Field is valid: ").concat(u?"YES":"NO")),u?r++:o=!1}),console.log("[".concat(n,"] [validateForm] Required fields: ").concat(a,", Valid fields: ").concat(r)),console.log("[".concat(n,"] [validateForm] Form validation result: ").concat(o?"VALID":"INVALID")),v(o),o},[s,v]),T=(0,a.useRef)(!1);(0,a.useEffect)(()=>{var e;if(!i){T.current=!1;return}if(T.current)return;T.current=!0;let{clearLogs:t}=ey.getState();t();let n={},o={},a=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(a,"] [ExecutionDialog] ========== INITIALIZING EXECUTION DIALOG WITH FRESH VALUES ==========")),console.log("[".concat(a,"] [ExecutionDialog] Missing fields count: ").concat(s.length)),s.length>0&&(console.log("[".concat(a,"] [ExecutionDialog] Missing fields details:")),s.forEach((e,t)=>{console.log("[".concat(a,"] [ExecutionDialog]   ").concat(t+1,". Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType)),console.log("The field",e),e.displayName||(console.log("[".concat(a,"] [ExecutionDialog] WARNING: Field ").concat(t+1," is missing display_name, using name instead")),e.displayName=e.name||"Unnamed Field"),e.nodeId||(console.log("[".concat(a,"] [ExecutionDialog] WARNING: Field ").concat(t+1,' is missing node_name, using "Unknown Node" instead')),e.nodeName="Unknown Node")}));let r=new Set((null===(e=window.currentWorkflowNodes)||void 0===e?void 0:e.map(e=>e.id))||[]);console.log("[".concat(a,"] [ExecutionDialog] Current node IDs in workflow:"),Array.from(r));let l=0===r.size&&s.length>0;l&&(console.log("[".concat(a,"] [ExecutionDialog] Detected prebuilt workflow - currentNodeIds is empty but missingFields has ").concat(s.length," fields")),console.log("[".concat(a,"] [ExecutionDialog] Will not filter out fields based on node existence for prebuilt workflow")));let c=[...s].filter(e=>{var t,n,o,i;if(l){let t=!1!==e.required,n=!0===e.directly_connected_to_start,a=null===(o=window.currentWorkflowNodes)||void 0===o?void 0:o.find(t=>t.id===e.nodeId),r=(null==a?void 0:null===(i=a.data)||void 0===i?void 0:i.config)&&void 0!==a.data.config[e.name],s=!0===e.is_handle&&!0===e.is_connected;return(0,eA.J)("ExecutionDialog","".concat(e.nodeId,"_").concat(e.name),e.nodeName,e.name,t,e.required,n,r,r?a.data.config[e.name]:void 0,s),(0,eA.K)(t,n,r,s)}if(!r.has(e.nodeId))return console.log("[".concat(a,"] [ExecutionDialog] Filtering out field from deleted node: ").concat(e.nodeName," (").concat(e.nodeId,")")),!1;if(!e.connected_to_start)return console.log("[".concat(a,"] [ExecutionDialog] Filtering out field from node not connected to Start node: ").concat(e.nodeName," (").concat(e.nodeId,")")),!1;let s=!1!==e.required,c=!0===e.directly_connected_to_start,d=null===(t=window.currentWorkflowNodes)||void 0===t?void 0:t.find(t=>t.id===e.nodeId),u=(null==d?void 0:null===(n=d.data)||void 0===n?void 0:n.config)&&void 0!==d.data.config[e.name],p=!0===e.is_handle&&!0===e.is_connected;return(0,eA.J)("ExecutionDialog","".concat(e.nodeId,"_").concat(e.name),e.nodeName,e.name,s,e.required,c,u,u?d.data.config[e.name]:void 0,p),(0,eA.K)(s,c,u,p)});console.log("[".concat(a,"] [ExecutionDialog] Using ").concat(c.length," fields from validation"));let p={...d};if(console.log("[".concat(a,"] [ExecutionDialog] ========== PROCESSING FIELDS FOR INITIALIZATION ==========")),console.log("[".concat(a,"] [ExecutionDialog] Total fields to process: ").concat(c.length)),console.log("[".concat(a,"] [ExecutionDialog] Existing field values: ").concat(Object.keys(p).length)),c.forEach(e=>{let t="".concat(e.nodeId,"_").concat(e.name);if(console.log("[".concat(a,"] [ExecutionDialog] Processing field: ").concat(t)),console.log("[".concat(a,"] [ExecutionDialog] Field details - Node: ").concat(e.nodeName,", Name: ").concat(e.name,", Type: ").concat(e.inputType)),void 0!==p[t]&&console.log("[".concat(a,"] [ExecutionDialog] Not using existing value for ").concat(t," to ensure fresh input")),void 0!==e.currentValue)console.log("[".concat(a,"] [ExecutionDialog] Raw currentValue from field collection for ").concat(t,":"),e.currentValue),console.log("[".concat(a,"] [ExecutionDialog] currentValue type: ").concat(typeof e.currentValue)),console.log("[".concat(a,"] [ExecutionDialog] Using currentValue as-is for ").concat(t,":"),e.currentValue),n[t]=e.currentValue;else switch(console.log("[".concat(a,"] [ExecutionDialog] No currentValue found, using default values for field ").concat(t)),console.log("[".concat(a,"] [ExecutionDialog] Setting default value for field ").concat(e.name," (").concat(e.inputType,")")),e.inputType){case"string":case"text":n[t]="",console.log("[".concat(a,"] [ExecutionDialog] Set default empty string for string field ").concat(t));break;case"number":case"int":case"float":n[t]=0,console.log("[".concat(a,"] [ExecutionDialog] Set default 0 for numeric field ").concat(t));break;case"boolean":case"bool":n[t]=!1,console.log("[".concat(a,"] [ExecutionDialog] Set default false for boolean field ").concat(t));break;case"dropdown":e.options&&e.options.length>0?(n[t]=e.options[0],console.log("[".concat(a,'] [ExecutionDialog] Set default first option "').concat(e.options[0],'" for dropdown field ').concat(t))):(n[t]="",console.log("[".concat(a,"] [ExecutionDialog] Set default empty string for dropdown field ").concat(t," (no options available)")));break;case"object":case"dict":case"json":try{if("keywords"===e.name||"tool_arg_keywords"===e.name){let e={time:"",objective:"",audience:"",gender:"",tone:"",speakers:""};n[t]=e,console.log("[".concat(a,"] [ExecutionDialog] Set default keywords object for field ").concat(t,":"),e)}else n[t]={},console.log("[".concat(a,"] [ExecutionDialog] Set default empty object for object field ").concat(t))}catch(o){console.error("[".concat(a,"] [ExecutionDialog] Error initializing object field ").concat(e.name,":"),o),n[t]={},console.log("[".concat(a,"] [ExecutionDialog] Set default empty object after error for field ").concat(t))}break;case"array":case"list":n[t]=[],console.log("[".concat(a,"] [ExecutionDialog] Set default empty array for array field ").concat(t));break;case"credential":n[t]="",console.log("[".concat(a,"] [ExecutionDialog] Set default empty string for credential field ").concat(t));break;default:n[t]="",console.log("[".concat(a,"] [ExecutionDialog] Set default empty string for unknown type field ").concat(t," (type: ").concat(e.inputType,")"))}console.log("[".concat(a,"] [ExecutionDialog] Final value for ").concat(t,": ").concat(JSON.stringify(n[t]))),"boolean"===e.inputType||n[t]||console.log("[".concat(a,"] [ExecutionDialog] Field ").concat(t," will have initial error: This field is required")),"boolean"===e.inputType||n[t]||(o[t]="This field is required")}),Object.keys(n).length>0){console.log("[".concat(a,"] [ExecutionDialog] ========== UPDATING FORM STATE ==========")),console.log("[".concat(a,"] [ExecutionDialog] Setting ").concat(Object.keys(n).length," initial field values")),Object.entries(n).forEach(e=>{let[t,n]=e;console.log("[".concat(a,"] [ExecutionDialog] Setting field ").concat(t," = ").concat(JSON.stringify(n)))}),Object.keys(o).length>0?(console.log("[".concat(a,"] [ExecutionDialog] Setting ").concat(Object.keys(o).length," initial field errors")),Object.entries(o).forEach(e=>{let[t,n]=e;console.log("[".concat(a,"] [ExecutionDialog] Field error: ").concat(t," - ").concat(n))})):console.log("[".concat(a,"] [ExecutionDialog] No initial field errors"));let e={...p,...n},t={...{...m},...o};console.log("[".concat(a,"] [ExecutionDialog] Total field values after merge: ").concat(Object.keys(e).length)),console.log("[".concat(a,"] [ExecutionDialog] Total field errors after merge: ").concat(Object.keys(t).length)),u(e),f(t),console.log("[".concat(a,"] [ExecutionDialog] Validating form with merged values and errors"));let r=O(e,t);r?(console.log("[".concat(a,"] [ExecutionDialog] Initial form validation result: VALID, Run button should be enabled")),window.dispatchEvent(new CustomEvent("form-validation-complete",{detail:{isValid:r}}))):console.log("[".concat(a,"] [ExecutionDialog] Initial form validation result: INVALID, Run button should be disabled"))}else console.log("[".concat(a,"] [ExecutionDialog] No new field values to set"))},[i,s,O]),(0,a.useEffect)(()=>{A.current&&A.current.scrollIntoView({behavior:"smooth"})},[b]),(0,a.useEffect)(()=>()=>{D.current&&(console.log("Closing SSE connection on component unmount"),D.current.close(),D.current=null),V(null)},[]),(0,a.useEffect)(()=>{if(!i){let{clearLogs:e}=ey.getState();e()}},[i]);let[F,R]=(0,a.useState)(!1),[W,V]=(0,a.useState)(null),[L,P]=(0,a.useState)(null),[,B]=(0,a.useState)([]),M=(0,a.useCallback)(()=>{let e=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(e,"] [addDefaultDescriptions] Adding default descriptions to dialog content")),B([]),console.log("[".concat(e,"] [addDefaultDescriptions] In a real implementation, this would modify the node configuration to add descriptions")),y("✅ Added default descriptions to dialog content")},[y]);(0,a.useEffect)(()=>{let e=e=>{console.log("Received workflow-terminal-status event:",e.detail);let t="waiting_for_approval"===W&&F;if(j(!1),E(!1),t)v(!1),console.log("Workflow is in approval state, keeping approval UI visible");else{v(!1);let e={},t={};s.forEach(n=>{let o="".concat(n.nodeId,"_").concat(n.name);"boolean"===n.inputType||"bool"===n.inputType?e[o]=!1:"number"===n.inputType||"int"===n.inputType||"float"===n.inputType?e[o]=0:"object"===n.inputType||"dict"===n.inputType||"json"===n.inputType?e[o]="{}":"array"===n.inputType||"list"===n.inputType?e[o]="[]":e[o]="",!1!==n.required&&"boolean"!==n.inputType&&"bool"!==n.inputType&&(t[o]="This field is required")}),u(e),f(t),R(!1),V(null),L&&(0,eN.clearApprovalEvent)(L.correlationId,L.nodeId),P(null)}};return window.addEventListener("workflow-terminal-status",e),()=>{window.removeEventListener("workflow-terminal-status",e)}},[v,E,j,u,f,R,V,P,s,L,eN.clearApprovalEvent]),(0,a.useEffect)(()=>{let e=e=>{let t=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(t,"] [FormValidationComplete] Received form-validation-complete event:"),e.detail),e.detail&&e.detail.isValid&&(console.log("[".concat(t,"] [FormValidationComplete] Form is valid, forcing button state update")),v(!0))};return window.addEventListener("form-validation-complete",e),()=>{window.removeEventListener("form-validation-complete",e)}},[v]),(0,a.useEffect)(()=>{let e=0,t=t=>{console.log("Received workflow-approval-needed event:",t.detail),t.detail.approvalKey||t.detail.force||!t.detail.timestamp||t.detail.timestamp>e?(e=t.detail.timestamp||Date.now(),c("logs"),setTimeout(()=>{R(!0),V("waiting_for_approval"),P(t.detail),console.log("Setting approval state:",{approvalNeeded:!0,workflowStatus:"waiting_for_approval",approvalDetails:t.detail})},100),console.log("Processed approval event for node ".concat(t.detail.nodeName," (").concat(t.detail.nodeId,") with timestamp ").concat(e))):console.log("Ignoring older approval event with timestamp ".concat(t.detail.timestamp," (last processed: ").concat(e,")"))},n=()=>{console.log("Received approval-ui-update event"),window._pendingApproval&&(console.log("Processing pending approval from UI update event:",window._pendingApproval),c("logs"),R(!0),P({correlationId:window._pendingApproval.correlationId,nodeId:window._pendingApproval.nodeId,nodeName:window._pendingApproval.nodeName,timestamp:window._pendingApproval.timestamp}))};window.addEventListener("workflow-approval-needed",t),window.addEventListener("approval-ui-update",n);let o=setInterval(()=>{window._pendingApproval&&!F&&(console.log("Found pending approval that wasn't processed:",window._pendingApproval),R(!0),P({correlationId:window._pendingApproval.correlationId,nodeId:window._pendingApproval.nodeId,nodeName:window._pendingApproval.nodeName,timestamp:window._pendingApproval.timestamp}),c("logs"))},2e3);return()=>{window.removeEventListener("workflow-approval-needed",t),window.removeEventListener("approval-ui-update",n),clearInterval(o),(0,eN.clearAllApprovalEvents)()}},[c,F]),(0,a.useEffect)(()=>{if(!F&&b.length>0&&C){for(let e of b.slice(Math.max(0,b.length-10)))if(e.includes("workflow_status")&&e.includes("waiting_for_approval")&&e.includes("approval_required")&&e.includes("status")&&e.includes("paused")){console.log("Found potential approval request in logs, checking details");try{let t=JSON.parse(e);if("waiting_for_approval"===t.workflow_status&&!0===t.approval_required&&"paused"===t.status&&t.node_id){console.log("Found valid approval request in logs:",{correlationId:C,nodeId:t.node_id,nodeName:t.node_name||t.node_id}),R(!0),P({correlationId:C,nodeId:t.node_id,nodeName:t.node_name||t.node_id,timestamp:Date.now()}),c("logs"),window._pendingApproval={correlationId:C,nodeId:t.node_id,nodeName:t.node_name||t.node_id,timestamp:Date.now()},window._approvalEventHistory&&window._approvalEventHistory.push({correlationId:C,nodeId:t.node_id,nodeName:t.node_name||t.node_id,timestamp:Date.now(),status:"detected_in_logs"}),setTimeout(()=>{window.dispatchEvent(new CustomEvent("approval-ui-update"))},200);break}console.log("Log contains waiting_for_approval but doesn't meet criteria:",t)}catch(e){console.error("Error parsing log data:",e)}}}},[b,F,C,c]);let U=()=>{console.log("Approval sent, waiting for workflow to continue..."),window._approvalEventHistory&&L&&window._approvalEventHistory.push({correlationId:L.correlationId,nodeId:L.nodeId,nodeName:L.nodeName||L.nodeId,timestamp:Date.now(),status:"approval_sent"}),R(!1),V("running"),L&&((0,eN.clearApprovalEvent)(L.correlationId,L.nodeId),window._pendingApproval=void 0),P(null),console.log("Approval state cleared after sending approval")},J=(0,a.useCallback)((e,t,n)=>{let o=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(o,"] [handleFieldChange] ========== FIELD CHANGE DETECTED ==========")),console.log("[".concat(o,"] [handleFieldChange] Field: ").concat(e)),console.log("[".concat(o,"] [handleFieldChange] Type: ").concat(n)),console.log("[".concat(o,"] [handleFieldChange] New value: ").concat(JSON.stringify(t))),console.log("[".concat(o,"] [handleFieldChange] Previous value: ").concat(JSON.stringify(d[e])));let a=e.split("_"),r=a[0],i=a.slice(1).join("_");console.log("[".concat(o,"] [handleFieldChange] Field belongs to node ").concat(r,", field name: ").concat(i));let l=s.find(t=>"".concat(t.nodeId,"_").concat(t.name)===e),c=(null==l?void 0:l.required)!==!1;console.log("[".concat(o,"] [handleFieldChange] Field is required: ").concat(c?"YES":"NO"," (required !== false: ").concat((null==l?void 0:l.required)!==!1,")"));let u={...m};if(console.log("[".concat(o,"] [handleFieldChange] Current errors: ").concat(JSON.stringify(u[e]||"none"))),c&&"boolean"!==n&&!t)console.log("[".concat(o,"] [handleFieldChange] VALIDATION ERROR: Field is required but empty")),u[e]="This field is required";else if("object"===n||"dict"===n||"json"===n||"array"===n||"list"===n)if(console.log("[".concat(o,"] [handleFieldChange] Validating ").concat(n," field")),"string"==typeof t&&""!==t.trim())try{JSON.parse(t),console.log("[".concat(o,"] [handleFieldChange] JSON string validation passed")),delete u[e]}catch(t){console.log("[".concat(o,"] [handleFieldChange] VALIDATION ERROR: Invalid JSON format - ").concat(t.message)),u[e]="Invalid JSON format"}else if("object"==typeof t&&null!==t)try{JSON.stringify(t),console.log("[".concat(o,"] [handleFieldChange] Native object validation passed")),delete u[e]}catch(t){console.log("[".concat(o,"] [handleFieldChange] VALIDATION ERROR: Invalid object structure - ").concat(t.message)),u[e]="Invalid object structure"}else""===t||null==t||Array.isArray(t)&&0===t.length||"object"==typeof t&&0===Object.keys(t).length?(console.log("[".concat(o,"] [handleFieldChange] Empty ").concat(n," field is valid")),delete u[e]):(console.log("[".concat(o,"] [handleFieldChange] VALIDATION ERROR: Invalid ").concat(n," value type")),u[e]="Invalid ".concat(n," value"));else console.log("[".concat(o,"] [handleFieldChange] Field validation passed")),delete u[e];console.log("[".concat(o,"] [handleFieldChange] Updating field value in store")),p(e,t),u[e]?console.log("[".concat(o,"] [handleFieldChange] Field has error: ").concat(u[e])):console.log("[".concat(o,"] [handleFieldChange] Field is valid")),console.log("[".concat(o,"] [handleFieldChange] Updating errors in state")),f(u);let g={...d,[e]:t};console.log("[".concat(o,"] [handleFieldChange] Validating entire form with updated values"));let h=O(g,u);console.log("[".concat(o,"] [handleFieldChange] Form validation result: ").concat(h?"VALID":"INVALID")),h&&(console.log("[".concat(o,"] [handleFieldChange] Form is now valid, Run button should be enabled")),window.dispatchEvent(new CustomEvent("form-validation-complete",{detail:{isValid:h}})))},[s,m,d,p,f,O]),G=(0,a.useCallback)(e=>{let t=new Date().toISOString().replace("T"," ").substring(0,19);console.log("[".concat(t,"] [setupSSEConnection] Setting up SSE connection for correlation ID: ").concat(e)),D.current&&(console.log("[".concat(t,"] [setupSSEConnection] Closing existing SSE connection")),D.current.close(),D.current=null);let n=new eI(e,{onOpen:()=>{console.log("[".concat(t,"] [setupSSEConnection] SSE connection opened")),E(!0),V("running"),y("Connected to execution stream...")},onMessage:e=>{let n;console.log("[".concat(t,"] [setupSSEConnection] SSE message received:"),e);try{"string"==typeof e.data&&(n=JSON.parse(e.data))}catch(e){console.error("[".concat(t,"] [setupSSEConnection] Error parsing SSE message data:"),e)}if("connected"===e.type)y("Stream connected: ".concat(e.data));else if("log"===e.type)y(e.data);else if("warning"===e.type)y("⚠️ ".concat(e.data));else if("error"===e.type)y("❌ ".concat(e.data));else if("info"===e.type)y("ℹ️ ".concat(e.data));else if("success"===e.type)y("✅ ".concat(e.data));else if("completed"===e.type)y("✓ ".concat(e.data)),j(!1);else if("{}"===e.data||n&&0===Object.keys(n).length||y(e.data),n&&n.workflow_status){let e=n.workflow_status.toLowerCase();if(V(e),"failed"===e?y("❌ Workflow execution failed"):"completed"===e?y("✅ Workflow execution completed successfully"):"cancelled"===e&&y("⚠️ Workflow execution was cancelled"),"waiting_for_approval"===e&&!0===n.approval_required&&"paused"===n.status){let e=n.node_name||n.node_id||"Unknown";y("⏸️ Workflow is waiting for approval. Node: ".concat(e))}}},onError:e=>{console.error("[".concat(t,"] [setupSSEConnection] SSE connection error:"),e),E(!1),y("❌ Error in execution stream connection")},onClose:e=>{console.log("[".concat(t,"] [setupSSEConnection] SSE connection closed"),e?"due to an error":""),E(!1)}});D.current=n,n.connect(),console.log("[".concat(t,"] [setupSSEConnection] SSE connection setup complete"))},[y,E,j,V]),z=(0,a.useCallback)(async()=>{let e=new Date().toISOString().replace("T"," ").substring(0,19);if(console.log("[".concat(e,"] [handleSubmit] ========== FORM SUBMISSION INITIATED ==========")),console.log("[".concat(e,"] [handleSubmit] Form is valid: ").concat(x?"YES":"NO")),!x){console.log("[".concat(e,"] [handleSubmit] Submission blocked - form is not valid"));return}let{clearLogs:t}=ey.getState();t(),V("running");try{console.log("[".concat(e,"] [handleSubmit] Executing workflow with values from the store...")),console.log("[".concat(e,"] [handleSubmit] Total field values: ").concat(Object.keys(d).length)),Object.entries(d).forEach(t=>{let[n,o]=t;console.log("[".concat(e,"] [handleSubmit] Field value: ").concat(n," = ").concat(JSON.stringify(o)))}),console.log("[".concat(e,"] [handleSubmit] Switching to logs tab")),c("logs");let t=new URLSearchParams(window.location.search).get("workflow_id");if(!t){console.error("[".concat(e,"] [handleSubmit] No workflow_id found in URL parameters")),y("❌ Error: No workflow ID found. Cannot execute workflow.");return}console.log("[".concat(e,"] [handleSubmit] Using workflow_id from URL: ").concat(t)),y("Preparing to execute workflow with ID: ".concat(t));let n=[],o={};s.forEach(t=>{let a=d["".concat(t.nodeId,"_").concat(t.name)];if(void 0!==a){let r;n.push(t.name),console.log("[".concat(e,"] [handleSubmit] Processing field ").concat(t.name,", raw value:"),a),console.log("[".concat(e,"] [handleSubmit] Value type: ").concat(typeof a,", is object: ").concat("object"==typeof a)),"object"==typeof a&&null!==a&&"value"in a&&(console.log("[".concat(e,"] [handleSubmit] DETECTED WRAPPED VALUE - unwrapping:"),a),a=a.value,console.log("[".concat(e,"] [handleSubmit] Unwrapped value:"),a)),console.log("the processed value is printed here",a),r=a,console.log("[".concat(e,"] [handleSubmit] Using value as-is for field ").concat(t.name,":"),r),o[t.name]={value:r,transition_id:t.nodeId},console.log("[".concat(e,"] [handleSubmit] Final processed field ").concat(t.name,":"),o[t.name])}}),console.log("[".concat(e,"] [handleSubmit] Prepared user_dependent_fields:"),n),console.log("[".concat(e,"] [handleSubmit] Prepared user_payload_template:"),o),console.log("[".concat(e,"] [handleSubmit] Payload template structure analysis:"),Object.entries(o).map(e=>{let[t,n]=e;return{field:t,hasTransitionId:"object"==typeof n&&null!==n&&"transition_id"in n,hasValue:"object"==typeof n&&null!==n&&"value"in n,type:typeof n,structure:"object"==typeof n&&null!==n?Object.keys(n):"primitive"}}));let a={user_id:"123",workflow_id:t,approval:!0,payload:{user_dependent_fields:n,user_payload_template:o}};console.log("[".concat(e,"] [handleSubmit] Sending execution request with payload:"),a),y("Sending workflow execution request..."),j(!0);let r=await (0,eS.executeWorkflowWithUserInputs)(a);r.success?(console.log("[".concat(e,"] [handleSubmit] Execution request successful:"),r),y("✅ Workflow execution started successfully"),r.correlationId?(I(r.correlationId),y("Streaming logs with correlation ID: ".concat(r.correlationId)),console.log("[".concat(e,"] [handleSubmit] Setting up SSE connection directly")),G(r.correlationId)):(console.error("[".concat(e,"] [handleSubmit] No correlationId returned from execution API")),y("⚠️ No correlation ID returned. Cannot stream execution logs."),j(!1))):(console.error("[".concat(e,"] [handleSubmit] Execution request failed:"),r),y("❌ Error executing workflow: ".concat(r.message||"Unknown error")),j(!1))}catch(t){console.error("[".concat(e,"] [handleSubmit] ERROR executing workflow:"),t),y("❌ Error: ".concat(t instanceof Error?t.message:String(t))),j(!1)}},[x,d,s,c,y,I,j,V,G]),H=(0,a.useCallback)(()=>{if(!Array.isArray(b)||0===b.length){console.warn("No logs available to download");return}let e=new Blob([b.join("\n")],{type:"text/plain"}),t=URL.createObjectURL(e),n=document.createElement("a");n.href=t,n.download="workflow-execution-logs-".concat(new Date().toISOString().replace(/:/g,"-"),".txt"),document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(t)},[b]);return i?(0,o.jsx)(en.lG,{open:!0,onOpenChange:e=>{e||t()},children:(0,o.jsxs)(en.Cf,{className:"flex max-h-[90vh] max-w-3xl flex-col",children:[(0,o.jsx)(en.c7,{children:(0,o.jsxs)(en.L3,{className:"flex items-center gap-2",children:["Workflow Execution",k&&(0,o.jsx)(w,{variant:"outline",className:"ml-2 animate-pulse bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",children:"Execution in progress"})]})}),(0,o.jsx)("div",{className:"flex flex-grow flex-col overflow-hidden",children:(0,o.jsxs)(es,{value:l,onValueChange:c,className:"flex flex-grow flex-col overflow-hidden",children:[(0,o.jsxs)(el,{className:"grid w-full grid-cols-2",children:[(0,o.jsxs)(ec,{value:"parameters",className:"flex items-center gap-2",children:[(0,o.jsx)(S.A,{className:"h-4 w-4"}),"Required Parameters"]}),(0,o.jsxs)(ec,{value:"logs",className:"flex items-center gap-2",disabled:s.length>0&&!x,children:[(0,o.jsx)(eu.A,{className:"h-4 w-4"}),"Execution Logs",b.length>0&&(0,o.jsx)("span",{className:"bg-primary text-primary-foreground ml-1 flex h-5 w-5 items-center justify-center rounded-full text-xs",children:b.length})]})]}),(0,o.jsx)(ed,{value:"parameters",className:"mt-4 flex flex-grow flex-col overflow-hidden",children:s.length>0?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{className:"mb-4 flex items-start gap-2 rounded-md border border-blue-200 bg-blue-50 p-2",children:[(0,o.jsx)(ep.A,{className:"mt-0.5 h-5 w-5 flex-shrink-0 text-blue-500"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-blue-800",children:"Workflow Parameters"}),(0,o.jsx)("p",{className:"text-xs text-blue-700",children:"Please review and edit the following parameters before executing the workflow. All values are editable."})]})]}),(0,o.jsx)(g,{className:"mb-4 max-h-[50vh] flex-grow",children:(0,o.jsx)("div",{className:"space-y-4 p-2",children:s.map(e=>{let t="".concat(e.nodeId,"_").concat(e.name),n=!1!==e.required;return(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)(eo.J,{htmlFor:t,className:"flex items-center gap-2 text-sm font-medium",children:[e.nodeName||"Unknown Node",":"," ",e.displayName||e.name||"Unnamed Field",(0,o.jsx)(w,{variant:"outline",className:"text-xs",children:e.inputType}),n?(0,o.jsx)(w,{variant:"destructive",className:"text-xs",children:"Required"}):(0,o.jsx)(w,{variant:"outline",className:"text-xs",children:"Optional"}),e.directly_connected_to_start&&(0,o.jsx)(w,{variant:"secondary",className:"bg-blue-100 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-300",children:"Connected to Start"})]})}),e.info&&(0,o.jsx)("p",{className:"text-muted-foreground text-xs",children:e.info}),"boolean"===e.inputType?(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{id:t,type:"checkbox",checked:!0===d[t],onChange:n=>{let o=n.target.checked;console.log("Boolean field ".concat(t," changed to: ").concat(o)),J(t,o,e.inputType)},className:"text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"}),(0,o.jsx)(eo.J,{htmlFor:t,className:"text-sm",children:e.displayName})]}):"object"===e.inputType||"dict"===e.inputType||"json"===e.inputType?(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,o.jsx)(_.A,{className:"h-4 w-4 text-blue-500"}),(0,o.jsxs)("p",{className:"text-muted-foreground text-xs",children:["Enter JSON object (e.g., ",'{"key": "value", "number": 123}',")"]})]}),(0,o.jsx)(ea.T,{id:t,value:(()=>{let e=d[t],n=e;if("object"==typeof e&&null!==e&&"value"in e&&(n=e.value),"object"==typeof n&&null!==n)try{return JSON.stringify(n,null,2)}catch(e){return"{}"}return n||"{}"})(),onChange:n=>{let o=n.target.value;try{if(""===o.trim())J(t,{},e.inputType);else{let n=JSON.parse(o);J(t,n,e.inputType)}}catch(n){J(t,o,e.inputType)}},placeholder:"Enter JSON object...",className:"font-mono text-sm ".concat(m[t]?"border-red-500":""),rows:4})]}):"array"===e.inputType||"list"===e.inputType?(0,o.jsx)(ea.T,{id:t,value:d[t]||"",onChange:n=>J(t,n.target.value,e.inputType),placeholder:"Enter ".concat(e.displayName||e.name||"value","..."),className:"font-mono text-sm ".concat(m[t]?"border-red-500":""),rows:4}):"number"===e.inputType||"int"===e.inputType||"float"===e.inputType?(0,o.jsx)(h.p,{id:t,type:"number",value:d[t]||"",onChange:n=>J(t,n.target.value,e.inputType),placeholder:"Enter ".concat(e.displayName||e.name||"value","..."),className:m[t]?"border-red-500":""}):(0,o.jsx)(h.p,{id:t,value:d[t]||"",onChange:n=>J(t,n.target.value,e.inputType),placeholder:"Enter ".concat(e.displayName||e.name||"value","..."),className:m[t]?"border-red-500":""}),m[t]&&(0,o.jsx)("p",{className:"text-xs text-red-500",children:m[t]})]},t)})})}),(0,o.jsx)("div",{className:"mt-4 flex justify-end",children:(0,o.jsxs)("button",{type:"button",onClick:z,disabled:!x||N,className:"bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs disabled:pointer-events-none disabled:opacity-50 ".concat(!x||N?"cursor-not-allowed opacity-50":""),children:[(0,o.jsx)(q.A,{className:"h-4 w-4"}),"Run Workflow"]})})]}):(0,o.jsx)("div",{className:"flex flex-grow items-center justify-center",children:(0,o.jsxs)("div",{className:"p-6 text-center",children:[(0,o.jsx)(em.A,{className:"mx-auto mb-4 h-12 w-12 text-green-500"}),(0,o.jsx)("h3",{className:"mb-2 text-lg font-medium",children:"No Parameters Found"}),(0,o.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:"No workflow parameters were found. This is unusual - most workflows require parameters."}),(0,o.jsxs)("div",{className:"flex justify-center gap-3",children:[(0,o.jsxs)("button",{type:"button",onClick:()=>{t()},className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-1 rounded-md border px-4 py-2 text-sm font-medium shadow-xs",children:[(0,o.jsx)(S.A,{className:"mr-1 h-4 w-4"}),"Check Workflow"]}),(0,o.jsxs)("button",{type:"button",onClick:()=>{c("logs"),z()},className:"bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs",children:[(0,o.jsx)(q.A,{className:"mr-1 h-4 w-4"}),"Execute Anyway"]})]})]})})}),(0,o.jsxs)(ed,{value:"logs",className:"mt-4 flex flex-grow flex-col overflow-hidden",children:[(0,o.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,o.jsx)("h3",{className:"text-sm font-medium",children:"Execution Logs"}),b.length>0&&(0,o.jsxs)("button",{type:"button",onClick:H,className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-7 items-center justify-center gap-1 rounded-md border px-2 text-sm text-xs font-medium shadow-xs",children:[(0,o.jsx)(ef.A,{className:"mr-1 h-3 w-3"}),"Download Logs"]})]}),(0,o.jsx)(er.Zp,{className:"flex-grow overflow-hidden border",children:(0,o.jsxs)(er.Wu,{className:"p-0",children:[F&&L&&(0,o.jsx)("div",{className:"p-2",children:(0,o.jsx)(e_,{correlationId:L.correlationId,nodeId:L.nodeId,nodeName:L.nodeName,onApprovalSent:U})}),(0,o.jsx)(ej,{logs:b,showStreamingStatus:!0}),b.some(e=>e.includes("Missing 'Description'"))&&(0,o.jsx)("div",{className:"border-t p-2",children:(0,o.jsxs)("div",{className:"mb-2 flex items-start gap-2 rounded-md border border-yellow-200 bg-yellow-50 p-2",children:[(0,o.jsx)(ep.A,{className:"mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-500"}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("p",{className:"text-xs font-medium text-yellow-800",children:"Dialog Content Warnings"}),(0,o.jsx)("p",{className:"text-xs text-yellow-700",children:"Missing descriptions for dialog content. This won't prevent execution but should be fixed for better user experience."})]}),(0,o.jsx)("button",{type:"button",onClick:M,className:"rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800 hover:bg-yellow-200",children:"Add Default Descriptions"})]})}),C&&(0,o.jsxs)("div",{className:"text-muted-foreground border-t p-2 text-xs",children:[(0,o.jsx)("span",{className:"font-medium",children:"Correlation ID:"})," ",C]})]})}),(0,o.jsxs)("div",{className:"mt-4 flex justify-between",children:[s.length>0&&(0,o.jsxs)("button",{type:"button",onClick:()=>c("parameters"),className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-1 rounded-md border px-4 py-2 text-sm font-medium shadow-xs",children:[(0,o.jsx)(S.A,{className:"mr-1 h-4 w-4"}),"Back to Parameters"]}),(0,o.jsxs)("button",{type:"button",onClick:z,disabled:!x||k,className:"bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs disabled:pointer-events-none disabled:opacity-50 ".concat(!x||k?"cursor-not-allowed opacity-50":""," ").concat(0===s.length?"ml-auto":""),children:[(0,o.jsx)(q.A,{className:"h-4 w-4"}),k?"Executing...":"Run Again"]})]})]})]})}),(0,o.jsxs)(en.Es,{className:"mt-4 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[k&&r&&"waiting_for_approval"!==W&&(0,o.jsxs)("button",{type:"button",onClick:async()=>{let e=new Date().toISOString().substring(11,19);y("[".concat(e,"] \uD83D\uDED1 Sending request to stop workflow execution...")),r&&r()},className:"inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-red-700",title:"Stop the current workflow execution",children:[(0,o.jsx)(eg.A,{className:"h-4 w-4"}),"Stop Workflow Execution"]}),"waiting_for_approval"===W&&F&&L&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("button",{type:"button",onClick:()=>{Promise.resolve().then(n.bind(n,69527)).then(e=>{let{sendApprovalDecision:t}=e;t(L.correlationId,"approve").then(e=>{e.success?(U(),y("✅ Approval sent for node: ".concat(L.nodeName))):y("❌ Error sending approval: ".concat(e.error))})})},className:"inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-green-700",title:"Approve this workflow step",children:[(0,o.jsx)(em.A,{className:"h-4 w-4"}),"Approve"]}),(0,o.jsxs)("button",{type:"button",onClick:()=>{Promise.resolve().then(n.bind(n,69527)).then(e=>{let{sendApprovalDecision:t}=e;t(L.correlationId,"reject").then(e=>{e.success?(U(),y("❌ Rejection sent for node: ".concat(L.nodeName))):y("❌ Error sending rejection: ".concat(e.error))})})},className:"inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-red-700",title:"Reject this workflow step",children:[(0,o.jsx)(ep.A,{className:"h-4 w-4"}),"Reject"]})]})]}),(0,o.jsx)("button",{type:"button",onClick:t,className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-xs",title:"Close this dialog without stopping execution",children:"Close"})]})]})}):null}let eO=a.memo(function(e){let{nodes:t,edges:r,disabled:i=!1,onRun:s}=e,l=ey(),{isDialogOpen:c,setDialogOpen:u,setMissingFields:p,addLog:m,clearLogs:f,setIsExecuting:g,setActiveTab:h,processFieldValues:x,setCorrelationId:v,isStreaming:w,setIsStreaming:b,hasActiveExecution:y,setHasActiveExecution:N,stopExecution:j,viewExecution:k,resetState:S}=l,{getNodes:_,getEdges:E}=(0,B.VH)(),{validateCurrentWorkflowBeforeExecution:C}=function(){let{isValid:e,errors:t,warnings:n,infos:o,missingFields:r,hasValidated:i,isValidating:s,validateWorkflow:l,validateWorkflowSmart:c,validateBeforeSave:u,validateBeforeExecution:p,validateDuringEditing:m,clearValidation:f}=et(),{getNodes:g,getEdges:h}=(0,B.VH)(),x=(0,a.useCallback)(async e=>{let t=g(),n=h();return await c(t,n,e)},[g,h,c]),v=(0,a.useCallback)(async()=>{let e=g(),t=h();return await u(e,t)},[g,h,u]),w=(0,a.useCallback)(async(e,t)=>{let n=e||g(),o=t||h();return console.log("[useWorkflowValidation] Validating workflow with ".concat(n.length," nodes and ").concat(o.length," edges")),0===n.length&&console.warn("[useWorkflowValidation] WARNING: Empty nodes array for validation"),await p(n,o)},[g,h,p]),b=(0,a.useCallback)(async()=>{let e=g(),t=h();return await m(e,t)},[g,h,m]),y=(0,a.useCallback)((0,d.s)(async(e,t,n)=>{await m(e,t)},500),[m]),N=(0,a.useCallback)(e=>t.filter(t=>t.nodeId===e),[t]),j=(0,a.useCallback)((e,n)=>t.filter(t=>t.nodeId===e&&t.fieldId===n),[t]),k=(0,a.useCallback)(e=>t.some(t=>t.nodeId===e),[t]),S=(0,a.useCallback)((e,n)=>t.some(t=>t.nodeId===e&&t.fieldId===n),[t]),_=(0,a.useCallback)(e=>r.filter(t=>t.nodeId===e),[r]);return{isValid:e,errors:t,warnings:n,infos:o,missingFields:r,hasValidated:i,isValidating:s,validateWorkflow:l,validateWorkflowSmart:c,validateCurrentWorkflow:x,validateBeforeSave:u,validateBeforeExecution:p,validateDuringEditing:m,validateCurrentWorkflowBeforeSave:v,validateCurrentWorkflowBeforeExecution:w,validateCurrentWorkflowDuringEditing:b,debouncedValidate:y,clearValidation:f,getNodeErrors:N,getFieldErrors:j,hasNodeErrors:k,hasFieldErrors:S,getNodeMissingFields:_}}(),I=(0,a.useRef)(null);console.log("Validation of currentw workflow before execution",C),(0,a.useEffect)(()=>()=>{I.current&&(I.current.close(),I.current=null)},[]),(0,a.useEffect)(()=>{let e=e=>{var t,n;console.log("RunButton: Received workflow-terminal-status event:",e.detail),b(!1),(null===(n=e.detail)||void 0===n?void 0:null===(t=n.status)||void 0===t?void 0:t.toLowerCase())==="completed"&&I.current&&(console.log("RunButton: Forcing SSE client close for completed status"),I.current.close(),I.current=null)};return window.addEventListener("workflow-terminal-status",e),()=>{window.removeEventListener("workflow-terminal-status",e)}},[b]),(0,a.useEffect)(()=>{let e=e=>{console.log("RunButton: Received workflow-approval-needed event:",e.detail),m("⏸️ Workflow is waiting for approval. Node: ".concat(e.detail.nodeName||"Unknown")),c||(u(!0),h("logs"))};return window.addEventListener("workflow-approval-needed",e),()=>{window.removeEventListener("workflow-approval-needed",e)}},[m,c,u,h]);let A=(0,a.useCallback)(e=>{I.current&&(console.log("Closing existing SSE connection before setting up a new one"),I.current.close(),I.current=null),console.log("Setting up new SSE connection for correlation ID: ".concat(e));let t=new eI(e,{onOpen:()=>{console.log("SSE connection opened"),b(!0),m("Connected to execution stream...")},onMessage:e=>{let t;console.log("SSE message received:",e);try{"string"==typeof e.data&&(t=JSON.parse(e.data))}catch(e){console.error("Error parsing SSE message data:",e)}if("connected"===e.type)m("Stream connected: ".concat(e.data));else if("log"===e.type)m(e.data);else if("warning"===e.type)m("⚠️ ".concat(e.data));else if("error"===e.type)m("❌ ".concat(e.data));else if("info"===e.type)m("ℹ️ ".concat(e.data));else if("success"===e.type)m("✅ ".concat(e.data));else if("completed"===e.type)m("✓ ".concat(e.data)),g(!1);else if("{}"===e.data||t&&0===Object.keys(t).length||m(e.data),t&&t.workflow_status){let e=t.workflow_status.toLowerCase();"failed"===e?m("❌ Workflow execution failed"):"completed"===e?m("✅ Workflow execution completed successfully"):"cancelled"===e&&m("⚠️ Workflow execution was cancelled"),"waiting_for_approval"===e&&m("⏸️ Workflow is waiting for approval. Node: ".concat(t.node_name||t.node_id||"Unknown")),("failed"===e||"completed"===e||"cancelled"===e)&&console.log("Received terminal workflow status: ".concat(e,". Workflow execution is completed."))}},onError:e=>{console.error("SSE connection error:",e),b(!1),m("❌ Error in execution stream connection")},onClose:e=>{var t;console.log("SSE connection closed",e?"due to an error":""),b(!1),(null===(t=I.current)||void 0===t?void 0:t.getReadyState())===EventSource.CLOSED&&console.log("SSE connection is fully closed, enabling Run Again button")}});I.current=t,t.connect()},[m,g,b]);(0,a.useCallback)(async()=>{g(!0),m("Executing workflow with provided values...");try{let e=x();console.log("Sending execution request with field values:",e),m("Processing ".concat(Object.keys(e).length," node configurations for execution")),Object.entries(e).forEach(e=>{let[t,n]=e;console.log("Node ".concat(t," values:"),n),m("Node ".concat(t," configured with ").concat(Object.keys(n).length," parameters"))});let n=new URLSearchParams(window.location.search).get("workflow_id");console.log("Using workflow_id from URL:",n),window.startNodeCollectedParameters||(window.startNodeCollectedParameters={}),console.log("Storing field values in StartNode collected parameters"),Object.keys(e).forEach(n=>{var o,a;let[r,...i]=n.split("_"),s=i.join("_"),l=t.find(e=>e.id===r),c=l&&l.data.label||"Unknown Node",d=null==l?void 0:null===(o=l.data)||void 0===o?void 0:o.definition,u=null==d?void 0:null===(a=d.inputs)||void 0===a?void 0:a.find(e=>e.name===s),p=!u||!1!==u.required;(window.startNodeCollectedParameters||{})[n]={node_id:r,node_name:c,input_name:s,value:e[n],connected_to_start:!1,required:p,input_type:(null==u?void 0:u.input_type)||typeof e[n]}}),console.log("Updated startNodeCollectedParameters:",window.startNodeCollectedParameters);let o=await Q(t,r,e,n||void 0);o.success?o.correlation_id?(v(o.correlation_id),A(o.correlation_id),m("Workflow execution initiated. Streaming logs with correlation ID: ".concat(o.correlation_id))):(m("Workflow executed successfully."),g(!1),o.results&&m("Results: ".concat(JSON.stringify(o.results,null,2)))):(m("Execution failed: ".concat(o.error||"Unknown error")),g(!1))}catch(e){m("Error executing workflow: ".concat(e instanceof Error?e.message:String(e))),g(!1)}},[t,r,m,g,x,v,A]);let D=(0,a.useCallback)(async()=>{f(),m("Starting workflow validation..."),g(!1),I.current&&(console.log("Closing existing SSE connection before starting new workflow run"),I.current.close(),I.current=null),v(null),b(!1),(0,eN.clearAllApprovalEvents)(),p([]),window.startNodeCollectedParameters&&(window.startNodeCollectedParametersNeedsRefresh=!0,console.log("Marked startNodeCollectedParameters for refresh"));try{let e=_(),n=E(),o=()=>new Date().toISOString().replace("T"," ").substring(0,19),a=o();console.log("[".concat(a,"] [RunButton] ========== REACT FLOW STATE DEBUGGING ==========")),console.log("[".concat(a,"] [RunButton] React Flow nodes count: ").concat(e.length)),console.log("[".concat(a,"] [RunButton] React Flow edges count: ").concat(n.length)),console.log("[".concat(a,"] [RunButton] Props nodes count: ").concat(t.length)),console.log("[".concat(a,"] [RunButton] Props edges count: ").concat(r.length)),0===e.length&&t.length>0?(console.warn("[".concat(a,"] [RunButton] WARNING: React Flow state is empty but props have nodes. Using props nodes instead.")),e=t,n=r,console.log("[".concat(a,"] [RunButton] Using props nodes (").concat(e.length,") and edges (").concat(n.length,")"))):0===e.length&&(console.warn("[".concat(a,"] [RunButton] WARNING: No nodes found in React Flow state or props. This may indicate a synchronization issue.")),m("Warning: No nodes found in the workflow. The workflow might not be fully initialized.")),console.log("[".concat(a,"] [RunButton] Detailed node information:")),e.forEach((e,t)=>{var n,o,r,i,s,l,c;console.log("[".concat(a,"] [RunButton] Node ").concat(t+1,":"),{id:e.id,type:e.type,position:e.position,dataType:null===(n=e.data)||void 0===n?void 0:n.type,originalType:null===(o=e.data)||void 0===o?void 0:o.originalType,definitionName:null===(i=e.data)||void 0===i?void 0:null===(r=i.definition)||void 0===r?void 0:r.name,label:null===(s=e.data)||void 0===s?void 0:s.label,hasConfig:!!(null===(l=e.data)||void 0===l?void 0:l.config),configKeys:(null===(c=e.data)||void 0===c?void 0:c.config)?Object.keys(e.data.config):[]})}),console.log("[".concat(a,"] [RunButton] ========== SEARCHING FOR START NODE =========="));let i=e.find(e=>e.data&&"StartNode"===e.data.originalType),s=e.find(e=>e.data&&e.data.definition&&"StartNode"===e.data.definition.name),c=e.find(e=>"StartNode"===e.type||e.data&&"StartNode"===e.data.type),d=e.find(e=>e.data&&("Start"===e.data.label||"StartNode"===e.data.label)),f=e.find(e=>e.data&&"component"===e.data.type&&e.data.definition&&e.data.definition.name&&e.data.definition.name.toLowerCase().includes("start"));console.log("[".concat(a,"] [RunButton] Start node detection results:")),console.log("[".concat(a,"] [RunButton] - By originalType: ").concat(i?i.id:"Not found")),console.log("[".concat(a,"] [RunButton] - By definition name: ").concat(s?s.id:"Not found")),console.log("[".concat(a,"] [RunButton] - By node type: ").concat(c?c.id:"Not found")),console.log("[".concat(a,"] [RunButton] - By label: ").concat(d?d.id:"Not found")),console.log("[".concat(a,"] [RunButton] - By pattern: ").concat(f?f.id:"Not found"));let g=i||s||c||d||f;if(g){console.log("[".concat(a,"] [RunButton] Found StartNode with ID: ").concat(g.id));let e=!0===window.startNodeCollectedParametersNeedsRefresh;g.data&&g.data.config&&g.data.config.collected_parameters?(console.log("[".concat(a,"] [RunButton] StartNode has collected parameters:"),g.data.config.collected_parameters),e&&(console.log("[".concat(a,"] [RunButton] Refreshing StartNode collected parameters due to refresh flag")),window.startNodeCollectedParametersNeedsRefresh=!1),window.startNodeCollectedParameters=g.data.config.collected_parameters,console.log("[".concat(a,"] [RunButton] Stored StartNode collected parameters in window object:"),window.startNodeCollectedParameters)):(console.log("[".concat(a,"] [RunButton] StartNode found but has no collected parameters")),window.startNodeCollectedParameters={},e&&(window.startNodeCollectedParametersNeedsRefresh=!1,console.log("[".concat(a,"] [RunButton] Cleared refresh flag"))))}else console.warn("[".concat(a,"] [RunButton] No StartNode found using any detection method")),window.startNodeCollectedParameters={};a=o(),console.log("[".concat(a,"] [RunButton] ========== STARTING WORKFLOW VALIDATION ==========")),console.log("[".concat(a,"] [RunButton] Total nodes: ").concat(e.length)),console.log("[".concat(a,"] [RunButton] Node types: ").concat(e.map(e=>{var t;return(null===(t=e.data)||void 0===t?void 0:t.type)||"undefined"}).join(", "))),console.log("[".concat(a,"] [RunButton] Node IDs: ").concat(e.map(e=>e.id).join(", "))),a=o(),console.log("[".concat(a,"] [RunButton] Calling validateCurrentWorkflowBeforeExecution() with ").concat(e.length," nodes and ").concat(n.length," edges"));let x=await C(e,n);if(a=o(),console.log("[".concat(a,"] [RunButton] ========== VALIDATION RESULT ==========")),console.log("[".concat(a,"] [RunButton] Is valid: ").concat(x.isValid?"YES":"NO")),console.log("[".concat(a,"] [RunButton] Errors: ").concat(x.errors.length)),console.log("[".concat(a,"] [RunButton] Warnings: ").concat(x.warnings.length)),console.log("[".concat(a,"] [RunButton] Start node ID: ").concat(x.startNodeId||"NONE")),console.log("[".concat(a,"] [RunButton] Connected nodes: ").concat(x.connectedNodes?Array.from(x.connectedNodes).join(", "):"NONE")),console.log("[".concat(a,"] [RunButton] Missing fields: ").concat(x.missingFields?x.missingFields.length:0)),!x.startNodeId&&e.length>0){a=o(),console.warn("[".concat(a,"] [RunButton] No Start node found in validation result, but workflow has ").concat(e.length," nodes. Using fallback mechanism."));let t=new Set(e.map(e=>e.id));console.log("[".concat(a,"] [RunButton] Created fallback connected nodes set with ").concat(t.size," nodes")),console.log("[".concat(a,"] [RunButton] Collecting missing fields using fallback method..."));let n=(0,z.pt)(e,t);n.length>0?(console.log("[".concat(a,"] [RunButton] Found ").concat(n.length," missing fields using fallback method")),console.log("[".concat(a,"] [RunButton] Storing ").concat(n.length," missing fields from fallback in execution store")),p(n),m("Warning: No Start node found. Using fallback validation with ".concat(n.length," missing fields."))):(console.log("[".concat(a,"] [RunButton] No missing fields found using fallback method")),p([]))}x.errors.length>0&&(console.log("[".concat(a,"] [RunButton] Detailed validation errors:")),x.errors.forEach((e,t)=>{console.log("[".concat(a,"] [RunButton]   ").concat(t+1,". ").concat(e.code,": ").concat(e.message," (Node: ").concat(e.nodeId||"N/A",", Field: ").concat(e.fieldId||"N/A",")"))})),x.missingFields&&x.missingFields.length>0&&(console.log("[".concat(a,"] [RunButton] Detailed missing fields:")),x.missingFields.forEach((e,t)=>{console.log("[".concat(a,"] [RunButton]   ").concat(t+1,". Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType))})),console.log("[".concat(a,"] [RunButton] Performing direct validation to check for missing fields"));let v=x.startNodeId,w=x.connectedNodes||new Set;if(v&&w.size>0){console.log("[".concat(a,"] [RunButton] ========== STARTING DIRECT VALIDATION ==========")),console.log("[".concat(a,"] [RunButton] Start node: ").concat(v)),console.log("[".concat(a,"] [RunButton] Connected nodes (").concat(w.size,"): ").concat(Array.from(w).join(", "))),e.forEach(e=>{w.has(e.id)&&(console.log("[".concat(a,"] [RunButton] Connected node details - ID: ").concat(e.id,", Label: ").concat(e.data.label||"Unnamed",", Type: ").concat(e.data.type)),e.data.config&&console.log("[".concat(a,"] [RunButton] Node ").concat(e.id," config keys: ").concat(Object.keys(e.data.config).join(", ")||"none")),e.data.definition&&e.data.definition.inputs&&(console.log("[".concat(a,"] [RunButton] Node ").concat(e.id," has ").concat(e.data.definition.inputs.length," inputs")),e.data.definition.inputs.forEach(e=>{console.log("[".concat(a,"] [RunButton] - Input: ").concat(e.name,", Type: ").concat(e.input_type,", Required: ").concat(!0===e.required?"YES":!1===e.required?"NO":"undefined"))})))}),console.log("[".concat(a,"] [RunButton] Collecting all fields from connected nodes..."));let t=(0,z.vp)(e,w,n);console.log("[".concat(a,"] [RunButton] Found ").concat(t.length," total fields from connected nodes")),t.length>0&&(console.log("[".concat(a,"] [RunButton] All fields details:")),t.forEach((e,t)=>{console.log("[".concat(a,"] [RunButton]   ").concat(t+1,". Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType,", Required: ").concat(e.required?"YES":"NO",", Empty: ").concat(e.isEmpty?"YES":"NO"))})),g&&t.length>0&&(console.log("[".concat(a,"] [RunButton] Storing all fields in StartNode collected parameters...")),g.data.config||(g.data.config={}),g.data.config.collected_parameters||(g.data.config.collected_parameters={}),t.forEach(e=>{let t="".concat(e.nodeId,"_").concat(e.name);if(g.data.config&&g.data.config.collected_parameters){let n=!0===e.directly_connected_to_start;console.log("[".concat(a,"] [RunButton] Adding field ").concat(e.nodeName,".").concat(e.name," to StartNode collected parameters:\n                - Field ID: ").concat(t,"\n                - Directly connected to Start: ").concat(n?"YES":"NO","\n                - Required: ").concat(!1!==e.required?"YES":"NO")),g.data.config.collected_parameters[t]={node_id:e.nodeId,node_name:e.nodeName,input_name:e.name,value:e.currentValue,connected_to_start:n,required:e.required,input_type:e.inputType,options:e.options}}}),window.startNodeCollectedParameters=g.data.config.collected_parameters,console.log("[".concat(a,"] [RunButton] Stored ").concat(Object.keys(window.startNodeCollectedParameters).length," fields in window.startNodeCollectedParameters"))),console.log("[".concat(a,"] [RunButton] Collecting missing required fields directly..."));let o=(0,z.pt)(e,w,n);console.log("[".concat(a,"] [RunButton] Direct validation found ").concat(o.length," missing required fields")),o.length>0&&(console.log("[".concat(a,"] [RunButton] Missing required fields details:")),o.forEach((e,t)=>{console.log("[".concat(a,"] [RunButton]   ").concat(t+1,". Node: ").concat(e.nodeName," (").concat(e.nodeId,"), Field: ").concat(e.displayName," (").concat(e.name,"), Type: ").concat(e.inputType))}));let r=o.length>0?o:x.missingFields||[];if(r.length>0){console.log("[".concat(a,"] [RunButton] ========== PROCESSING MISSING FIELDS ==========")),console.log("[".concat(a,"] [RunButton] Total missing fields to process: ").concat(r.length));let e={};r.forEach(t=>{e[t.nodeId]||(e[t.nodeId]=[]),e[t.nodeId].push(t)}),console.log("[".concat(a,"] [RunButton] Missing fields grouped by ").concat(Object.keys(e).length," nodes")),Object.entries(e).forEach(e=>{let[t,n]=e;console.log("[".concat(a,"] [RunButton] Node ").concat(t," has ").concat(n.length," missing fields"))}),console.log("[".concat(a,"] [RunButton] Storing missing fields in execution store")),p(r),m("Found ".concat(r.length," missing required fields across ").concat(Object.keys(e).length," nodes.")),Object.entries(e).forEach(e=>{let[t,n]=e,o=n[0].nodeName||t;console.log("[".concat(a,"] [RunButton] Adding log for node ").concat(o," with ").concat(n.length," missing fields")),m('Node "'.concat(o,'" is missing ').concat(n.length," required fields:")),n.forEach(e=>{console.log("[".concat(a,"] [RunButton] Adding log for missing field ").concat(e.displayName)),m("  - ".concat(e.displayName," (").concat(e.inputType,")"))})}),console.log("[".concat(a,"] [RunButton] ========== MISSING FIELDS PROCESSING COMPLETE =========="))}else console.log("[".concat(a,"] [RunButton] No missing fields found in direct validation")),p([])}else console.log("[".concat(a,"] [RunButton] No start node or connected nodes found")),p([]);e.length>0?(window.currentWorkflowNodes=e,console.log("[".concat(a,"] [RunButton] Stored ").concat(e.length," nodes in window.currentWorkflowNodes"))):window.currentWorkflowNodes&&window.currentWorkflowNodes.length>0?console.log("[".concat(a,"] [RunButton] Keeping existing ").concat(window.currentWorkflowNodes.length," nodes in window.currentWorkflowNodes for prebuilt workflow")):(window.currentWorkflowNodes=[],console.log("[".concat(a,"] [RunButton] Initialized empty window.currentWorkflowNodes array"))),console.log("[".concat(a,"] [RunButton] ========== OPENING EXECUTION DIALOG ==========")),console.log("[".concat(a,"] [RunButton] Dialog will open with tab: parameters"));let b=l.missingFields;if(console.log("[".concat(a,"] [RunButton] Missing fields count: ").concat(b?b.length:0)),console.log("[".concat(a,"] [RunButton] StartNode collected parameters: ").concat(JSON.stringify(window.startNodeCollectedParameters||{}))),console.log("[".concat(a,"] [RunButton] Resetting execution store state to ensure fresh input values")),S(),u(!0),h("parameters"),!x.isValid){if(x.errors.length>0){let e=x.errors.map(e=>e.message).join("; ");m("Validation error: ".concat(e))}else m("Validation failed. Please check your workflow configuration.");return}m("Validation successful. Please review parameters before execution.")}catch(e){m("Error: ".concat(e instanceof Error?e.message:String(e)))}},[t,r,m,f,h,g,u,p,v,b,S,_,E,C,l]),O=async()=>{console.log("Stopping workflow execution");let{correlationId:e}=ey.getState();if(e)try{console.log("Sending stop request to backend for correlation ID: ".concat(e));let{sendApprovalDecision:t}=await Promise.resolve().then(n.bind(n,69527)),o=await t(e,"reject");o.success?(console.log("Successfully stopped workflow execution for correlation ID: ".concat(e)),m("✅ Successfully stopped workflow execution")):(console.error("Failed to stop workflow execution: ".concat(o.error)),m("❌ Failed to stop workflow execution: ".concat(o.error)))}catch(e){console.error("Error stopping workflow execution:",e),m("❌ Error stopping workflow execution: ".concat(e instanceof Error?e.message:String(e)))}else console.warn("No correlation ID available, cannot send stop request to backend"),m("⚠️ Cannot stop workflow execution: No correlation ID available");I.current&&(console.log("Closing SSE connection on stop execution"),I.current.close(),I.current=null),j()};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("button",{type:"button",onClick:()=>{y||w?k():s?s():D()},disabled:i&&!y,className:"inline-flex h-8 items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium ".concat(y?"bg-blue-600 hover:bg-blue-700":w?"bg-yellow-600 hover:bg-yellow-700":"bg-green-600 hover:bg-green-700"," text-white disabled:pointer-events-none disabled:opacity-50"),title:y?"View ongoing workflow execution":w?"Workflow is running":"Run workflow",children:[y?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(M.A,{className:"h-4 w-4"}),"View Execution"]}):w?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(q.A,{className:"h-4 w-4"}),"Running..."]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(q.A,{className:"h-4 w-4"}),"Run"]}),y&&(0,o.jsxs)("span",{className:"absolute top-0 right-0 -mt-1 -mr-1 flex h-3 w-3",children:[(0,o.jsx)("span",{className:"absolute inline-flex h-full w-full animate-ping rounded-full bg-blue-400 opacity-75"}),(0,o.jsx)("span",{className:"relative inline-flex h-3 w-3 rounded-full bg-blue-500"})]})]}),(0,o.jsx)(eD,{onClose:()=>{console.log("Closing dialog but keeping SSE connection active"),w&&N(!0),u(!1)},onStopExecution:O})]})}),eT=a.memo(function(e){let{nodes:t,edges:n,disabled:a,onRun:r}=e;return(0,o.jsx)(B.Ln,{children:(0,o.jsx)(eO,{nodes:t,edges:n,disabled:a,onRun:r})})}),eF=a.memo(function(e){let{nodes:t,edges:n,disabled:r,onRun:i}=e,[s,l]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{l(!0)},[]),s)?(0,o.jsx)(eT,{nodes:t,edges:n,disabled:r,onRun:i}):(0,o.jsx)("button",{type:"button",className:"inline-flex h-8 items-center justify-center gap-1.5 rounded-md bg-green-600 px-3 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:pointer-events-none disabled:opacity-50",disabled:!0,children:"Run"})});var eR=n(89613);function eW(e){let{delayDuration:t=0,...n}=e;return(0,o.jsx)(eR.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...n})}function eV(e){let{...t}=e;return(0,o.jsx)(eW,{children:(0,o.jsx)(eR.bL,{"data-slot":"tooltip",...t})})}function eL(e){let{...t}=e;return(0,o.jsx)(eR.l9,{"data-slot":"tooltip-trigger",...t})}function eP(e){let{className:t,sideOffset:n=0,children:a,...r}=e;return(0,o.jsx)(eR.ZL,{children:(0,o.jsxs)(eR.UC,{"data-slot":"tooltip-content",sideOffset:n,className:(0,d.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...r,children:[a,(0,o.jsx)(eR.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}var eB=n(4229),eM=n(10081),eq=n(23837),eU=n(83744),eJ=n(62525),eG=n(69803),ez=n(62098),eH=n(93509),eK=n(94788),eY=n(43453),eX=n(54361),e$=n(44838),eZ=n(63449);let eQ=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)(eZ.bL,{ref:t,className:(0,d.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",n),...a})});eQ.displayName=eZ.bL.displayName,a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)(eZ._V,{ref:t,className:(0,d.cn)("aspect-square h-full w-full",n),...a})}).displayName=eZ._V.displayName;let e0=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)(eZ.H4,{ref:t,className:(0,d.cn)("bg-muted flex h-full w-full items-center justify-center rounded-full",n),...a})});e0.displayName=eZ.H4.displayName;var e1=n(71007),e2=n(381),e5=n(34835);function e4(){var e;let{user:t,isAuthenticated:n,logout:a}=(0,eX.k)(),i=(0,r.useRouter)();if(!n||!t)return(0,o.jsx)(ek.$,{variant:"outline",size:"sm",onClick:()=>i.push("/login"),children:"Log in"});let s=async()=>{try{await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"logout"})}),await a(),i.push("/login")}catch(e){console.error("Error during logout:",e),await a(),i.push("/login")}};return(0,o.jsxs)(e$.rI,{children:[(0,o.jsx)(e$.ty,{asChild:!0,children:(0,o.jsx)(ek.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,o.jsx)(eQ,{className:"h-8 w-8",children:(0,o.jsx)(e0,{className:"bg-primary text-primary-foreground",children:(e=t.fullName)?e.split(" ").map(e=>e[0]).join("").toUpperCase().substring(0,2):"U"})})})}),(0,o.jsxs)(e$.SQ,{align:"end",className:"w-56",children:[(0,o.jsx)(e$.lp,{children:(0,o.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,o.jsx)("p",{className:"text-sm leading-none font-medium",children:t.fullName}),(0,o.jsx)("p",{className:"text-muted-foreground text-xs leading-none",children:t.email})]})}),(0,o.jsx)(e$.mB,{}),(0,o.jsxs)(e$._2,{onClick:()=>i.push("/profile"),children:[(0,o.jsx)(e1.A,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{children:"Profile"})]}),(0,o.jsxs)(e$._2,{onClick:()=>i.push("/settings"),children:[(0,o.jsx)(e2.A,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{children:"Settings"})]}),(0,o.jsx)(e$.mB,{}),(0,o.jsxs)(e$._2,{onClick:s,children:[(0,o.jsx)(e5.A,{className:"mr-2 h-4 w-4"}),(0,o.jsx)("span",{children:"Log out"})]})]})]})}let e3=a.memo(function(e){let{onSave:t,onRun:n,workflowTitle:r="Untitled Workflow",onTitleChange:i,onValidate:l,onExport:c,onImport:d,onDelete:u,onLoad:p,isDarkMode:m,onToggleTheme:f,className:g,nodes:x=[],edges:v=[]}=e,[w,b]=(0,a.useState)(!1),[y,N]=(0,a.useState)(r);(0,a.useEffect)(()=>{N(r)},[r]);let j=()=>{b(!1),i&&y.trim()&&i(y)};return(0,o.jsxs)("div",{className:"bg-card/30 flex h-16 shrink-0 items-center justify-between border-b px-4 py-2 shadow-sm backdrop-blur-sm ".concat(g||""),children:[(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)(s(),{href:"/workflows",className:"mr-2 flex items-center",children:(0,o.jsx)(P.default,{src:"/wflogo_white.svg",alt:"Workflow Builder Logo",width:120,height:30,className:"h-8 w-auto"})}),w?(0,o.jsx)(h.p,{value:y,onChange:e=>{N(e.target.value)},onBlur:j,onKeyDown:e=>{"Enter"===e.key&&j()},autoFocus:!0,className:"bg-background/50 h-9 w-64 text-lg font-semibold"}):(0,o.jsx)("h1",{className:"hover:text-primary cursor-pointer text-lg font-semibold transition-colors",onClick:()=>{i&&b(!0)},title:i?"Click to edit workflow title":"",children:r}),(0,o.jsx)(L,{orientation:"vertical",className:"h-8"}),(0,o.jsxs)("button",{type:"button",className:"bg-secondary text-secondary-foreground hover:bg-secondary/80 inline-flex h-8 items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium shadow-xs",onClick:t,title:"Save workflow (Ctrl+S)",children:[(0,o.jsx)(eB.A,{className:"h-4 w-4"})," Save"]}),(0,o.jsxs)("button",{type:"button",className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center gap-1.5 rounded-md border px-3 py-2 text-sm font-medium shadow-xs",onClick:p||(()=>console.log("Load clicked")),title:"Load existing workflow",children:["Load ",(0,o.jsx)(eM.A,{className:"h-4 w-4"})]})]}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[c&&(0,o.jsx)("button",{type:"button",className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-xs",onClick:c,title:"Export workflow",children:(0,o.jsx)(eq.A,{className:"h-4 w-4"})}),d&&(0,o.jsx)("button",{type:"button",className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-xs",onClick:d,title:"Import workflow",children:(0,o.jsx)(eU.A,{className:"h-4 w-4"})}),u&&(0,o.jsx)("button",{type:"button",className:"bg-background hover:bg-accent text-destructive hover:bg-destructive/10 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium shadow-xs",onClick:u||(()=>console.log("Delete clicked")),title:"Delete workflow",children:(0,o.jsx)(eJ.A,{className:"h-4 w-4"})}),(0,o.jsx)(L,{orientation:"vertical",className:"mx-1 h-8"}),(0,o.jsx)(s(),{href:"/credentials",className:"hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium",title:"Manage Credentials",children:(0,o.jsx)(eG.A,{className:"h-4 w-4"})}),f&&(0,o.jsx)("button",{type:"button",className:"hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium",onClick:f,title:"Toggle theme",children:m?(0,o.jsx)(ez.A,{className:"h-4 w-4"}):(0,o.jsx)(eH.A,{className:"h-4 w-4"})}),(0,o.jsx)("button",{type:"button",className:"hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium",title:"Help",children:(0,o.jsx)(eK.A,{className:"h-4 w-4"})}),(0,o.jsx)(L,{orientation:"vertical",className:"mx-1 h-8"}),l&&(0,o.jsx)(eW,{children:(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsxs)("button",{type:"button",className:"inline-flex h-8 items-center justify-center gap-1.5 rounded-md border border-yellow-500/30 bg-yellow-500/10 px-3 py-2 text-sm font-medium text-yellow-700 hover:bg-yellow-500/20 dark:text-yellow-400",onClick:l||(()=>console.log("Validate clicked")),children:[(0,o.jsx)(eY.A,{className:"h-4 w-4"})," Validate Workflow"]})}),(0,o.jsx)(eP,{children:(0,o.jsxs)("p",{children:["Validate the entire workflow.",(0,o.jsx)("br",{}),"Fields are not validated during editing."]})})]})}),(0,o.jsx)(eF,{nodes:x,edges:v,disabled:0===x.length,onRun:n}),(0,o.jsx)(L,{orientation:"vertical",className:"mx-1 h-8"}),(0,o.jsx)(e4,{})]})]})});var e8=n(79295),e9=n(43454),e6=n(25541),e7=n(24357),te=n(32650),tt=n(3201);function tn(e){let{node:t,onNodeDataChange:n}=e,o=function(e,t){var n;let[o,r]=(0,a.useState)([]),i=(0,a.useRef)("");return(0,a.useEffect)(()=>{if(!e||!e.data||!e.data.definition)return;let n="".concat(e.id,"-").concat(JSON.stringify(e.data.definition));if(n===i.current)return;i.current=n;let o=function(e){if(!e||!e.data||!e.data.definition)return[];let t=e.data.definition.inputs||[],n=e.data.definition.mcp_info||{},o=(null==n?void 0:n.input_schema)||{properties:{}};return t.map(e=>{if(!e.name)return console.warn("Input missing name:",e),null;if(e.display_name||(e.display_name=e.name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())),e.input_type||(e.input_type="string"),e.is_handle&&(e.input_types?e.input_types.includes("Any")||e.input_types.push("Any"):e.input_types=[e.input_type,"Any"]),e.name.endsWith("_handle")&&(e.input_type="handle",e.is_handle=!0),e.name.endsWith("_connection")&&!e.is_handle){let n=e.name.replace("_connection","");t.some(e=>e.name===n)&&(e.name="".concat(n,"_handle"),e.input_type="handle",e.is_handle=!0)}if(e.input_types||("handle"===e.input_type?e.input_types=["Any"]:e.input_types=[e.input_type]),void 0===e.real_time_refresh&&(e.real_time_refresh=!1),void 0===e.advanced&&(e.advanced=!1),void 0===e.is_list&&(e.is_list="list"===e.input_type),void 0===e.visibility_logic&&(e.visibility_logic="OR"),void 0===e.value)switch(e.input_type){case"string":default:e.value="";break;case"int":case"number":e.value=0;break;case"bool":e.value=!1;break;case"list":e.value=[];break;case"dict":case"object":e.value={};break;case"handle":e.value=null}let n=e.name.replace("_handle",""),a=o.properties?o.properties[n]:null;return"dict"===e.input_type&&a&&"object"===a.type&&a.properties&&(e.input_type="object",e.properties=a.properties),a&&(e.validation=e.validation||{},"string"===a.type&&a.minLength&&(e.validation.minLength=a.minLength),"string"===a.type&&a.maxLength&&(e.validation.maxLength=a.maxLength),("number"===a.type||"integer"===a.type)&&void 0!==a.minimum&&(e.validation.min=a.minimum),("number"===a.type||"integer"===a.type)&&void 0!==a.maximum&&(e.validation.max=a.maximum),o.required&&o.required.includes(n)&&(e.required=!0)),void 0===e.required&&(e.is_handle||e.name.endsWith("_handle")||"handle"===e.input_type||["link","api_key","base_url"].includes(e.name)?e.required=!1:e.required=!0),e}).filter(Boolean)}(e);r(o);let a=tt.LN.getState(),s=e.data.config||{},l={...a.getValue(e.id,"config",{}),...s},c={...l};if(o.filter(e=>{if(e.name.endsWith("_connection")){let t=e.name.replace("_connection","");return!o.some(e=>e.name===t)}return!0}).forEach(e=>{e.is_handle||void 0!==c[e.name]||(c[e.name]=e.value)}),o.forEach(e=>{if(!(e.is_handle||"handle"===e.input_type||e.name.endsWith("_handle"))&&void 0===c[e.name])if(e.required)switch(e.input_type){case"string":case"text":case"credential":default:c[e.name]="";break;case"number":case"int":case"float":c[e.name]=0;break;case"boolean":case"bool":c[e.name]=!1;break;case"dropdown":e.options&&e.options.length>0?c[e.name]=e.options[0]:void 0!==e.value?c[e.name]=e.value:c[e.name]="";break;case"object":case"dict":case"json":"keywords"===e.name||"tool_arg_keywords"===e.name?c[e.name]={time:"",objective:"",audience:"",gender:"",tone:"",speakers:""}:c[e.name]={};break;case"array":case"list":c[e.name]=[]}else void 0!==e.value&&(c[e.name]=e.value)}),JSON.stringify(c)!==JSON.stringify(l)&&(a.setValue(e.id,"inputs",o),a.setValue(e.id,"config",c),JSON.stringify(c)!==JSON.stringify(s))){let n={...e.data,config:c};t(e.id,n)}},[null==e?void 0:e.id,null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.definition]),o}(t,n),r=(0,a.useRef)(!1),i=(0,a.useRef)("");return(0,a.useEffect)(()=>{},[t,o]),(0,a.useEffect)(()=>{if(!t||!t.data||0===o.length)return;let e=JSON.stringify(t.data.config||{});if(e===i.current)return;i.current=e;let a={...t.data.config};o.forEach(e=>{e.is_handle||void 0!==a[e.name]||(a[e.name]=e.value)}),o.forEach(e=>{null!==e.value&&void 0!==e.value&&""!==e.value&&("object"!=typeof e.value||Object.keys(e.value).length>0)&&(a[e.name]=e.value)}),tt.LN.getState().setValue(t.id,"config",a);let s=JSON.stringify(a);s!==e&&(r.current=!0,n(t.id,{...t.data,config:a}),i.current=s,setTimeout(()=>{r.current=!1},0))},[t,o,n]),null}var to=n(69474);function ta(e){let{...t}=e;return(0,o.jsx)(to._s.Root,{"data-slot":"drawer",...t})}function tr(e){let{...t}=e;return(0,o.jsx)(to._s.Portal,{"data-slot":"drawer-portal",...t})}function ti(e){let{className:t,...n}=e;return(0,o.jsx)(to._s.Overlay,{"data-slot":"drawer-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...n})}function ts(e){let{className:t,children:n,...a}=e;return(0,o.jsxs)(tr,{"data-slot":"drawer-portal",children:[(0,o.jsx)(ti,{}),(0,o.jsxs)(to._s.Content,{"data-slot":"drawer-content",className:(0,d.cn)("group/drawer-content bg-background fixed z-50 flex h-auto flex-col","data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-lg data-[vaul-drawer-direction=top]:border-b","data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-lg data-[vaul-drawer-direction=bottom]:border-t","data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm","data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm",t),...a,children:[(0,o.jsx)("div",{className:"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block"}),n]})]})}function tl(e){let{className:t,...n}=e;return(0,o.jsx)("div",{"data-slot":"drawer-header",className:(0,d.cn)("flex flex-col gap-1.5 p-4",t),...n})}function tc(e){let{className:t,...n}=e;return(0,o.jsx)("div",{"data-slot":"drawer-footer",className:(0,d.cn)("mt-auto flex flex-col gap-2 p-4",t),...n})}function td(e){let{className:t,...n}=e;return(0,o.jsx)(to._s.Title,{"data-slot":"drawer-title",className:(0,d.cn)("text-foreground font-semibold",t),...n})}function tu(e){let{className:t,...n}=e;return(0,o.jsx)(to._s.Description,{"data-slot":"drawer-description",className:(0,d.cn)("text-muted-foreground text-sm",t),...n})}let tp=(0,x.F)("[&>svg]:text-foreground relative w-full rounded-lg border p-4 [&>svg]:absolute [&>svg]:top-4 [&>svg]:left-4 [&>svg+div]:translate-y-[-3px] [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),tm=a.forwardRef((e,t)=>{let{className:n,variant:a,...r}=e;return(0,o.jsx)("div",{ref:t,role:"alert",className:(0,d.cn)(tp({variant:a}),n),...r})});tm.displayName="Alert";let tf=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)("h5",{ref:t,className:(0,d.cn)("mb-1 leading-none font-medium tracking-tight",n),...a})});tf.displayName="AlertTitle";let tg=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,o.jsx)("div",{ref:t,className:(0,d.cn)("text-sm [&_p]:leading-relaxed",n),...a})});tg.displayName="AlertDescription";let th=(0,ee.v)()((0,eb.Zr)(e=>({activeTab:"settings",showValidation:!1,validationErrors:{},preferences:{defaultTab:"settings",expandedSections:[]},setActiveTab:t=>e({activeTab:t}),setShowValidation:t=>e({showValidation:t}),setValidationError:(t,n)=>e(e=>({validationErrors:{...e.validationErrors,[t]:n}})),clearValidationErrors:()=>e({validationErrors:{},showValidation:!1}),setPreference:(t,n)=>e(e=>({preferences:{...e.preferences,[t]:n}})),toggleExpandedSection:t=>e(e=>{let n=[...e.preferences.expandedSections],o=n.indexOf(t);return -1===o?n.push(t):n.splice(o,1),{preferences:{...e.preferences,expandedSections:n}}})}),{name:"inspector-preferences",partialize:e=>({preferences:e.preferences})})),tx=(0,a.createContext)(void 0);function tv(e){let{children:t,selectedNode:r,onNodeDataChange:i,onClose:s,onDeleteNode:l,edges:c,nodes:d,setIsEditingField:u}=e,{activeTab:p,setActiveTab:m,showValidation:f,setShowValidation:g,validationErrors:h,setValidationError:x,clearValidationErrors:v}=th(),{isInputConnected:w,shouldDisableInput:b,getConnectionInfo:y}=function(e,t,n){let[o,r]=(0,a.useState)({}),[i,s]=(0,a.useState)({});(0,a.useEffect)(()=>{var n,o;if(!e){r({}),s({});return}let a=t.filter(t=>t.target===e.id),i={},l={};(null===(n=e.data.definition)||void 0===n?void 0:n.inputs)&&(e.data.definition.inputs.forEach(e=>{e.is_handle&&(i[e.name]=!1)}),(null===(o=e.data.config)||void 0===o?void 0:o.inputs)&&e.data.config.inputs.forEach(e=>{e.is_handle&&(i[e.name]=!1)})),a.forEach(e=>{e.targetHandle&&(i[e.targetHandle]=!0,e.source&&e.sourceHandle&&(l[e.targetHandle]={nodeId:e.source,handleId:e.sourceHandle}))}),r(i),s(l)},[e,t]);let l=(0,a.useCallback)(e=>!!o[e],[o]),c=(0,a.useCallback)(e=>!e.endsWith("_handle")&&(!!l(e)||l("".concat(e,"_handle"))),[o,l]),d=(0,a.useCallback)(e=>i[e]||null,[i]),u=(0,a.useCallback)(e=>{if(!l(e))return{isConnected:!1};let t=d(e);if(!t)return{isConnected:!0};let o=n.find(e=>e.id===t.nodeId);return o?{isConnected:!0,sourceNodeId:t.nodeId,sourceNodeType:o.data.type,sourceNodeLabel:o.data.label||o.data.type}:{isConnected:!0,sourceNodeId:t.nodeId}},[l,d,n]);return{connectedInputs:o,isInputConnected:l,shouldDisableInput:c,getConnectedSource:d,getConnectionInfo:u}}(r,c,d),[N,j]=a.useState({isOpen:!1,title:"",message:"",preserveState:!0}),k=a.useRef(null);return(0,o.jsx)(tx.Provider,{value:{selectedNode:r,activeTab:p,setActiveTab:m,showValidation:f,setShowValidation:g,validationErrors:h,setValidationError:x,clearValidationErrors:v,isInputConnected:w,shouldDisableInput:b,getConnectionInfo:y,handleLabelChange:e=>{if(!r)return;u&&u(!0);let t={...r.data,label:e.target.value};if(i(r.id,t),window.currentWorkflowNodes){let t=window.currentWorkflowNodes.findIndex(e=>e.id===r.id);if(-1!==t){let n=[...window.currentWorkflowNodes];n[t]={...n[t],data:{...n[t].data,label:e.target.value}},window.currentWorkflowNodes=n}}u&&u(!1)},handleConfigChange:(e,t)=>{if(!r)return;u&&u(!0);let n=Date.now(),o=k.current;if(o&&o.nodeId===r.id&&o.inputName===e&&JSON.stringify(o.value)===JSON.stringify(t)&&n-o.timestamp<10)return;if(k.current={nodeId:r.id,inputName:e,value:t,timestamp:n},"mcp"===r.data.type){let n=tt.LN.getState(),o=n.getValue(r.id,"config",{});if(JSON.stringify(o[e])===JSON.stringify(t)){u&&u(!1);return}let a={...o,[e]:t};n.setValue(r.id,"config",a)}let a=r.data;if(a.config&&JSON.stringify(a.config[e])===JSON.stringify(t)){u&&u(!1);return}let s={...a.config,[e]:t},l={...a,config:s};if(i(r.id,l),window.currentWorkflowNodes){let n=window.currentWorkflowNodes.findIndex(e=>e.id===r.id);if(-1!==n){let o=[...window.currentWorkflowNodes];o[n]={...o[n],data:{...o[n].data,config:{...o[n].data.config,[e]:t}}},window.currentWorkflowNodes=o}}u&&u(!1)},handleDefinitionChange:(e,t)=>{if(!r||!r.data.definition)return;u&&u(!0);let n={...r.data.definition,[e]:t},o={...r.data,definition:n};if(i(r.id,o),window.currentWorkflowNodes){let e=window.currentWorkflowNodes.findIndex(e=>e.id===r.id);if(-1!==e){let t=[...window.currentWorkflowNodes];t[e]={...t[e],data:{...t[e].data,definition:n}},window.currentWorkflowNodes=t}}u&&u(!1)},applyAllChanges:()=>{if(r&&(j({isOpen:!0,title:"Changes Applied",message:"All changes have been applied to the node configuration.",preserveState:!1}),window.currentWorkflowNodes)){let e=window.currentWorkflowNodes.findIndex(e=>e.id===r.id);if(-1!==e){let t=[...window.currentWorkflowNodes];t[e]={...r},window.currentWorkflowNodes=t}}},onClose:s,onDeleteNode:l,validateAllNodeInputs:()=>{var e;r&&(null===(e=r.data.definition)||void 0===e?void 0:e.inputs)&&Promise.resolve().then(n.bind(n,2910)).then(e=>{var t;let{validateAllInputs:n}=e,o=n((null===(t=r.data.definition)||void 0===t?void 0:t.inputs)||[],r.data.config||{});v(),Object.entries(o).forEach(e=>{let[t,n]=e;x(t,n)}),g(!0);let a=Object.values(o).every(e=>e.isValid);j({isOpen:!0,title:a?"Validation Successful":"Validation Failed",message:a?"All inputs are valid.":"Some inputs are invalid. Please check the highlighted fields. Validation only occurs when explicitly requested or before saving/executing the workflow.",preserveState:!a})})},notification:N,setNotification:j},children:t})}function tw(){let e=(0,a.useContext)(tx);if(void 0===e)throw Error("useInspector must be used within an InspectorProvider");return e}var tb=n(54416);function ty(){var e;let{selectedNode:t,onClose:n}=tw();return(null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.definition)?(0,o.jsxs)(tl,{className:"flex flex-shrink-0 items-start justify-between border-b p-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)(td,{className:"flex items-center gap-2",children:[t.data.definition.display_name,(0,o.jsx)(w,{variant:"outline",className:"bg-muted/50 text-[10px]",children:t.data.definition.category})]}),(0,o.jsx)(tu,{className:"mt-1 text-xs",children:t.data.definition.description})]}),(0,o.jsx)(ek.$,{variant:"ghost",size:"icon",className:"-mt-1 -mr-2 h-8 w-8",onClick:n,children:(0,o.jsx)(tb.A,{className:"h-4 w-4"})})]}):null}var tN=n(14711),tj=n(47655);function tk(e){let{className:t,children:n,...a}=e;return(0,o.jsxs)(tj.bL,{"data-slot":"scroll-area",className:(0,d.cn)("relative",t),...a,children:[(0,o.jsx)(tj.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:n}),(0,o.jsx)(tS,{}),(0,o.jsx)(tj.OK,{})]})}function tS(e){let{className:t,orientation:n="vertical",...a}=e;return(0,o.jsx)(tj.VM,{"data-slot":"scroll-area-scrollbar",orientation:n,className:(0,d.cn)("flex touch-none p-px transition-colors select-none","vertical"===n&&"h-full w-2.5 border-l border-l-transparent","horizontal"===n&&"h-2.5 flex-col border-t border-t-transparent",t),...a,children:(0,o.jsx)(tj.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}function t_(e){var t,n,a,r,i,s;let{node:l}=e;return(0,o.jsx)(tk,{className:"h-full flex-grow overflow-auto p-4",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"mb-3 text-sm font-medium",children:"Node Information"}),(0,o.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,o.jsxs)("div",{className:"flex justify-between border-b py-1.5",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:"Type"}),(0,o.jsx)("span",{className:"font-medium",children:l.data.type})]}),(0,o.jsxs)("div",{className:"flex justify-between border-b py-1.5",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:"ID"}),(0,o.jsx)("span",{className:"font-mono text-[10px] font-medium",children:l.id})]}),(0,o.jsxs)("div",{className:"flex justify-between border-b py-1.5",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:"Category"}),(0,o.jsx)("span",{className:"font-medium",children:(null===(t=l.data.definition)||void 0===t?void 0:t.category)||"Unknown"})]})]})]}),(null===(r=l.data.definition)||void 0===r?void 0:null===(a=r.inputs)||void 0===a?void 0:null===(n=a.filter(e=>e.is_handle))||void 0===n?void 0:n.length)>0&&(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"mb-2 text-sm font-medium",children:"Input Handles"}),(0,o.jsx)("div",{className:"space-y-1.5 text-xs",children:l.data.definition.inputs.filter(e=>e.is_handle).map(e=>{var t;return(0,o.jsxs)("div",{className:"flex justify-between border-b py-1.5",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:e.display_name}),(0,o.jsx)("span",{className:"font-medium",children:(null===(t=e.input_types)||void 0===t?void 0:t.join(", "))||"any"})]},e.name)})})]}),(null===(s=l.data.definition)||void 0===s?void 0:null===(i=s.outputs)||void 0===i?void 0:i.length)>0&&(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"mb-2 text-sm font-medium",children:"Output Handles"}),(0,o.jsx)("div",{className:"space-y-1.5 text-xs",children:l.data.definition.outputs.map(e=>(0,o.jsxs)("div",{className:"flex justify-between border-b py-1.5",children:[(0,o.jsx)("span",{className:"text-muted-foreground",children:e.display_name}),(0,o.jsx)("span",{className:"font-medium",children:e.output_type})]},e.name))})]})]})})}function tE(e,t){if(!e.visibility_rules||0===e.visibility_rules.length)return!0;let n=e.visibility_logic||"OR";return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"OR";if(!e||0===e.length)return!0;let o=e.map(e=>(function(e,t){let n=null==t?void 0:t[e.field_name];return void 0===e.operator||"equals"===e.operator?n===e.field_value:"not_equals"===e.operator?n!==e.field_value:"contains"===e.operator?Array.isArray(n)?n.includes(e.field_value):"string"==typeof n&&n.includes(String(e.field_value)):"greater_than"===e.operator?"number"==typeof n&&"number"==typeof e.field_value&&n>e.field_value:"less_than"===e.operator?"number"==typeof n&&"number"==typeof e.field_value&&n<e.field_value:"exists"===e.operator?null!=n:"not_exists"===e.operator&&null==n})(e,t));return"AND"===n?o.every(e=>e):o.some(e=>e)}(e.visibility_rules,t,n)}function tC(e,t,n){var o,a,r,i;if(!t)return!1;if("DynamicCombineTextComponent"===t.data.type&&e.name.startsWith("input_")&&!e.is_handle){let t=e.name.match(/input_(\d+)/);if(t&&t[1])return parseInt(t[1],10)<=parseInt(n.num_additional_inputs||"0",10)}if("ConditionalNode"===t.data.originalType){let t=e.name.match(/condition_(\d+)_/);if(t&&t[1]){let o=parseInt(t[1],10),a=parseInt(n.num_additional_conditions||"0",10);if(o>2&&o>2+a)return!1;if(e.name.endsWith("_input_handle"))return"node_output"===(n["condition_".concat(o,"_source")]||"node_output")}}return(o=t)&&("MCPMarketplaceComponent"===o.data.type||(null===(a=o.data.definition)||void 0===a?void 0:a.category)==="MCP Marketplace")?function(e,t,n){var o,a,r,i,s,l,c;if("handle"===e.input_type||e.name.endsWith("_handle"))return!0;if(e.name.endsWith("_connection")){let n=e.name.replace("_connection","");if(null===(r=t.data)||void 0===r?void 0:null===(a=r.definition)||void 0===a?void 0:null===(o=a.inputs)||void 0===o?void 0:o.some(e=>e.name===n))return!1}return!!e.is_handle||(i=e.name,(null==(s=t)?1:null===(c=s.data)||void 0===c?1:null===(l=c.definition)||void 0===l?1:!l.inputs)||s.data.definition.inputs.find(e=>(e.is_handle||"handle"===e.input_type)&&e.name==="".concat(i,"_handle")),!0)}(e,t,0):"MCPToolsComponent"===t.data.type?(r=e,i=n,"selected_tool_name"===r.name?"Connected"===(i.connection_status||"Not Connected"):"refresh_tools"!==r.name&&"disconnect"!==r.name&&tE(r,i)):tE(e,n)}var tI=n(2910);function tA(e){let{children:t,inputDef:n,value:r,className:i}=e,{showValidation:s,validationErrors:l,setValidationError:c}=th(),u=l[n.name]||{isValid:!0,message:""},p=u.isValid,m=u.message;return(0,a.useCallback)((0,d.s)((e,t)=>{let n=(0,tI.validateInput)(e,t);c(e.name,n)},500),[c]),(0,o.jsxs)("div",{className:(0,d.cn)("space-y-1",i),children:[t,s&&(m||n.required&&p)&&(0,o.jsx)(tD,{isValid:p,message:m})]})}function tD(e){let{isValid:t,message:n}=e;return n||t||(n="Invalid input"),(0,o.jsxs)("div",{className:(0,d.cn)("mt-1 flex items-center gap-1.5 text-xs",t?"text-success":"text-destructive"),children:[t?(0,o.jsx)(em.A,{className:"h-3 w-3"}):(0,o.jsx)(ep.A,{className:"h-3 w-3"}),(0,o.jsx)("span",{children:n})]})}var tO=n(28309),tT=n(49103),tF=n(38164);function tR(e){let{className:t,showText:n=!0}=e;return(0,o.jsx)(eW,{children:(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsx)("div",{className:(0,d.cn)("pointer-events-none absolute inset-0 flex items-center justify-center",t),children:(0,o.jsxs)("span",{className:"text-muted-foreground bg-background/80 border-primary/30 flex items-center gap-1 rounded border px-2 py-1 text-xs",children:[(0,o.jsx)(tF.A,{className:"text-primary h-3 w-3"}),n&&"Connected via handle"]})})}),(0,o.jsx)(eP,{side:"right",children:"This input is controlled by another node via handle and cannot be edited directly."})]})})}function tW(e){let{inputDef:t,currentValue:n,onChange:r,isDisabled:i=!1,isConnected:s=!1,minInputs:l=1,maxInputs:c=10,defaultInputs:u=2}=e,[p,m]=(0,a.useState)(Array.isArray(n)?Math.max(n.length,l):u),f=Array.isArray(n)?n:Array(p).fill(""),g=e=>{if(p>l){m(p-1);let n=[...f];n.splice(e,1),r(t.name,n)}},x=(e,n)=>{let o=[...f];o[e]=n,r(t.name,o)};return(0,o.jsxs)("div",{className:"space-y-2",children:[Array.from({length:p}).map((e,n)=>(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsxs)("div",{className:(0,d.cn)("relative",s&&i&&"connected"),children:[(0,o.jsx)(h.p,{id:"".concat(t.name,"_").concat(n),type:"text",value:f[n],onChange:e=>x(n,e.target.value),disabled:i,className:"bg-background/50 mt-1 h-8 text-xs"}),s&&i&&(0,o.jsx)(tR,{className:"absolute top-1/2 right-2 -translate-y-1/2"})]}),p>l&&(0,o.jsx)(ek.$,{variant:"ghost",size:"icon",className:"text-destructive/70 hover:text-destructive h-8 w-8",onClick:()=>g(n),disabled:i,children:(0,o.jsx)(tO.A,{className:"h-4 w-4"})})]},n)),p<c&&(0,o.jsxs)(ek.$,{variant:"outline",size:"sm",className:"mt-2 h-7 w-full border-dashed text-xs",onClick:()=>{if(p<c){m(p+1);let e=[...f,""];r(t.name,e)}},disabled:i,children:[(0,o.jsx)(tT.A,{className:"mr-1 h-3.5 w-3.5"})," Add Input"]})]})}function tV(e,t){if(null==e)return"";switch(t){case"object":case"dict":case"json":case"list":case"array":return"object"==typeof e?JSON.stringify(e,null,2):String(e);case"bool":return e?"True":"False";default:return String(e)}}function tL(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,nodeId:s}=e,l="config-".concat(s,"-").concat(t.name);return(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(h.p,{id:l,type:function(e){switch(e){case"int":case"float":case"number":return"number";case"password":return"password";default:return"text"}}(t.input_type),value:null!=n?n:"",onChange:e=>a(t.name,e.target.value),placeholder:t.display_name,className:(0,d.cn)("bg-background/50 mt-1 h-8 text-xs",r&&"opacity-50"),disabled:r}),r&&i&&(0,o.jsx)(tR,{})]})})}function tP(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,nodeId:s}=e,l="config-".concat(s,"-").concat(t.name),c="float"===t.input_type;return(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(h.p,{id:l,type:"number",step:c?"any":"1",value:null!=n?n:"",onChange:e=>a(t.name,e.target.value),placeholder:t.display_name,className:(0,d.cn)("bg-background/50 mt-1 h-8 text-xs",r&&"opacity-50"),disabled:r}),r&&i&&(0,o.jsx)(tR,{})]})})}var tB=n(4884);function tM(e){let{className:t,...n}=e;return(0,o.jsx)(tB.bL,{"data-slot":"switch",className:(0,d.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:(0,o.jsx)(tB.zi,{"data-slot":"switch-thumb",className:(0,d.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}function tq(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,nodeId:s}=e,l="config-".concat(s,"-").concat(t.name);return(0,o.jsxs)("div",{className:"mt-2 flex items-center space-x-2",children:[(0,o.jsx)(tM,{id:l,checked:!!n,onCheckedChange:e=>a(t.name,e),disabled:r,className:r?"opacity-50":""}),(0,o.jsx)(eo.J,{htmlFor:l,className:"text-xs",children:n?"Enabled":"Disabled"}),i&&(0,o.jsx)(tR,{className:"ml-2"})]})}var tU=n(59409);function tJ(e){let{inputDef:t,currentValue:n,onChange:r,isDisabled:i=!1,isConnected:s=!1}=e,[l,c]=(0,a.useState)("object"==typeof n&&null!==n?n:{});(0,a.useEffect)(()=>{"object"==typeof n&&null!==n&&c(n)},[n]);let d=t.properties||{},g=(e,n)=>{let o={...l,[e]:n};c(o),r(t.name,o)},x=(e,n)=>{if(n.enum&&Array.isArray(n.enum))return n.enum;if(n.oneOf&&Array.isArray(n.oneOf)){let e=n.oneOf.filter(e=>e&&void 0!==e.const).map(e=>String(e.const));if(e.length>0)return e}if("tool_arg_keywords"===t.name){let t={time:["15seconds","30seconds","1minute","2minutes","5minutes"],objective:["educational","informative","entertaining","promotional","instructional"],audience:["children","teenagers","18 to 30 ages","30 to 50 ages","50+ ages","professionals"],gender:["male","female","neutral"],tone:["Casual","Professional","Friendly","Technical","Formal","Humorous"],speakers:["single person","multiple people","influencer","expert","narrator"]};if(t[e])return t[e]}return[]},v=(e,t)=>{let n=l[e]||"",a=t.type||"string",r=t.options||[];return(0===r.length&&(r=x(e,t)),r.length>0)?(0,o.jsxs)(tU.l6,{value:n,onValueChange:t=>g(e,t),disabled:i,children:[(0,o.jsx)(tU.bq,{className:"bg-background/50 h-8 text-xs ".concat(i?"opacity-50":""),children:(0,o.jsx)(tU.yv,{placeholder:"Select ".concat(e,"...")})}),(0,o.jsx)(tU.gC,{children:r.map(e=>(0,o.jsx)(tU.eb,{value:e,className:"text-xs",children:e},e))})]}):(0,o.jsx)(h.p,{type:"number"===a?"number":"text",value:n,onChange:t=>g(e,t.target.value),placeholder:"Enter ".concat(e,"..."),disabled:i,className:"bg-background/50 h-8 text-xs ".concat(i?"opacity-50":"")})};return s&&i?(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,o.jsx)("span",{className:"text-xs font-medium",children:t.display_name||t.name}),(0,o.jsx)(tR,{})]}),(0,o.jsxs)("div",{className:"bg-background/30 border-border/50 rounded-md border p-3 opacity-70",children:[(0,o.jsx)("div",{className:"text-muted-foreground text-xs",children:"Connected to another node. Values will be provided at runtime."}),"object"==typeof n&&null!==n&&Object.keys(n).length>0&&(0,o.jsx)("div",{className:"bg-background/50 mt-2 max-h-32 overflow-auto rounded p-2 font-mono text-xs",children:JSON.stringify(n,null,2)})]})]}):(0,o.jsx)("div",{className:"space-y-2",children:(0,o.jsx)(u,{type:"single",collapsible:!0,defaultValue:"item-1",className:"w-full",children:(0,o.jsxs)(p,{value:"item-1",className:"border-none",children:[(0,o.jsx)(m,{className:"py-2 text-xs font-medium",children:t.display_name||t.name}),(0,o.jsx)(f,{children:(0,o.jsx)("div",{className:"bg-background/30 border-border/50 space-y-3 rounded-md border p-3 pt-2",children:Object.entries(d).map(e=>{let[t,n]=e;return(0,o.jsxs)("div",{className:"space-y-1",children:[(0,o.jsxs)(eo.J,{className:"flex items-center justify-between text-xs font-medium",children:[(0,o.jsx)("span",{children:n.description||t.charAt(0).toUpperCase()+t.slice(1)}),n.required&&(0,o.jsx)("span",{className:"text-destructive text-[10px]",children:"Required"})]}),(0,o.jsx)("div",{className:"relative",children:v(t,n)})]},t)})})})]})})})}var tG=n(74126),tz=n(84616);function tH(e){let{inputDef:t,value:n,onChange:r,isDisabled:i=!1,isConnected:s=!1,nodeId:l}=e,c="config-".concat(l,"-").concat(t.name),[g,x]=(0,a.useState)("object"==typeof n&&null!==n?n:{}),[v,w]=(0,a.useState)(!1),[b,y]=(0,a.useState)(()=>tV(g,"object")),[N,j]=(0,a.useState)([]),[k,S]=(0,a.useState)({}),[_,E]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if("object"==typeof n&&null!==n)x(n);else if("string"==typeof n)try{let e=JSON.parse(n);x(e)}catch(e){}},[n]),(0,a.useEffect)(()=>{var e,o,a,r;if(null===(a=t.mcp_info)||void 0===a?void 0:null===(o=a.input_schema)||void 0===o?void 0:null===(e=o.properties)||void 0===e?void 0:e[t.name]){let e=t.mcp_info.input_schema.properties[t.name];if("object"===e.type&&e.properties){S(e.properties),j(Object.keys(e.properties)),E(!0);return}}if(t.properties&&Object.keys(t.properties).length>0){S(t.properties),j(Object.keys(t.properties)),E(!0);return}if(null===(r=t.schema)||void 0===r?void 0:r.properties){S(t.schema.properties),j(Object.keys(t.schema.properties)),E(!0);return}if("keywords"===t.name){let e={time:{type:"string",description:"Time for the script"},objective:{type:"string",description:"Objective of the script"},audience:{type:"string",description:"Audience for the script"},gender:{type:"string",description:"Gender for the script"},tone:{type:"string",description:"Tone of the script"},speakers:{type:"string",description:"Speaker in the script"}};S(e),j(Object.keys(e)),E(!0);return}"object"==typeof n&&null!==n?j(Object.keys(n)):j(["value"]),E(!1)},[t,n]);let C=(e,n)=>{let o={...g,[e]:n};if(x(o),r(t.name,o),window.currentWorkflowNodes&&l){let e=window.currentWorkflowNodes.findIndex(e=>e.id===l);if(-1!==e){let n=[...window.currentWorkflowNodes];n[e].data.config||(n[e].data.config={}),n[e].data.config[t.name]=o,window.currentWorkflowNodes=n}}},I=e=>{j(N.filter(t=>t!==e));let n={...g};if(delete n[e],x(n),r(t.name,n),window.currentWorkflowNodes&&l){let e=window.currentWorkflowNodes.findIndex(e=>e.id===l);if(-1!==e){let o=[...window.currentWorkflowNodes];o[e].data.config||(o[e].data.config={}),o[e].data.config[t.name]=n,window.currentWorkflowNodes=o}}},A=(0,a.useCallback)((0,d.s)(e=>{try{let n,o=JSON.parse(e);if(_){let e={};N.forEach(t=>{e[t]=void 0!==o[t]?o[t]:g[t]||""}),x(e),r(t.name,e),n=e}else x(o),r(t.name,o),n=o,j(Object.keys(o));if(window.currentWorkflowNodes&&l){let e=window.currentWorkflowNodes.findIndex(e=>e.id===l);if(-1!==e){let o=[...window.currentWorkflowNodes];o[e].data.config||(o[e].data.config={}),o[e].data.config[t.name]=n,window.currentWorkflowNodes=o}}}catch(e){}},50),[_,N,g,r,t.name]),D=e=>{A(e)},O=()=>{w(!v)};return s&&i?(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)("div",{className:"bg-background/30 border-border/50 rounded-md border p-3 opacity-70",children:[(0,o.jsx)("div",{className:"text-muted-foreground text-xs",children:"Connected to another node. Values will be provided at runtime."}),"object"==typeof n&&null!==n&&Object.keys(n).length>0&&(0,o.jsx)("div",{className:"bg-background/50 mt-2 max-h-32 overflow-auto rounded p-2 font-mono text-xs",children:JSON.stringify(n,null,2)})]}),(0,o.jsx)(tR,{})]})}):((0,a.useEffect)(()=>{v&&y(tV(g,"object"))},[g,v]),v)?(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)(eo.J,{className:"text-xs font-medium",children:t.display_name||t.name}),(0,o.jsx)(ek.$,{variant:"ghost",size:"sm",onClick:O,className:"h-6 px-2 text-xs",children:"Switch to Form View"})]}),_&&(0,o.jsx)("div",{className:"text-muted-foreground text-xs mb-2",children:"This object has a predefined structure. Changes that don't match the structure will be ignored."}),(0,o.jsx)(ea.T,{id:c,value:b,onChange:e=>{let t=e.target.value;y(t),D(t)},placeholder:"Enter ".concat(t.display_name," (JSON format)"),className:(0,d.cn)("bg-background/50 font-mono text-xs",i&&"opacity-50"),rows:5,disabled:i})]})}):(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsx)("div",{className:"space-y-2",children:(0,o.jsx)(u,{type:"single",collapsible:!0,defaultValue:"item-1",className:"w-full",children:(0,o.jsxs)(p,{value:"item-1",className:"border-none",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)(m,{className:"py-2 text-xs font-medium",children:t.display_name||t.name}),(0,o.jsx)(ek.$,{variant:"ghost",size:"sm",onClick:O,className:"h-6 px-2 text-xs",children:"Edit as JSON"})]}),(0,o.jsx)(f,{children:(0,o.jsxs)("div",{className:"bg-background/30 border-border/50 space-y-3 rounded-md border p-3 pt-2",children:[_&&(0,o.jsx)("div",{className:"text-muted-foreground text-xs mb-2",children:"This object has a predefined structure. You can only edit the values of existing properties."}),N.map(e=>(0,o.jsxs)("div",{className:"space-y-1",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)(eo.J,{className:"text-xs font-medium",children:e.charAt(0).toUpperCase()+e.slice(1)}),!_&&(0,o.jsx)(ek.$,{variant:"ghost",size:"sm",onClick:()=>I(e),className:"h-6 w-6 p-0",children:(0,o.jsx)(tG.A,{className:"h-3 w-3"})})]}),(0,o.jsx)(h.p,{type:"text",value:g[e]||"",onChange:t=>C(e,t.target.value),placeholder:"Enter ".concat(e,"..."),className:"bg-background/50 h-8 text-xs",disabled:i})]},e)),!_&&(0,o.jsxs)(ek.$,{variant:"outline",size:"sm",onClick:()=>{let e="newProperty",t=1;for(;N.includes(e);)e="newProperty".concat(t),t++;j([...N,e]),C(e,"")},className:"mt-2 w-full text-xs",children:[(0,o.jsx)(tz.A,{className:"mr-1 h-3 w-3"})," Add Property"]})]})})]})})})})}function tK(e){let{inputDef:t,value:n,onChange:r,isDisabled:i=!1,isConnected:s=!1,nodeId:l}=e,c="config-".concat(l,"-").concat(t.name);if("json"===t.input_type||"keywords"===t.name||t.mcp_info&&t.mcp_info.input_schema)return(0,o.jsx)(tH,{inputDef:t,value:n,onChange:r,isDisabled:i,isConnected:s,nodeId:l});if(t.properties&&Object.keys(t.properties).length>0)return(0,o.jsx)(tJ,{inputDef:t,currentValue:n,onChange:r,isDisabled:i,isConnected:s});let[u,p]=a.useState(()=>tV(n,"object")),m=a.useCallback((0,d.s)(e=>{try{let n=JSON.parse(e);if(r(t.name,n),window.currentWorkflowNodes&&l){let e=window.currentWorkflowNodes.findIndex(e=>e.id===l);if(-1!==e){let o=[...window.currentWorkflowNodes];o[e].data.config||(o[e].data.config={}),o[e].data.config[t.name]=n,window.currentWorkflowNodes=o}}}catch(e){}},50),[r,t.name]);return a.useEffect(()=>{let e=tV(n,"object");e!==u&&p(e)},[n]),(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(ea.T,{id:c,value:u,onChange:e=>{let t=e.target.value;p(t),m(t)},placeholder:"Enter ".concat(t.display_name," (JSON format)"),className:(0,d.cn)("bg-background/50 mt-1 font-mono text-xs",i&&"opacity-50"),rows:5,disabled:i}),i&&s&&(0,o.jsx)(tR,{})]})})}function tY(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,nodeId:s}=e,l="config-".concat(s,"-").concat(t.name);return(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(ea.T,{id:l,value:tV(n,"array"),onChange:e=>{try{let n=JSON.parse(e.target.value);a(t.name,n)}catch(n){a(t.name,e.target.value)}},placeholder:"Enter ".concat(t.display_name," (JSON format)"),className:(0,d.cn)("bg-background/50 mt-1 font-mono text-xs",r&&"opacity-50"),rows:5,disabled:r}),r&&i&&(0,o.jsx)(tR,{})]})})}function tX(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,nodeId:s}=e,l="config-".concat(s,"-").concat(t.name),c=(t.options||[]).map(e=>"string"==typeof e?{value:e,label:e}:e);return(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)(tU.l6,{value:null!=n?n:"",onValueChange:e=>a(t.name,e),disabled:r,children:[(0,o.jsx)(tU.bq,{id:l,className:(0,d.cn)("bg-background/50 h-8 text-xs",r&&"opacity-50"),children:(0,o.jsx)(tU.yv,{placeholder:"Select ".concat(t.display_name,"...")})}),(0,o.jsx)(tU.gC,{children:c.map(e=>(0,o.jsx)(tU.eb,{value:e.value,className:"text-xs",children:e.label},e.value))})]}),r&&i&&(0,o.jsx)(tR,{})]})}function t$(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,connectionInfo:s,nodeId:l}=e,c="config-".concat(l,"-").concat(t.name),u=(null==s?void 0:s.sourceNodeLabel)?"from ".concat(s.sourceNodeLabel):(null==s?void 0:s.sourceNodeId)?"from ".concat(s.sourceNodeId):"";return(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)("div",{className:"mt-1 flex items-center space-x-2",children:[(0,o.jsx)("div",{className:"flex-grow",children:(0,o.jsx)(h.p,{id:c,type:"text",value:i?"Connected ".concat(u):"Connection handle (not connected)",className:(0,d.cn)("bg-background/50 h-8 text-xs",i?"border-blue-500/30 opacity-50":"opacity-50"),disabled:!0,readOnly:!0})}),(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)(w,{variant:i?"success":"outline",className:"h-5 px-2 text-[9px]",children:i?"Connected":"Not Connected"})})]}),t.info&&(0,o.jsx)("p",{className:"text-muted-foreground mt-1 text-xs",children:t.info})]})}function tZ(e){let{inputDef:t,value:n,onChange:a,isDisabled:r=!1,isConnected:i=!1,connectionInfo:s,nodeId:l}=e,c="config-".concat(l,"-").concat(t.name);switch(t.input_type){case"string":case"password":return(0,o.jsx)(tL,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,nodeId:l});case"int":case"float":return(0,o.jsx)(tP,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,nodeId:l});case"multiline":case"code":return(0,o.jsx)(tA,{inputDef:t,value:n,children:(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(ea.T,{id:c,value:null!=n?n:"",onChange:e=>a(t.name,e.target.value),placeholder:"Enter ".concat(t.display_name,"..."),className:(0,d.cn)("bg-background/50 mt-1 text-xs",r&&"opacity-50"),rows:4,disabled:r}),r&&i&&(0,o.jsx)(tR,{})]})});case"bool":return(0,o.jsx)(tq,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,nodeId:l});case"button":return(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)(ek.$,{id:c,onClick:()=>a(t.name,!0),disabled:r,className:"h-8 w-full text-xs",variant:"default",children:t.button_text||t.display_name||"Click"})});case"select":case"dropdown":return(0,o.jsx)(tX,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,nodeId:l});case"object":case"dict":case"json":return(0,o.jsx)(tK,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,nodeId:l});case"list":case"array":return(0,o.jsx)(tY,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,nodeId:l});case"credential":return(0,o.jsxs)("div",{className:"mt-1 space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(tM,{id:"".concat(c,"-use-credential"),checked:(null==n?void 0:n.use_credential_id)||!1,onCheckedChange:e=>{a(t.name,{...n||{},use_credential_id:e})},disabled:r,className:r?"opacity-50":""}),(0,o.jsx)(eo.J,{htmlFor:"".concat(c,"-use-credential"),className:"text-xs",children:"Use credential from secure storage"})]}),(null==n?void 0:n.use_credential_id)?(0,o.jsx)("p",{className:"text-muted-foreground text-xs",children:"Credential selection UI would go here"}):(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)(h.p,{id:"".concat(c,"-value"),type:"password",value:(null==n?void 0:n.value)||"",onChange:e=>{a(t.name,{...n||{},value:e.target.value})},placeholder:"Enter ".concat(t.display_name,"..."),className:(0,d.cn)("bg-background/50 h-8 text-xs",r&&"opacity-50"),disabled:r}),r&&i&&(0,o.jsx)(tR,{})]})]});case"dynamic_handle":return(0,o.jsx)(tW,{inputDef:t,currentValue:n,onChange:(e,t)=>a(e,t),isDisabled:r,isConnected:i,minInputs:t.min_handles||0,maxInputs:t.max_handles||10,defaultInputs:t.default_handles||1});case"handle":return(0,o.jsx)(t$,{inputDef:t,value:n,onChange:a,isDisabled:r,isConnected:i,connectionInfo:s,nodeId:l});default:return(0,o.jsxs)("p",{className:"mt-1 text-xs text-red-500",children:["Unsupported input type: ",t.input_type]})}}function tQ(e){let{inputDef:t,node:n,onConfigChange:a,isInputConnected:r,shouldDisableInput:i,getConnectionInfo:s}=e;return(0,o.jsxs)("div",{className:"pb-2",children:[(0,o.jsxs)(eo.J,{htmlFor:"config-".concat(null==n?void 0:n.id,"-").concat(t.name),className:"flex items-center justify-between text-xs font-medium",children:[(0,o.jsx)("span",{children:t.display_name}),t.required&&(0,o.jsx)(w,{variant:"destructive",className:"h-4 px-1 text-[9px]",children:"Required"})]}),t.info&&(0,o.jsx)("p",{className:"text-muted-foreground mt-0.5 mb-1.5 text-xs",children:t.info}),(0,o.jsx)(tZ,{inputDef:t,value:function(e,t){var n;if("mcp"===e.data.type){let n=tt.LN.getState().getValue(e.id,"config",{});if(void 0!==n[t.name])return n[t.name]}let o=null===(n=e.data.config)||void 0===n?void 0:n[t.name];return void 0!==o?o:void 0!==t.value?t.value:function(e){switch(e){case"bool":return!1;case"int":case"float":return 0;default:return""}}(t.input_type)}(n,t),onChange:a,isDisabled:i(t.name),isConnected:r(t.name),connectionInfo:s(t.name),nodeId:n.id})]})}function t0(e){var t,n,a,r,i,s,l;let{node:c,onConfigChange:d,isInputConnected:u,shouldDisableInput:p,getConnectionInfo:m}=e;return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-sm font-medium",children:"Basic Settings"}),null===(t=c.data.definition)||void 0===t?void 0:t.inputs.filter(e=>!e.is_handle&&["url","method"].includes(e.name)).map(e=>(0,o.jsx)(tQ,{inputDef:e,node:c,onConfigChange:d,isInputConnected:u,shouldDisableInput:p,getConnectionInfo:m},e.name))]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("h3",{className:"text-sm font-medium",children:"Request Parameters"}),(null===(n=c.data.config)||void 0===n?void 0:n.method)&&(0,o.jsx)(w,{variant:(()=>{let e=c.data.config.method;return"GET"===e?"info":"POST"===e?"success":"PUT"===e||"PATCH"===e?"warning":"DELETE"===e?"destructive":"outline"})(),className:"text-[10px]",children:c.data.config.method})]}),null===(a=c.data.definition)||void 0===a?void 0:a.inputs.filter(e=>"query_params"===e.name).map(e=>(0,o.jsx)(tQ,{inputDef:e,node:c,onConfigChange:d,isInputConnected:u,shouldDisableInput:p,getConnectionInfo:m},e.name)),["POST","PUT","PATCH"].includes(null===(r=c.data.config)||void 0===r?void 0:r.method)&&(null===(i=c.data.definition)||void 0===i?void 0:i.inputs.filter(e=>"body"===e.name).map(e=>tC(e,c,c.data.config||{})?(0,o.jsx)(tQ,{inputDef:e,node:c,onConfigChange:d,isInputConnected:u,shouldDisableInput:p,getConnectionInfo:m},e.name):null)),null===(s=c.data.definition)||void 0===s?void 0:s.inputs.filter(e=>"headers"===e.name).map(e=>(0,o.jsx)(tQ,{inputDef:e,node:c,onConfigChange:d,isInputConnected:u,shouldDisableInput:p,getConnectionInfo:m},e.name))]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-sm font-medium",children:"Advanced Options"}),null===(l=c.data.definition)||void 0===l?void 0:l.inputs.filter(e=>!e.is_handle&&!["url","method","query_params","headers","body"].includes(e.name)).map(e=>tC(e,c,c.data.config||{})?(0,o.jsx)(tQ,{inputDef:e,node:c,onConfigChange:d,isInputConnected:u,shouldDisableInput:p,getConnectionInfo:m},e.name):null)]})]})}function t1(e){var t,n,a;let{node:r,onConfigChange:i,isInputConnected:s,shouldDisableInput:l,getConnectionInfo:c}=e;return(0,o.jsxs)("div",{className:"space-y-4",children:[null===(t=r.data.definition)||void 0===t?void 0:t.inputs.map(e=>tC(e,r,r.data.config||{})?(0,o.jsx)(tQ,{inputDef:e,node:r,onConfigChange:i,isInputConnected:s,shouldDisableInput:l,getConnectionInfo:c},e.name):null),null===(a=r.data.config)||void 0===a?void 0:null===(n=a.inputs)||void 0===n?void 0:n.filter(e=>{var t,n;return!(null===(n=r.data.definition)||void 0===n?void 0:null===(t=n.inputs)||void 0===t?void 0:t.some(t=>t.name===e.name))}).map(e=>tC(e,r,r.data.config||{})?(0,o.jsx)(tQ,{inputDef:e,node:r,onConfigChange:i,isInputConnected:s,shouldDisableInput:l,getConnectionInfo:c},e.name):null)]})}function t2(e){var t;let{node:n,onLabelChange:a,onConfigChange:r,isInputConnected:i,shouldDisableInput:s,getConnectionInfo:l}=e;return(0,o.jsx)(tk,{className:"h-full flex-grow overflow-auto p-4",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)(eo.J,{htmlFor:"nodeLabel",className:"text-sm font-medium",children:"Node Label"}),(0,o.jsx)(h.p,{id:"nodeLabel",value:n.data.label,onChange:a,className:"bg-background/50 mt-1 h-9 text-sm"})]}),(0,o.jsx)(L,{}),(null===(t=n.data.definition)||void 0===t?void 0:t.inputs)&&(0,o.jsx)("div",{className:"space-y-6",children:"ApiRequestNode"===n.data.type?(0,o.jsx)(t0,{node:n,onConfigChange:r,isInputConnected:i,shouldDisableInput:s,getConnectionInfo:l}):(0,o.jsx)(t1,{node:n,onConfigChange:r,isInputConnected:i,shouldDisableInput:s,getConnectionInfo:l})})]})})}function t5(e){var t;let{node:n,onDefinitionChange:a}=e;return(0,o.jsx)(tk,{className:"h-full flex-grow overflow-auto p-4",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"mb-3 text-sm font-medium",children:"Advanced Settings"}),(0,o.jsxs)("div",{className:"mb-4 space-y-2",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)(eo.J,{htmlFor:"requiresApproval",className:"text-xs font-medium",children:"Requires Approval"}),(0,o.jsx)(tM,{id:"requiresApproval",checked:(null===(t=n.data.definition)||void 0===t?void 0:t.requires_approval)||!1,onCheckedChange:e=>a("requires_approval",e)})]}),(0,o.jsx)("p",{className:"text-muted-foreground text-xs",children:"When enabled, this component will require approval before execution. This setting is stored in the component definition."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)(eo.J,{htmlFor:"jsonConfig",className:"text-xs font-medium",children:"Raw Configuration"}),(0,o.jsx)(ea.T,{id:"jsonConfig",value:JSON.stringify(n.data.config||{},null,2),className:"bg-background/50 mt-1.5 font-mono text-xs",rows:10,readOnly:!0})]})]})})}function t4(){let{selectedNode:e,activeTab:t,setActiveTab:n,handleLabelChange:a,handleConfigChange:r,handleDefinitionChange:i,isInputConnected:s,shouldDisableInput:l,getConnectionInfo:c}=tw();return e?(0,o.jsxs)(es,{value:t,onValueChange:e=>n(e),className:"flex flex-grow flex-col overflow-hidden",children:[(0,o.jsxs)(el,{className:"mx-4 mt-2 grid grid-cols-3",children:[(0,o.jsxs)(ec,{value:"settings",className:"text-xs",children:[(0,o.jsx)(e2.A,{className:"mr-1.5 h-3.5 w-3.5"})," Settings"]}),(0,o.jsxs)(ec,{value:"info",className:"text-xs",children:[(0,o.jsx)(eh.A,{className:"mr-1.5 h-3.5 w-3.5"})," Info"]}),(0,o.jsxs)(ec,{value:"advanced",className:"text-xs",children:[(0,o.jsx)(tN.A,{className:"mr-1.5 h-3.5 w-3.5"})," Advanced"]})]}),(0,o.jsx)(ed,{value:"settings",className:"flex h-full flex-grow flex-col overflow-hidden",children:(0,o.jsx)(t2,{node:e,onLabelChange:a,onConfigChange:r,isInputConnected:s,shouldDisableInput:l,getConnectionInfo:c})}),(0,o.jsx)(ed,{value:"info",className:"flex h-full flex-grow flex-col overflow-hidden",children:(0,o.jsx)(t_,{node:e})}),(0,o.jsx)(ed,{value:"advanced",className:"flex h-full flex-grow flex-col overflow-hidden",children:(0,o.jsx)(t5,{node:e,onDefinitionChange:i})})]}):null}function t3(){let{selectedNode:e,onClose:t,onDeleteNode:n,validateAllNodeInputs:a,applyAllChanges:r}=tw();return e?(0,o.jsxs)(tc,{className:"flex flex-shrink-0 justify-between border-t p-4",children:[(0,o.jsxs)("div",{className:"flex gap-2",children:["StartNode"!==e.data.originalType&&(0,o.jsxs)(ek.$,{variant:"destructive",size:"sm",onClick:()=>n(e.id),className:"gap-1",children:[(0,o.jsx)(eJ.A,{className:"h-4 w-4"})," Delete"]}),(0,o.jsx)(eW,{children:(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsxs)(ek.$,{variant:"secondary",size:"sm",onClick:a,className:"gap-1 border-2 border-yellow-500/30 bg-yellow-500/10 text-yellow-700 hover:bg-yellow-500/20 dark:text-yellow-400",children:[(0,o.jsx)(em.A,{className:"h-4 w-4"})," Validate Node"]})}),(0,o.jsx)(eP,{children:(0,o.jsxs)("p",{children:["Manually validate this node's configuration.",(0,o.jsx)("br",{}),"Fields are not validated during editing."]})})]})})]}),(0,o.jsx)(eW,{children:(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsxs)(ek.$,{variant:"default",size:"sm",onClick:()=>{r(),t()},className:"gap-1",children:[(0,o.jsx)(eB.A,{className:"h-4 w-4"})," Apply"]})}),(0,o.jsx)(eP,{children:(0,o.jsxs)("p",{children:["Apply all changes to the node configuration.",(0,o.jsx)("br",{}),"Changes are also applied automatically as you type."]})})]})})]}):null}function t8(){return(0,o.jsxs)("div",{className:"text-muted-foreground flex h-full flex-col items-center justify-center p-8 text-center","data-testid":"inspector-empty-state",children:[(0,o.jsx)(eh.A,{className:"text-muted-foreground/50 mb-2 h-8 w-8"}),(0,o.jsx)("p",{children:"Select a node to inspect and configure its properties."})]})}function t9(e){let{title:t,message:n,isOpen:r,onClose:i,preserveState:s=!0}=e,l=a.useCallback(()=>{i()},[i]);return(0,o.jsx)(en.lG,{open:r,onOpenChange:e=>!e&&l(),children:(0,o.jsxs)(en.Cf,{className:"sm:max-w-[425px]",children:[(0,o.jsx)(en.c7,{children:(0,o.jsx)(en.L3,{children:t})}),(0,o.jsx)("div",{className:"py-4",children:(0,o.jsx)("p",{className:"text-muted-foreground text-sm",children:n})}),(0,o.jsx)(en.Es,{children:(0,o.jsx)(ek.$,{onClick:l,className:"w-full",children:"OK"})})]})})}function t6(){let{notification:e,setNotification:t}=tw();return(0,o.jsx)(t9,{isOpen:e.isOpen,title:e.title,message:e.message,preserveState:e.preserveState,onClose:()=>t({...e,isOpen:!1})})}function t7(e){var t;let{selectedNode:n,onNodeDataChange:r,onClose:i}=e,s=!!n,[l,c]=(0,a.useState)([]),[d,u]=(0,a.useState)(!1);(0,a.useEffect)(()=>{n&&p()},[n]);let p=async()=>{try{u(!0);let e=await (0,eS.fetchCredentials)();c(e.credentials||[])}catch(e){console.error("Error loading credentials:",e)}finally{u(!1)}};return(0,o.jsxs)(tv,{...e,children:[n&&!!n&&!!("mcp"===n.data.type||"MCPToolsComponent"===n.data.originalType||n.data.definition&&n.data.definition.mcp_info||n.data.definition&&n.data.definition.path&&n.data.definition.path.includes("mcp_marketplace")||n.data.definition&&n.data.definition.display_name&&(n.data.definition.display_name.includes("Script Generator")||n.data.definition.display_name.includes("Generator"))||n.data.label&&(n.data.label.includes("Script Generator")||n.data.label.includes("Generator")))&&(0,o.jsx)(tn,{node:n,onNodeDataChange:r}),(0,o.jsx)(ta,{open:s,onClose:i,direction:"right",children:(0,o.jsx)(ts,{className:"bg-card/30 mt-0 ml-auto flex h-full w-[400px] flex-col overflow-hidden rounded-none backdrop-blur-sm",children:(null==n?void 0:null===(t=n.data)||void 0===t?void 0:t.definition)?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(ty,{}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)(tm,{variant:"info",className:"mx-4 mt-2 bg-blue-500/10 text-blue-700 dark:text-blue-400",children:[(0,o.jsx)(eh.A,{className:"h-4 w-4"}),(0,o.jsx)(tg,{className:"text-xs",children:'Fields are not validated during editing. Use the "Validate Node" button to check your inputs.'})]}),(0,o.jsxs)(tm,{variant:"success",className:"mx-4 mt-0 bg-green-500/10 text-green-700 dark:text-green-400",children:[(0,o.jsx)(eh.A,{className:"h-4 w-4"}),(0,o.jsx)(tg,{className:"text-xs",children:'Changes are applied immediately as you type. Click "Apply" to explicitly apply all changes.'})]})]}),(0,o.jsx)(t4,{}),(0,o.jsx)(t3,{})]}):(0,o.jsx)(t8,{})})}),(0,o.jsx)(t6,{})]})}let ne=(e,t,n)=>{if("StartNode"===n)return b.A;switch(null==e?void 0:e.toLowerCase()){case"io":return t.includes("input")?b.A:e5.A;case"data":return y.A;case"processing":return N.A;case"api":return j.A;case"control flow":return k.A;case"text":return S.A;case"code":return _.A;default:return A.A}},nt=(0,a.memo)(e=>{var t,n;let{data:r,isConnectable:i,selected:s}=e,{label:l,definition:c,type:d,originalType:u}=r;if(!c)return(0,o.jsx)("div",{className:"bg-destructive/20 text-destructive rounded border p-2 text-xs font-medium",children:"Missing Definition!"});let p=(c.inputs||[]).filter(e=>{if(!e.is_handle)return!1;if(!e.visibility_rules||0===e.visibility_rules.length)return!0;if("dynamic_handle"===e.input_type||"DynamicCombineTextComponent"===d&&e.name.startsWith("input_")&&e.name.endsWith("_handle")||"ConditionalNode"===u&&e.name.startsWith("condition_")&&e.name.endsWith("_input_handle")){var t,n,o,a;if("dynamic_handle"===e.input_type){let n=(null===(t=r.config)||void 0===t?void 0:t.num_handles)||e.default_handles||2,o=e.name.match(new RegExp("".concat(e.base_name,"_(d+)")));return!o||!o[1]||parseInt(o[1],10)<=n}if("DynamicCombineTextComponent"===d&&e.name.startsWith("input_")&&e.name.endsWith("_handle")){let t=e.name.match(/input_(\d+)_handle/);if(t&&t[1])return parseInt(t[1],10)<=parseInt((null===(n=r.config)||void 0===n?void 0:n.num_additional_inputs)||"0",10)}if("ConditionalNode"===u&&e.name.startsWith("condition_")&&e.name.endsWith("_input_handle")){let t=e.name.match(/condition_(\d+)_input_handle/);if(t&&t[1]){let e=parseInt(t[1],10);return e<=2+parseInt((null===(o=r.config)||void 0===o?void 0:o.num_additional_conditions)||"0",10)&&"node_output"===((null===(a=r.config)||void 0===a?void 0:a["condition_".concat(e,"_source")])||"node_output")}}}return e.visibility_rules.some(e=>{var t;return(null===(t=r.config)||void 0===t?void 0:t[e.field_name])===e.field_value})}),m=c.outputs||[];if("ConditionalNode"===u){let e=2+parseInt((null===(n=r.config)||void 0===n?void 0:n.num_additional_conditions)||"0",10),t=[];for(let n=1;n<=e;n++)t.push({name:"condition_".concat(n,"_output"),display_name:"Condition ".concat(n),output_type:"Any",info:"Outputs data when condition ".concat(n," matches")});t.push({name:"default_output",display_name:"Default",output_type:"Any",info:"Outputs data when no conditions match"}),m=t}let f=ne(c.category,d,u),g="StartNode"===u,h=(null===(t=r.definition)||void 0===t?void 0:t.requires_approval)===!0;return(0,o.jsx)(eW,{delayDuration:150,children:(0,o.jsxs)(er.Zp,{className:"workflow-node relative w-52 overflow-visible transition-all duration-200 ".concat(s?"ring-primary scale-105 shadow-lg ring-2":"shadow-md hover:shadow-lg"," ").concat(g?"border-primary bg-primary/5 border-2":""," ").concat(h?"border-2 border-[#3F72AF]/50":""),style:{minHeight:"".concat(Math.max(90,35+20*Math.max(p.length,m.length)),"px"),height:"auto",zIndex:10},children:[(0,o.jsxs)(er.aR,{className:"flex flex-row items-center gap-1.5 space-y-0 border-b p-2",children:[(0,o.jsx)("div",{className:"bg-primary/10 rounded-md p-1",children:(0,o.jsx)(f,{className:"text-primary h-3.5 w-3.5"})}),(0,o.jsxs)("div",{className:"flex-1 overflow-hidden",children:[(0,o.jsxs)(er.ZB,{className:"truncate text-xs leading-tight font-medium",children:[l,h&&(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsx)("span",{className:"ml-1 inline-flex",children:(0,o.jsx)(ew.A,{className:"h-3 w-3 text-[#3F72AF]"})})}),(0,o.jsxs)(eP,{side:"top",className:"p-2 text-xs",children:[(0,o.jsx)("div",{className:"font-medium",children:"Requires Approval"}),(0,o.jsx)("div",{className:"text-muted-foreground text-[10px]",children:"This node requires approval before execution"})]})]})]}),(0,o.jsx)(er.BT,{className:"truncate text-[9px] leading-tight",children:c.display_name})]})]}),(0,o.jsxs)(er.Wu,{className:"flex flex-col gap-2 p-2 text-xs",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)(w,{variant:"outline",className:"bg-muted/50 h-5 px-1.5 py-0 text-[9px]",children:c.category}),(0,o.jsxs)("div",{className:"flex items-center gap-1",children:[p.length>0&&(0,o.jsxs)("span",{className:"text-muted-foreground flex items-center gap-0.5 text-[9px]",children:[(0,o.jsx)("span",{className:"bg-primary/40 h-1.5 w-1.5 rounded-full"}),p.length]}),m.length>0&&(0,o.jsxs)("span",{className:"text-muted-foreground flex items-center gap-0.5 text-[9px]",children:[(0,o.jsx)("span",{className:"bg-primary/40 h-1.5 w-1.5 rounded-full"}),m.length]})]})]}),h&&(0,o.jsxs)(w,{variant:"warning",className:"flex h-5 w-fit items-center gap-1 px-1.5 py-0 text-[9px]",children:[(0,o.jsx)(ew.A,{className:"h-3 w-3"}),"Requires Approval"]})]}),p.map((e,t)=>{var n;return(0,o.jsx)(a.Fragment,{children:(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsxs)("div",{className:"group",children:[(0,o.jsx)(B.h7,{type:"target",position:B.yX.Left,id:e.name,className:"!bg-primary hover:!bg-primary/80 transition-all duration-200",style:{top:"".concat(35+20*t,"px"),width:"10px",height:"10px",borderRadius:"5px",border:"2px solid var(--background)",left:"-5px",zIndex:50},isConnectable:i}),(0,o.jsx)("div",{className:"bg-primary/30 absolute h-[2px]",style:{top:"".concat(35+20*t,"px"),left:"5px",width:"5px",transform:"translateY(-50%)"}}),(0,o.jsx)("div",{className:"text-foreground bg-background/95 border-primary/20 pointer-events-none absolute z-30 max-w-[80%] overflow-hidden rounded-sm border px-1.5 py-0.5 text-[9px] font-medium text-ellipsis opacity-0 shadow-sm transition-all duration-150 group-hover:opacity-100",style:{top:"".concat(35+20*t,"px"),left:"10px",transform:"translateY(-50%)"},children:e.display_name})]})}),(0,o.jsxs)(eP,{side:"left",className:"bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm",children:[(0,o.jsx)("div",{className:"font-medium",children:e.display_name}),(0,o.jsxs)("div",{className:"text-muted-foreground text-[10px]",children:["Type: ",(null===(n=e.input_types)||void 0===n?void 0:n.join(", "))||"any"]})]})]})},"input-".concat(e.name))}),m.map((e,t)=>(0,o.jsx)(a.Fragment,{children:(0,o.jsxs)(eV,{children:[(0,o.jsx)(eL,{asChild:!0,children:(0,o.jsxs)("div",{className:"group",children:[(0,o.jsx)(B.h7,{type:"source",position:B.yX.Right,id:e.name,className:"!bg-primary hover:!bg-primary/80 transition-all duration-200",style:{top:"".concat(35+20*t,"px"),width:"10px",height:"10px",borderRadius:"5px",border:"2px solid var(--background)",right:"-5px",zIndex:50},isConnectable:i}),(0,o.jsx)("div",{className:"bg-primary/30 absolute h-[2px]",style:{top:"".concat(35+20*t,"px"),right:"5px",width:"5px",transform:"translateY(-50%)"}}),(0,o.jsx)("div",{className:"text-foreground bg-background/95 border-primary/20 pointer-events-none absolute z-30 max-w-[80%] overflow-hidden rounded-sm border px-1.5 py-0.5 text-right text-[9px] font-medium text-ellipsis opacity-0 shadow-sm transition-all duration-150 group-hover:opacity-100",style:{top:"".concat(35+20*t,"px"),right:"10px",transform:"translateY(-50%)"},children:e.display_name})]})}),(0,o.jsxs)(eP,{side:"right",className:"bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm",children:[(0,o.jsx)("div",{className:"font-medium",children:e.display_name}),(0,o.jsxs)("div",{className:"text-muted-foreground text-[10px]",children:["Type: ",e.output_type]})]})]})},"output-".concat(e.name)))]})})});nt.displayName="WorkflowNode";let nn=e=>{var t,n;let o=e.data;switch((null==o?void 0:null===(n=o.definition)||void 0===n?void 0:null===(t=n.category)||void 0===t?void 0:t.toLowerCase())||""){case"io":return"var(--chart-1)";case"data":return"var(--chart-2)";case"processing":return"var(--chart-3)";case"api":return"var(--chart-4)";case"control flow":return"var(--chart-5)";default:return"var(--primary)"}},no=()=>({id:"start-node",type:"WorkflowNode",position:{x:100,y:100},data:{label:"Start",type:"component",originalType:"StartNode",definition:{name:"StartNode",display_name:"Start",description:"The starting point for all workflows. Only nodes connected to this node will be executed.",category:"Input/Output",icon:"Play",beta:!1,inputs:[],outputs:[{name:"flow",display_name:"Flow",output_type:"Any"}],is_valid:!0,path:"components.io.start_node"},config:{collected_parameters:{}}}}),na=[no()],nr=[],ni=[.5,.5],ns={WorkflowNode:nt},nl={default:e=>{let{id:t,sourceX:n,sourceY:r,targetX:i,targetY:s,sourcePosition:l,targetPosition:c,style:d={},markerEnd:u,selected:p}=e,{setEdges:m}=(0,B.VH)(),[f,g,h]=(0,B.Fp)({sourceX:n,sourceY:r,sourcePosition:l,targetX:i,targetY:s,targetPosition:c}),x=(0,a.useCallback)(e=>{e.stopPropagation(),m(e=>e.filter(e=>e.id!==t))},[t,m]),v={strokeWidth:p?3:(null==d?void 0:d.strokeWidth)||2,stroke:p?"#ff6b6b":(null==d?void 0:d.stroke)||"var(--primary)",fill:"none",...d};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("path",{id:t,d:f,style:v,className:"react-flow__edge-path",markerEnd:u}),(0,o.jsx)(B.rV,{children:(0,o.jsx)("div",{style:{position:"absolute",transform:"translate(-50%, -50%) translate(".concat(g,"px,").concat(h,"px)"),pointerEvents:"all",zIndex:1e3},className:"nodrag nopan",children:(0,o.jsx)("button",{className:"flex h-3 w-3 items-center justify-center rounded-full bg-white text-gray-600 shadow-sm transition-all duration-200 hover:scale-110 hover:bg-gray-100",onClick:x,title:"Delete edge",type:"button",children:(0,o.jsx)(eJ.A,{className:"h-2 w-2"})})})})]})}};function nc(e){let{onFlowChange:t,initialNodes:n,initialEdges:r}=e,i=(0,a.useRef)(null),{screenToFlowPosition:s,fitView:l}=(0,B.VH)(),[c,d]=(0,a.useState)(n||na),[u,p]=(0,a.useState)(()=>(r||nr).map(e=>({...e,type:e.type||"default"})));(0,a.useEffect)(()=>{n&&(console.log("Updating nodes from props:",n),n.some(e=>"StartNode"===e.data.originalType)?d(n):(console.log("Adding StartNode to initial nodes"),d([no(),...n])))},[n]),(0,a.useEffect)(()=>{if(r){console.log("Updating edges from props:",r);let e=r.map(e=>({...e,type:e.type||"default"}));console.log("Edges with type:",e),p(e)}},[r]);let[m,f]=(0,a.useState)(null),g=(0,a.useRef)(c),h=(0,a.useRef)(u);(0,a.useEffect)(()=>{console.log("[WorkflowCanvas] Current state:",{nodesLength:c.length,edgesLength:u.length,hasStartNode:c.some(e=>"StartNode"===e.data.originalType)}),c.length>0&&!c.some(e=>"StartNode"===e.data.originalType)&&(console.warn("[WorkflowCanvas] No StartNode found in current nodes. Adding one."),d(e=>[no(),...e])),0===c.length&&(console.warn("[WorkflowCanvas] Nodes array is empty. Reinitializing with StartNode."),d([no()]))},[c,d]);let x=(0,a.useRef)(!1),v=(0,a.useCallback)(e=>{x.current=e},[]);(0,a.useEffect)(()=>{c===g.current&&u===h.current||x.current?x.current&&(g.current=c,h.current=u):(g.current=c,h.current=u,t(c,u))},[c,u,t]),(0,a.useEffect)(()=>{c.length>0&&setTimeout(()=>{l({padding:.2})},100)},[c,l]);let w=(0,a.useCallback)(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Set;if(n.has(e))return n;for(let o of(n.add(e),t.filter(t=>t.source===e)))w(o.target,t,n);return n},[]),b=(0,a.useCallback)(()=>{let e=c.find(e=>"StartNode"===e.data.originalType);return e?w(e.id,u):new Set},[c,u,w]),y=(0,a.useCallback)(()=>{if(0===c.length)return;let e=b();d(t=>t.some(t=>{var n,o;if("StartNode"===t.data.originalType)return!1;let a=e.has(t.id),r=null===(n=t.style)||void 0===n?void 0:n.opacity,i=null===(o=t.style)||void 0===o?void 0:o.border;return a&&1!==r||!a&&.5!==r||a&&void 0!==i||!a&&"1px dashed #3F72AF"!==i})?t.map(t=>{if("StartNode"===t.data.originalType)return t;let n=e.has(t.id);return{...t,style:{...t.style,opacity:n?1:.5,border:n?void 0:"1px dashed #3F72AF"}}}):t)},[c,u,b]);(0,a.useEffect)(()=>{y()},[y]);let N=(0,a.useCallback)(e=>{let t=e.filter(e=>{if("remove"===e.type){let t=e.id,n=c.find(e=>e.id===t);if(n&&"StartNode"===n.data.originalType)return console.log("Prevented deletion of StartNode"),!1}return!0});d(e=>(0,B._0)(t,e))},[c]),j=(0,a.useCallback)(e=>p(t=>(0,B.zW)(e,t)),[]),k=(0,a.useCallback)(e=>p(t=>(0,B.rN)({...e,type:"default"},t)),[]),S=(0,a.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),_=(0,a.useCallback)(e=>{if(e.preventDefault(),!i.current)return;let t=e.dataTransfer.getData("application/reactflow");if(!t)return;let{nodeType:n,definition:o}=JSON.parse(t);console.log("Dropped Node Definition Outputs:",o.outputs);let a=i.current.getBoundingClientRect();console.log("Event ClientX:",e.clientX,"ClientY:",e.clientY),console.log("Canvas Bounds:",a);let r=s({x:e.clientX-a.left,y:e.clientY-a.top});console.log("Computed position:",r);let l="MCP"===o.type||o.path&&o.path.includes("mcp_marketplace")||"MCPToolsComponent"===n,c={id:"".concat(n,"-").concat(+new Date),type:"WorkflowNode",position:r,data:{label:o.display_name,type:l?"mcp":"component",originalType:n,definition:o,config:{}}};d(e=>e.concat(c))},[s,d]),E=(0,a.useCallback)((e,t)=>{f(t),console.log("Selected Node:",t)},[]),C=()=>{f(null)},I=(0,a.useCallback)(e=>{let t=c.find(t=>t.id===e);if(t&&"StartNode"===t.data.originalType){console.log("Cannot delete StartNode");return}let n=c.find(e=>"StartNode"===e.data.originalType);if(n&&n.data.config&&n.data.config.collected_parameters){console.log("Cleaning up StartNode parameters for deleted node ".concat(e));let t={...n.data.config.collected_parameters};Object.keys(t).forEach(n=>{n.startsWith("".concat(e,"_"))&&(console.log("Removing parameter ".concat(n," from StartNode collected parameters")),delete t[n])}),d(e=>e.map(e=>e.id===n.id?{...e,data:{...e.data,config:{...e.data.config,collected_parameters:t}}}:e));{let t=window.startNodeCollectedParameters||{};Object.keys(t).forEach(n=>{n.startsWith("".concat(e,"_"))&&delete t[n]}),window.startNodeCollectedParameters=t,console.log("Updated global startNodeCollectedParameters after node deletion:",window.startNodeCollectedParameters)}}d(t=>t.filter(t=>t.id!==e)),p(t=>t.filter(t=>t.source!==e&&t.target!==e)),f(null)},[c,d,p]),A=(0,a.useRef)(null),D=(0,a.useCallback)((e,t)=>{let n=Date.now(),o=JSON.stringify(t),a=A.current;if(!a||a.nodeId!==e||a.dataJson!==o||!(n-a.timestamp<50)){if(A.current={nodeId:e,dataJson:o,timestamp:n},!c.some(t=>t.id===e)){console.warn("Attempted to update non-existent node: ".concat(e));return}d(n=>{let a=n.findIndex(t=>t.id===e);if(-1===a)return n;let r=n[a];if(JSON.stringify(r.data)===o)return n;let i=[...n];return i[a]={...r,data:t},i}),f(n=>n&&n.id===e&&JSON.stringify(n.data)!==o?{...n,data:t}:n)}},[c,d]);(0,a.useEffect)(()=>{let e=e=>{"Delete"!==e.key&&"Backspace"!==e.key||!m||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||I(m.id)};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[m,I]);let[O,T]=(0,a.useState)(null),F=(0,a.useCallback)(()=>{m&&T(m)},[m]),R=(0,a.useCallback)(()=>{if(O){let e=O.data.originalType||O.data.type,t="".concat(e,"-").concat(+new Date),n={x:O.position.x+50,y:O.position.y+50},o={...O,id:t,position:n,selected:!1,data:{...O.data}};d(e=>e.concat(o))}},[O,d]);return(0,o.jsxs)("div",{className:"relative h-full w-full flex-grow overflow-hidden",ref:i,children:[(0,o.jsxs)(B.Gc,{nodes:c,edges:u,onNodesChange:N,onEdgesChange:j,onConnect:k,onDrop:_,onDragOver:S,onNodeClick:E,onPaneClick:C,nodeTypes:ns,edgeTypes:nl,fitView:!0,nodeOrigin:ni,className:"bg-background",snapToGrid:!0,snapGrid:[20,20],defaultEdgeOptions:{animated:!0,style:{strokeWidth:2,stroke:"var(--primary)",zIndex:5}},zoomOnScroll:!0,zoomOnPinch:!0,panOnScroll:!0,elementsSelectable:!0,selectNodesOnDrag:!1,children:[(0,o.jsx)(e8.H,{showInteractive:!1,className:"bg-card rounded-lg border p-1 shadow-md"}),(0,o.jsxs)(B.Zk,{position:"top-right",className:"mt-2 mr-2 flex gap-2",children:[(0,o.jsx)("button",{type:"button",className:"bg-card hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border",onClick:F,title:"Copy Node (Ctrl+C)",children:(0,o.jsx)(e7.A,{className:"h-4 w-4"})}),(0,o.jsx)("button",{type:"button",className:"bg-card hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border",onClick:R,title:"Paste Node (Ctrl+V)",children:(0,o.jsx)(te.A,{className:"h-4 w-4"})}),(0,o.jsx)("button",{type:"button",className:"bg-card hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md border",onClick:()=>m&&I(m.id),disabled:!m,title:"Delete Node (Delete)",children:(0,o.jsx)(eJ.A,{className:"h-4 w-4"})})]}),(0,o.jsx)(e9.o,{nodeStrokeWidth:3,zoomable:!0,pannable:!0,nodeColor:nn,className:"bg-card/80 rounded-lg border shadow-md backdrop-blur-sm"}),(0,o.jsx)(e6.V,{variant:e6._.Dots,gap:20,size:1,color:"var(--border)",className:"bg-background/50"})]}),(0,o.jsx)(t7,{selectedNode:m,onNodeDataChange:D,onClose:C,onDeleteNode:I,edges:u,nodes:c,setIsEditingField:v})]})}let nd=a.memo(function(e){let{onFlowChange:t,initialWorkflow:n}=e,[r,i]=(0,a.useState)(0),s=(0,a.useRef)(null),l=(0,a.useCallback)((e,n)=>{t(e,n)},[t]);return(0,a.useEffect)(()=>{if(!n)return;if(!s.current||s.current===n){s.current=n;return}let e=s.current.nodes,t=n.nodes,o=s.current.edges,a=n.edges;(e.length!==t.length||o.length!==a.length)&&(console.log("Initial workflow changed significantly, forcing re-render"),i(e=>e+1)),s.current=n},[n]),(0,o.jsx)(B.Ln,{children:(0,o.jsx)(nc,{onFlowChange:l,initialNodes:null==n?void 0:n.nodes,initialEdges:null==n?void 0:n.edges})},r)},(e,t)=>{if(!e.initialWorkflow&&!t.initialWorkflow)return!0;if(!e.initialWorkflow||!t.initialWorkflow)return!1;if(e.initialWorkflow===t.initialWorkflow)return!0;let n=e.initialWorkflow.nodes,o=t.initialWorkflow.nodes,a=e.initialWorkflow.edges,r=t.initialWorkflow.edges;return n.length===o.length&&a.length===r.length});function nu(e){if(!e||"object"!=typeof e)return{isValid:!1,error:"Workflow data must be a valid JSON object"};if(e.workflow_name&&"string"!=typeof e.workflow_name)return{isValid:!1,error:"Workflow name must be a string"};if(!Array.isArray(e.nodes))return{isValid:!1,error:'Workflow must contain a "nodes" array'};if(!Array.isArray(e.edges)&&!Array.isArray(e.connections))return{isValid:!1,error:'Workflow must contain an "edges" array'};let t=e.edges||e.connections;for(let t=0;t<e.nodes.length;t++){let n=e.nodes[t];if(!n.id)return{isValid:!1,error:"Node at index ".concat(t,' is missing an "id" property')};if(!n.type)return{isValid:!1,error:"Node at index ".concat(t,' is missing a "type" property')};if(!n.position||"object"!=typeof n.position||"number"!=typeof n.position.x||"number"!=typeof n.position.y)return{isValid:!1,error:"Node at index ".concat(t,' has an invalid "position" property')};if(!n.data||"object"!=typeof n.data)return{isValid:!1,error:"Node at index ".concat(t,' is missing a valid "data" property')};if(!n.data.type)return{isValid:!1,error:"Node at index ".concat(t,' is missing a "data.type" property')};if(!n.data.label)return{isValid:!1,error:"Node at index ".concat(t,' is missing a "data.label" property')};if(!n.data.definition||"object"!=typeof n.data.definition)return{isValid:!1,error:"Node at index ".concat(t,' is missing a valid "data.definition" property')}}for(let n=0;n<t.length;n++){let o=t[n];if(!o.id)return{isValid:!1,error:"Edge at index ".concat(n,' is missing an "id" property')};if(!o.source)return{isValid:!1,error:"Edge at index ".concat(n,' is missing a "source" property')};if(!o.target)return{isValid:!1,error:"Edge at index ".concat(n,' is missing a "target" property')};if(!e.nodes.some(e=>e.id===o.source))return{isValid:!1,error:"Edge at index ".concat(n," references a non-existent source node: ").concat(o.source)};if(!e.nodes.some(e=>e.id===o.target))return{isValid:!1,error:"Edge at index ".concat(n," references a non-existent target node: ").concat(o.target)}}return{isValid:!0,data:e}}var np=n(93885),nm=n(4982);async function nf(e){try{return(await nm.Ek.get(e,{headers:{"Content-Type":"application/json"}})).data}catch(e){if(e.response)throw Error("Failed to fetch workflow data: ".concat(e.response.status," ").concat(e.response.statusText));throw e}}function ng(e){let{isOpen:t,onClose:n,onLoadWorkflow:r}=e,[i,s]=(0,a.useState)("upload"),[l,c]=(0,a.useState)(""),[d,u]=(0,a.useState)(null),[p,m]=(0,a.useState)(null),[f,g]=(0,a.useState)(!1),x=(0,a.useRef)(null),v=()=>{c(""),u(null),m(null),g(!1),s("upload")},w=()=>{v(),n()},b=async e=>{g(!0);try{let t=nu(e);if(!t.isValid){m(t.error||"Invalid workflow data"),g(!1);return}r(e,!0),w()}catch(e){m(e instanceof Error?e.message:"An unknown error occurred")}finally{g(!1)}},y=async()=>{if(!d){m("Please select a file");return}if(!d.name.endsWith(".json")){m("Please select a JSON file");return}g(!0);try{let e,t=await d.text();console.log("File content:",t);try{e=JSON.parse(t),console.log("Parsed file data:",e)}catch(e){console.error("JSON parse error:",e),m("Invalid JSON format"),g(!1);return}if(!e.nodes||!e.edges&&!e.connections){console.error("Missing nodes or edges in workflow data"),m("Invalid workflow format: missing nodes or edges"),g(!1);return}!e.edges&&e.connections&&(console.log("Converting connections to edges for backward compatibility"),e.edges=e.connections),await b(e)}catch(e){console.error("Error in handleLoadFromFile:",e),m(e instanceof Error?e.message:"Failed to read file"),g(!1)}},N=async()=>{if(!l.trim()){m("Please enter JSON data");return}g(!0);try{let e;try{e=JSON.parse(l),console.log("Parsed JSON data:",e)}catch(e){console.error("JSON parse error:",e),m("Invalid JSON format"),g(!1);return}if(!e.nodes||!e.edges&&!e.connections){console.error("Missing nodes or edges in workflow data"),m("Invalid workflow format: missing nodes or edges"),g(!1);return}!e.edges&&e.connections&&(console.log("Converting connections to edges for backward compatibility"),e.edges=e.connections),await b(e)}catch(e){console.error("Error in handleLoadFromJson:",e),m(e instanceof Error?e.message:"Failed to parse JSON"),g(!1)}};return(0,o.jsx)(en.lG,{open:t,onOpenChange:e=>!e&&w(),children:(0,o.jsxs)(en.Cf,{className:"sm:max-w-[550px]",children:[(0,o.jsxs)(en.c7,{children:[(0,o.jsx)(en.L3,{children:"Load Workflow"}),(0,o.jsx)(en.rr,{children:"Load a workflow from a JSON file or paste JSON directly."})]}),(0,o.jsxs)(es,{value:i,onValueChange:s,className:"w-full",children:[(0,o.jsxs)(el,{className:"grid w-full grid-cols-2",children:[(0,o.jsx)(ec,{value:"upload",children:"Upload File"}),(0,o.jsx)(ec,{value:"paste",children:"Paste JSON"})]}),(0,o.jsxs)(ed,{value:"upload",className:"mt-4",children:[(0,o.jsxs)("div",{className:"hover:bg-muted/50 cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors",onClick:()=>{var e;return null===(e=x.current)||void 0===e?void 0:e.click()},onDragOver:e=>{e.preventDefault(),e.stopPropagation()},onDrop:e=>{e.preventDefault(),e.stopPropagation(),e.dataTransfer.files&&e.dataTransfer.files.length>0&&(u(e.dataTransfer.files[0]),m(null))},children:[(0,o.jsx)(eU.A,{className:"text-muted-foreground mx-auto mb-4 h-10 w-10"}),(0,o.jsx)("p",{className:"mb-2 font-medium",children:"Drag and drop a JSON file here"}),(0,o.jsx)("p",{className:"text-muted-foreground mb-4 text-sm",children:"or click to browse"}),(0,o.jsx)(h.p,{ref:x,type:"file",accept:".json",className:"hidden",onChange:e=>{e.target.files&&e.target.files.length>0&&(u(e.target.files[0]),m(null))}}),d&&(0,o.jsxs)("div",{className:"bg-muted mt-2 rounded p-2 text-sm",children:[(0,o.jsx)("p",{className:"font-medium",children:"Selected file:"}),(0,o.jsx)("p",{className:"truncate",children:d.name})]})]}),(0,o.jsx)("button",{type:"button",className:"bg-primary text-primary-foreground hover:bg-primary/90 mt-4 inline-flex h-9 w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium disabled:pointer-events-none disabled:opacity-50",onClick:y,disabled:!d||f,children:f?"Loading...":"Load Workflow"})]}),(0,o.jsxs)(ed,{value:"paste",className:"mt-4",children:[(0,o.jsx)(ea.T,{placeholder:"Paste your workflow JSON here...",className:"min-h-[200px] font-mono text-sm",value:l,onChange:e=>{c(e.target.value),m(null)}}),(0,o.jsx)("button",{type:"button",className:"bg-primary text-primary-foreground hover:bg-primary/90 mt-4 inline-flex h-9 w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium disabled:pointer-events-none disabled:opacity-50",onClick:N,disabled:!l.trim()||f,children:f?"Loading...":"Load Workflow"})]})]}),p&&(0,o.jsxs)(tm,{variant:"destructive",children:[(0,o.jsx)(ep.A,{className:"h-4 w-4"}),(0,o.jsx)(tf,{children:"Error"}),(0,o.jsx)(tg,{children:p})]}),(0,o.jsx)(en.Es,{children:(0,o.jsx)("button",{type:"button",className:"bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium",onClick:w,children:"Cancel"})})]})})}np.vr,np.pr,np.gw;var nh=n(90010);function nx(e){let{isOpen:t,onClose:n,onContinue:a,title:r="Unsaved Changes",description:i="You have unsaved changes that will be lost if you leave this page. Are you sure you want to continue?",continueText:s="Continue without saving",cancelText:l="Cancel"}=e;return(0,o.jsx)(nh.Lt,{open:t,onOpenChange:n,children:(0,o.jsxs)(nh.EO,{children:[(0,o.jsxs)(nh.wd,{children:[(0,o.jsx)(nh.r7,{children:r}),(0,o.jsx)(nh.$v,{children:i})]}),(0,o.jsxs)(nh.ck,{children:[(0,o.jsx)(nh.Zr,{children:l}),(0,o.jsx)(nh.Rx,{onClick:a,children:s})]})]})})}function nv(e){let{onWorkflowLoad:t,onWorkflowIdChange:n,onLoadingChange:o,onErrorChange:i,workflowId:s,loadedWorkflow:l}=e,c=(0,r.useSearchParams)(),d=(0,r.useRouter)();return(0,a.useEffect)(()=>{(async()=>{let e=c.get("workflow_id");if(!e){d.push("/workflows");return}if(e===s&&l){console.log("Workflow already loaded, skipping fetch");return}n(e),o(!0),i(null);try{let n=await (0,np.gw)(e);if(console.log("Workflow details:",n),!n||!n.workflow||!n.workflow.builder_url){var a;throw console.error("Workflow data URL not found inside workflow details.",null==n?void 0:null===(a=n.workflow)||void 0===a?void 0:a.builder_url),Error("Workflow data URL not found")}let o=await nf(n.workflow.builder_url);!o.workflow_name&&n.workflow.name&&(console.log("Setting workflow_name from workflowDetails:",n.workflow.name),o.workflow_name=n.workflow.name),console.log("Final workflowData before loading:",o),t(o,!1)}catch(e){console.error("Error loading workflow:",e),i(e instanceof Error?e.message:"Failed to load workflow. Please try again.")}finally{o(!1)}})()},[c,t,s,l,d,n,o,i]),null}function nw(){let[e,t]=(0,a.useState)({}),n=(0,a.useMemo)(()=>e,[e]),[r,i]=(0,a.useState)(!0),[l,c]=(0,a.useState)(null),[d,u]=(0,a.useState)([]),[p,m]=(0,a.useState)([]),[f,g]=(0,a.useState)("Untitled Workflow"),[h,x]=(0,a.useState)(!1),[v,w]=(0,a.useState)(!1),[b,y]=(0,a.useState)(null),[N,j]=(0,a.useState)(!1),[k,S]=(0,a.useState)(null),[_,E]=(0,a.useState)(!1),[C,I]=(0,a.useState)(!1),[A,D]=(0,a.useState)(null),[O,T]=(0,a.useState)(null),[F,R]=(0,a.useState)(!1),[V,L]=(0,a.useState)(null),[P,B]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=async()=>{try{i(!0),c(null);let e=await (0,eS.fetchComponents)();console.log("DEBUG: Fetched regular components categories:",Object.keys(e)),e.AI?console.log("DEBUG: AI components found:",Object.keys(e.AI)):console.log("DEBUG: No AI category found in components");let n=await (0,eS.fetchMCPComponents)();console.log("DEBUG: Fetched MCP components:",n),console.log("DEBUG: MCP components structure:",n),n.MCP?(console.log("DEBUG: MCP category exists with components:",Object.keys(n.MCP)),console.log("DEBUG: First MCP component:",Object.values(n.MCP)[0])):console.log("DEBUG: MCP category does not exist in mcpComponents");let o={...e,...n};e.MCP&&n.MCP&&(console.log("DEBUG: Both fetchedComponents and mcpComponents have MCP category, merging them"),o.MCP={...e.MCP,...n.MCP}),console.log("DEBUG: Merged components categories:",Object.keys(o)),console.log("DEBUG: Final MCP components count:",o.MCP?Object.keys(o.MCP).length:0),t(o)}catch(t){let e=t instanceof Error?t.message:"Unknown error";c("Failed to load components: ".concat(e,". Ensure the backend is running.")),console.error(t)}finally{i(!1)}};console.log("Loading components..."),e()},[]);let M=(0,a.useCallback)((e,t)=>{u(e),m(t),B(!0)},[]);(0,a.useCallback)(async()=>{try{let e="".concat(f.replace(/\s+/g,"_")),t=await (0,eS.saveWorkflowToServer)({nodes:d,edges:p,filename:e,workflow_name:f,...O&&{workflow_id:O}});t.success?(!O&&t.workflow_id&&(console.log("Received workflow ID from server:",t.workflow_id),T(t.workflow_id),window.history.replaceState({},"","?workflow_id=".concat(t.workflow_id))),B(!1),alert(t.message||"Workflow saved successfully on server at: ".concat(t.filepath))):alert("Failed to save workflow on server: ".concat(t.error||"Unknown error"))}catch(t){let e=t instanceof Error?t.message:String(t);console.error("Error in handleSaveWorkflowToServer:",t),alert("Error saving workflow to server: ".concat(e))}},[d,p,f,O]);let q=(0,a.useCallback)(async()=>{try{let{validateBeforeSave:e}=et.getState(),t=await e(d,p);if(!t.isValid){let e=t.errors.map(e=>e.message).join("\n");alert("Validation failed. Please fix the following issues:\n".concat(e));return}let n="".concat(f.replace(/\s+/g,"_")),o=await (0,eS.saveWorkflowToServer)({nodes:d,edges:p,filename:n,workflow_name:f,...O&&{workflow_id:O}});o.success?(!O&&o.workflow_id&&(console.log("Received workflow ID from server:",o.workflow_id),T(o.workflow_id),window.history.replaceState({},"","?workflow_id=".concat(o.workflow_id))),B(!1),alert(o.message||"Workflow saved successfully!")):alert("Failed to save workflow: ".concat(o.error||"Unknown error"))}catch(t){let e=t instanceof Error?t.message:String(t);console.error("Error in handleSaveWorkflow:",t),alert("Error saving workflow: ".concat(e))}},[d,p,f,O]),U=(0,a.useCallback)(async()=>{if(!v){w(!0),y(null);try{let{validateBeforeExecution:e}=et.getState(),t=await e(d,p);if(!t.isValid){let e=t.errors.map(e=>e.message).join("\n");alert("Validation failed. Please fix the following issues:\n".concat(e)),w(!1);return}let n="".concat(f.replace(/\s+/g,"_")),o=await (0,eS.saveWorkflowToServer)({nodes:d,edges:p,filename:n,workflow_name:f,...O&&{workflow_id:O}});if(!o.success){alert("Failed to save workflow before execution: ".concat(o.error||"Unknown error")),w(!1);return}let a=O;!a&&o.workflow_id&&(a=o.workflow_id,T(a),window.history.replaceState({},"","?workflow_id=".concat(a))),B(!1);let r=ey.getState();t.missingFields&&r.setMissingFields(t.missingFields),r.setDialogOpen(!0),r.setActiveTab("parameters"),r.addLog("Workflow validated and saved successfully. Ready for execution."),w(!1)}catch(t){let e=t instanceof Error?t.message:String(t);console.error("Error in handleRunWorkflow:",t),y({success:!1,error:"Frontend error: ".concat(e)}),alert("Error preparing workflow for execution: ".concat(e)),w(!1)}}},[d,p,v,O,f,B]),J=(0,a.useCallback)(()=>{x(e=>!e),document.documentElement.classList.toggle("dark")},[]),G=(0,a.useCallback)(e=>{P?(D(()=>e),I(!0)):e()},[P]),z=(0,a.useCallback)(()=>{G(()=>{j(!0)})},[G]),H=(0,a.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];try{console.log("Loading workflow data:",e);let o=function(e){let t=nu(e);if(!t.isValid)return t;try{let t=function(e){console.log("Converting workflow data to ReactFlow format:",e),e.workflow_data&&e.workflow_data.nodes&&(console.log("Detected wrapped workflow structure, unwrapping..."),e=e.workflow_data);let t=e.nodes.map(e=>(console.log("Processing node:",e),e.data&&"StartNode"===e.data.originalType&&(console.log("Found StartNode, ensuring config is preserved:",e),e.data.config||(e.data.config={}),e.data.config.collected_parameters||(e.data.config.collected_parameters={}),e.data.config.collected_parameters&&Object.keys(e.data.config.collected_parameters).forEach(t=>{let n=e.data.config.collected_parameters[t];void 0===n.required&&(console.log("Setting required=true for parameter ".concat(t," in StartNode")),n.required=!0)}),console.log("StartNode config after ensuring structure:",e.data.config)),{id:e.id,type:e.type||"WorkflowNode",position:e.position,data:e.data,width:e.width,height:e.height,selected:!1,dragging:!1})),n=(e.edges||e.connections).map(e=>(console.log("Processing edge:",e),{id:e.id,source:e.source,sourceHandle:e.sourceHandle,target:e.target,targetHandle:e.targetHandle,type:e.type||"default",animated:!0}));console.log("Converted nodes:",t),console.log("Converted edges:",n);let o=t.map(e=>{var t,n;if((null===(n=e.data)||void 0===n?void 0:null===(t=n.config)||void 0===t?void 0:t.requires_approval)!==void 0){console.log("Migrating requires_approval flag for node ".concat(e.id," from config to definition"));let t={...e,data:{...e.data,definition:e.data.definition?{...e.data.definition,requires_approval:e.data.config.requires_approval,name:e.data.definition.name||"",display_name:e.data.definition.display_name||"",description:e.data.definition.description||"",category:e.data.definition.category||"",icon:e.data.definition.icon||"",beta:e.data.definition.beta||!1,inputs:e.data.definition.inputs||[],outputs:e.data.definition.outputs||[],is_valid:e.data.definition.is_valid||!1,path:e.data.definition.path||""}:void 0}},n={...e.data.config};return delete n.requires_approval,t.data.config=n,t}return e});return console.log("Migrated nodes:",o),{nodes:o,edges:n}}(e);return{isValid:!0,data:t}}catch(e){return{isValid:!1,error:e instanceof Error?e.message:"Failed to convert workflow data to ReactFlow format"}}}(e);if(console.log("Validation result:",o),!o.isValid||!o.data){console.error("Failed to load workflow: ".concat(o.error||"Invalid workflow data"));return}let a=o.data.nodes.find(e=>"StartNode"===e.data.originalType);if(a){var n;console.log("StartNode found in loaded workflow:",a),a.data.config||(a.data.config={}),a.data.config.collected_parameters||(a.data.config.collected_parameters={}),a.data.config.collected_parameters&&Object.keys(a.data.config.collected_parameters).forEach(e=>{let t=a.data.config.collected_parameters[e];void 0===t.required&&(console.log("Setting required=true for parameter ".concat(e," in StartNode")),t.required=!0)}),console.log("StartNode config after ensuring structure:",a.data.config),window.startNodeCollectedParameters=(null===(n=a.data.config)||void 0===n?void 0:n.collected_parameters)||{},console.log("Stored StartNode collected parameters in window object:",window.startNodeCollectedParameters)}else console.log("No StartNode found in loaded workflow, this may cause issues"),window.startNodeCollectedParameters={};console.log("Setting workflow state with:",o.data),S(o.data),u(o.data.nodes),m(o.data.edges),e.workflow_name?(console.log("Setting workflow title from workflowData.workflow_name:",e.workflow_name),g(e.workflow_name)):console.log("No workflow_name found in workflowData, keeping current title:",f),B(!1),t&&(y({success:!0,message:"Workflow loaded successfully!"}),setTimeout(()=>{y(null)},3e3))}catch(e){console.error("Error loading workflow:",e),y({success:!1,error:"Error loading workflow: ".concat(e instanceof Error?e.message:"Unknown error")}),setTimeout(()=>{y(null)},5e3)}},[f]);(0,a.useEffect)(()=>{let e=window.matchMedia("(prefers-color-scheme: dark)").matches;x(e),e&&document.documentElement.classList.add("dark")},[]),(0,a.useEffect)(()=>{let e=e=>{if(P){let t="You have unsaved changes. Are you sure you want to leave?";return e.preventDefault(),e.returnValue=t,t}};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}},[P]);let K=(0,a.useCallback)(()=>{E(e=>!e)},[]);return(0,a.useEffect)(()=>{let e=e=>{e.altKey&&"s"===e.key&&K()};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}},[K]),(0,o.jsxs)("main",{className:"relative flex h-screen w-screen flex-col overflow-hidden ".concat(h?"dark":""),children:[(0,o.jsx)(a.Suspense,{fallback:(0,o.jsx)("div",{children:"Loading workflow..."}),children:(0,o.jsx)(nv,{onWorkflowLoad:H,onWorkflowIdChange:T,onLoadingChange:R,onErrorChange:L,workflowId:O,loadedWorkflow:k})}),(0,o.jsx)(e3,{onSave:q,onRun:U,workflowTitle:f,onTitleChange:g,onValidate:()=>console.log("Validate workflow"),isDarkMode:h,onToggleTheme:J,onLoad:z,className:"flex-shrink-0",nodes:d,edges:p}),(0,o.jsxs)("div",{className:"flex flex-grow overflow-hidden",children:[(0,o.jsx)(W,{components:n,collapsed:_,onToggleCollapse:K}),(0,o.jsxs)("div",{className:"bg-background/50 flex flex-grow flex-col overflow-hidden backdrop-blur-sm",children:[r&&(0,o.jsx)("div",{className:"flex flex-grow items-center justify-center",children:(0,o.jsxs)("div",{className:"flex animate-pulse flex-col items-center",children:[(0,o.jsx)("div",{className:"bg-brand-primary/20 mb-4 h-12 w-12 rounded-full"}),(0,o.jsx)("div",{className:"bg-brand-primary/20 h-4 w-48 rounded"}),(0,o.jsx)("div",{className:"font-secondary text-brand-secondary-font mt-2 text-sm",children:"Loading components..."})]})}),l&&(0,o.jsx)("div",{className:"flex flex-grow items-center justify-center",children:(0,o.jsxs)("div",{className:"border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center",children:[(0,o.jsx)("div",{className:"font-primary text-brand-unpublish mb-2 font-medium",children:"Error Loading Components"}),(0,o.jsx)("div",{className:"font-secondary text-brand-secondary-font text-sm",children:l}),(0,o.jsx)("button",{type:"button",className:"border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 mt-4 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium",onClick:()=>window.location.reload(),children:"Retry"})]})}),F&&(0,o.jsx)("div",{className:"flex flex-grow items-center justify-center",children:(0,o.jsxs)("div",{className:"border-brand-stroke bg-brand-card-hover max-w-md rounded-lg border p-6 text-center",children:[(0,o.jsx)(ev.A,{className:"text-brand-primary mx-auto mb-4 h-8 w-8 animate-spin"}),(0,o.jsx)("div",{className:"font-primary text-brand-primary-font dark:text-brand-white-text mb-2 font-medium",children:"Loading Workflow"}),(0,o.jsx)("div",{className:"font-secondary text-brand-secondary-font text-sm",children:"Please wait while we load your workflow..."})]})}),V&&(0,o.jsx)("div",{className:"flex flex-grow items-center justify-center",children:(0,o.jsxs)("div",{className:"border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center",children:[(0,o.jsx)("div",{className:"font-primary text-brand-unpublish mb-2 font-medium",children:"Error Loading Workflow"}),(0,o.jsx)("div",{className:"font-secondary text-brand-secondary-font mb-4 text-sm",children:V}),(0,o.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,o.jsx)("button",{type:"button",className:"border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium",onClick:()=>window.location.reload(),children:"Try Again"}),(0,o.jsx)(s(),{href:"/home",children:(0,o.jsx)("button",{type:"button",className:"brand-gradient-indicator text-brand-white-text inline-flex h-8 items-center justify-center rounded-md px-3 py-2 text-sm font-medium",children:"Back to Home"})})]})]})}),!r&&!l&&!F&&!V&&(0,o.jsx)(nd,{onFlowChange:M,initialWorkflow:k||void 0})]})]}),v&&(0,o.jsx)("div",{className:"brand-gradient-indicator text-brand-white-text animate-in fade-in slide-in-from-bottom-4 absolute bottom-4 left-4 z-50 rounded-lg p-3 text-sm shadow-lg",children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("div",{className:"h-2 w-2 animate-ping rounded-full bg-white/80"}),(0,o.jsx)("span",{className:"font-primary",children:"Executing workflow..."})]})}),b&&!v&&(0,o.jsx)("div",{className:"animate-in fade-in slide-in-from-bottom-4 absolute right-4 bottom-4 z-50 rounded-lg p-3 text-sm shadow-lg ".concat(b.success?"bg-brand-tick text-white":"bg-brand-unpublish text-white"),children:(0,o.jsx)("div",{className:"flex items-center gap-2",children:b.success?(0,o.jsx)("span",{className:"font-primary",children:"Workflow executed successfully!"}):(0,o.jsxs)("span",{className:"font-primary",children:["Error: ",b.error]})})}),(0,o.jsx)(ng,{isOpen:N,onClose:()=>j(!1),onLoadWorkflow:H}),(0,o.jsx)(nx,{isOpen:C,onClose:()=>I(!1),onContinue:()=>{I(!1),A&&(A(),D(null))}})]})}},45215:(e,t,n)=>{"use strict";n.r(t),n.d(t,{clearAllApprovalEvents:()=>i,clearApprovalEvent:()=>r,dispatchApprovalNeededEvent:()=>a,hasApprovalEventBeenDispatched:()=>s}),window._approvalEventHistory=window._approvalEventHistory||[];let o=new Set;function a(e,t,n){if(!e||!t||"unknown"===t){console.log("Skipping approval event due to invalid parameters: correlationId=".concat(e,", nodeId=").concat(t));return}let a="".concat(e,"_").concat(t),r=Date.now();if(window._approvalEventHistory&&(window._approvalEventHistory.push({correlationId:e,nodeId:t,nodeName:n||t,timestamp:r,status:"requested"}),window._approvalEventHistory.length>10&&(window._approvalEventHistory=window._approvalEventHistory.slice(-10))),!o.has(a)||r-(window._lastApprovalTimestamp||0)>5e3){console.log("Dispatching approval needed event for node ".concat(n||t," (").concat(t,")")),o.add(a),window._lastApprovalTimestamp=r;let i=new CustomEvent("workflow-approval-needed",{detail:{correlationId:e,nodeId:t,nodeName:n||t,timestamp:r,approvalKey:a}});window.dispatchEvent(i),window._pendingApproval={correlationId:e,nodeId:t,nodeName:n||t,timestamp:r},setTimeout(()=>{window.dispatchEvent(new CustomEvent("approval-ui-update"))},200)}else console.log("Skipping duplicate approval event for node ".concat(n||t," (").concat(t,")"))}function r(e,t){let n="".concat(e,"_").concat(t);o.delete(n),window._approvalEventHistory&&(window._approvalEventHistory.push({correlationId:e,nodeId:t,nodeName:t,timestamp:Date.now(),status:"cleared"}),window._approvalEventHistory.length>10&&(window._approvalEventHistory=window._approvalEventHistory.slice(-10))),console.log("Cleared approval event for node ".concat(t," with correlation ID ").concat(e))}function i(){o.clear(),window._approvalEventHistory&&(window._approvalEventHistory.push({correlationId:"all",nodeId:"all",nodeName:"all",timestamp:Date.now(),status:"cleared_all"}),window._approvalEventHistory.length>10&&(window._approvalEventHistory=window._approvalEventHistory.slice(-10))),window._pendingApproval=void 0,console.log("Cleared all approval events")}function s(e,t){let n="".concat(e,"_").concat(t);return o.has(n)}}},e=>{var t=t=>e(e.s=t);e.O(0,[1294,8702,9352,3291,6874,8517,8095,2740,1822,7764,7907,9527,8441,1684,7358],()=>t(10979)),_N_E=e.O()}]);