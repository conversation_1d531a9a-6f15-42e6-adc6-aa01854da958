"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4766],{7165:(t,e,i)=>{i.d(e,{jG:()=>s});var n=t=>setTimeout(t,0),s=function(){let t=[],e=0,i=t=>{t()},s=t=>{t()},r=n,o=n=>{e?t.push(n):r(()=>{i(n)})},u=()=>{let e=t;t=[],e.length&&r(()=>{s(()=>{e.forEach(t=>{i(t)})})})};return{batch:t=>{let i;e++;try{i=t()}finally{--e||u()}return i},batchCalls:t=>(...e)=>{o(()=>{t(...e)})},schedule:o,setNotifyFunction:t=>{i=t},setBatchNotifyFunction:t=>{s=t},setScheduler:t=>{r=t}}}()},21239:(t,e,i)=>{i.d(e,{t:()=>r});var n=i(25910),s=i(52020),r=new class extends n.Q{#t=!0;#e;#i;constructor(){super(),this.#i=t=>{if(!s.S$&&window.addEventListener){let e=()=>t(!0),i=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",i)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#i=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#t}}},25910:(t,e,i)=>{i.d(e,{Q:()=>n});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},26715:(t,e,i)=>{i.d(e,{Ht:()=>u,jE:()=>o});var n=i(12115),s=i(95155),r=n.createContext(void 0),o=t=>{let e=n.useContext(r);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},u=t=>{let{client:e,children:i}=t;return n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,s.jsx)(r.Provider,{value:e,children:i})}},34560:(t,e,i)=>{i.d(e,{$:()=>u,s:()=>o});var n=i(7165),s=i(57948),r=i(45780),o=class extends s.k{#n;#s;#r;constructor(t){super(),this.mutationId=t.mutationId,this.#s=t.mutationCache,this.#n=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#n.includes(t)||(this.#n.push(t),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#n=this.#n.filter(e=>e!==t),this.scheduleGc(),this.#s.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#n.length||("pending"===this.state.status?this.scheduleGc():this.#s.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#o({type:"continue"})};this.#r=(0,r.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#o({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#o({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#s.canRun(this)});let i="pending"===this.state.status,n=!this.#r.canStart();try{if(i)e();else{this.#o({type:"pending",variables:t,isPaused:n}),await this.#s.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#o({type:"pending",context:e,variables:t,isPaused:n})}let s=await this.#r.start();return await this.#s.config.onSuccess?.(s,t,this.state.context,this),await this.options.onSuccess?.(s,t,this.state.context),await this.#s.config.onSettled?.(s,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(s,null,t,this.state.context),this.#o({type:"success",data:s}),s}catch(e){try{throw await this.#s.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#s.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#o({type:"error",error:e})}}finally{this.#s.runNext(this)}}#o(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),n.jG.batch(()=>{this.#n.forEach(e=>{e.onMutationUpdate(t)}),this.#s.notify({mutation:this,type:"updated",action:t})})}};function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},35695:(t,e,i)=>{var n=i(18999);i.o(n,"useParams")&&i.d(e,{useParams:function(){return n.useParams}}),i.o(n,"usePathname")&&i.d(e,{usePathname:function(){return n.usePathname}}),i.o(n,"useRouter")&&i.d(e,{useRouter:function(){return n.useRouter}}),i.o(n,"useSearchParams")&&i.d(e,{useSearchParams:function(){return n.useSearchParams}})},45780:(t,e,i)=>{i.d(e,{v_:()=>u,II:()=>h,wm:()=>c});var n=i(50920),s=i(21239),r=i(52020);function o(t){return Math.min(1e3*2**t,3e4)}function u(t){return(t??"online")!=="online"||s.t.isOnline()}var a=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function c(t){return t instanceof a}function h(t){let e,i=!1,c=0,h=!1,l=function(){let t,e,i=new Promise((i,n)=>{t=i,e=n});function n(t){Object.assign(i,t),delete i.resolve,delete i.reject}return i.status="pending",i.catch(()=>{}),i.resolve=e=>{n({status:"fulfilled",value:e}),t(e)},i.reject=t=>{n({status:"rejected",reason:t}),e(t)},i}(),d=()=>n.m.isFocused()&&("always"===t.networkMode||s.t.isOnline())&&t.canRun(),f=()=>u(t.networkMode)&&t.canRun(),p=i=>{h||(h=!0,t.onSuccess?.(i),e?.(),l.resolve(i))},y=i=>{h||(h=!0,t.onError?.(i),e?.(),l.reject(i))},m=()=>new Promise(i=>{e=t=>{(h||d())&&i(t)},t.onPause?.()}).then(()=>{e=void 0,h||t.onContinue?.()}),v=()=>{let e;if(h)return;let n=0===c?t.initialPromise:void 0;try{e=n??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(p).catch(e=>{if(h)return;let n=t.retry??3*!r.S$,s=t.retryDelay??o,u="function"==typeof s?s(c,e):s,a=!0===n||"number"==typeof n&&c<n||"function"==typeof n&&n(c,e);if(i||!a){y(e);return}c++,t.onFail?.(c,e),(0,r.yy)(u).then(()=>d()?void 0:m()).then(()=>{i?y(e):v()})})};return{promise:l,cancel:e=>{h||(y(new a(e)),t.abort?.())},continue:()=>(e?.(),l),cancelRetry:()=>{i=!0},continueRetry:()=>{i=!1},canStart:f,start:()=>(f()?v():m().then(v),l)}}},50920:(t,e,i)=>{i.d(e,{m:()=>r});var n=i(25910),s=i(52020),r=new class extends n.Q{#u;#e;#i;constructor(){super(),this.#i=t=>{if(!s.S$&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#i)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#i=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#u!==t&&(this.#u=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#u?this.#u:globalThis.document?.visibilityState!=="hidden"}}},52020:(t,e,i)=>{i.d(e,{Cp:()=>p,EN:()=>f,Eh:()=>c,F$:()=>d,MK:()=>h,S$:()=>n,ZM:()=>E,ZZ:()=>C,Zw:()=>r,d2:()=>a,f8:()=>y,gn:()=>o,hT:()=>S,j3:()=>u,lQ:()=>s,nJ:()=>l,pl:()=>g,y9:()=>P,yy:()=>w});var n="undefined"==typeof window||"Deno"in globalThis;function s(){}function r(t,e){return"function"==typeof t?t(e):t}function o(t){return"number"==typeof t&&t>=0&&t!==1/0}function u(t,e){return Math.max(t+(e||0)-Date.now(),0)}function a(t,e){return"function"==typeof t?t(e):t}function c(t,e){return"function"==typeof t?t(e):t}function h(t,e){let{type:i="all",exact:n,fetchStatus:s,predicate:r,queryKey:o,stale:u}=t;if(o){if(n){if(e.queryHash!==d(o,e.options))return!1}else if(!p(e.queryKey,o))return!1}if("all"!==i){let t=e.isActive();if("active"===i&&!t||"inactive"===i&&t)return!1}return("boolean"!=typeof u||e.isStale()===u)&&(!s||s===e.state.fetchStatus)&&(!r||!!r(e))}function l(t,e){let{exact:i,status:n,predicate:s,mutationKey:r}=t;if(r){if(!e.options.mutationKey)return!1;if(i){if(f(e.options.mutationKey)!==f(r))return!1}else if(!p(e.options.mutationKey,r))return!1}return(!n||e.state.status===n)&&(!s||!!s(e))}function d(t,e){return(e?.queryKeyHashFn||f)(t)}function f(t){return JSON.stringify(t,(t,e)=>v(e)?Object.keys(e).sort().reduce((t,i)=>(t[i]=e[i],t),{}):e)}function p(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(i=>p(t[i],e[i]))}function y(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let i in t)if(t[i]!==e[i])return!1;return!0}function m(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function v(t){if(!b(t))return!1;let e=t.constructor;if(void 0===e)return!0;let i=e.prototype;return!!(b(i)&&i.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(t)===Object.prototype}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function w(t){return new Promise(e=>{setTimeout(e,t)})}function g(t,e,i){return"function"==typeof i.structuralSharing?i.structuralSharing(t,e):!1!==i.structuralSharing?function t(e,i){if(e===i)return e;let n=m(e)&&m(i);if(n||v(e)&&v(i)){let s=n?e:Object.keys(e),r=s.length,o=n?i:Object.keys(i),u=o.length,a=n?[]:{},c=0;for(let r=0;r<u;r++){let u=n?r:o[r];(!n&&s.includes(u)||n)&&void 0===e[u]&&void 0===i[u]?(a[u]=void 0,c++):(a[u]=t(e[u],i[u]),a[u]===e[u]&&void 0!==e[u]&&c++)}return r===u&&c===r?e:a}return i}(t,e):e}function P(t,e,i=0){let n=[...t,e];return i&&n.length>i?n.slice(1):n}function C(t,e,i=0){let n=[e,...t];return i&&n.length>i?n.slice(0,-1):n}var S=Symbol();function E(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==S?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}},57948:(t,e,i)=>{i.d(e,{k:()=>s});var n=i(52020),s=class{#a;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.gn)(this.gcTime)&&(this.#a=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(n.S$?1/0:3e5))}clearGcTimeout(){this.#a&&(clearTimeout(this.#a),this.#a=void 0)}}}}]);