(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8620],{35695:(e,u,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(u,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(u,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(u,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(u,{useSearchParams:function(){return s.useSearchParams}})},94574:(e,u,r)=>{"use strict";r.r(u),r.d(u,{default:()=>t});var s=r(12115),a=r(35695);function t(){let e=(0,a.useRouter)();return(0,s.useEffect)(()=>{e.replace("/workflows")},[e]),null}},97162:(e,u,r)=>{Promise.resolve().then(r.bind(r,94574))}},e=>{var u=u=>e(e.s=u);e.O(0,[8441,1684,7358],()=>u(97162)),_N_E=e.O()}]);