(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4735],{7254:(e,t,r)=>{Promise.resolve().then(r.bind(r,53987))},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:l="",children:d,iconNode:u,...m}=e;return(0,a.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:o?24*Number(n)/Number(s):n,className:i("lucide",l),...m},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:c,...d}=r;return(0,a.createElement)(l,{ref:n,iconNode:t,className:i("lucide-".concat(s(o(e))),"lucide-".concat(e),c),...d})});return r.displayName=o(e),r}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>i});var a=r(95155);r(12115);var s=r(99708),n=r(74466),o=r(59434);let i=(0,n.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:n,asChild:c=!1,...l}=e,d=c?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:n,className:t})),...l})}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40081:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]])},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53987:(e,t,r)=>{"use strict";r.d(t,{default:()=>k});var a=r(95155),s=r(12115),n=r(35695),o=r(6874),i=r.n(o),c=r(54897),l=r(30285),d=r(66695),u=r(40081),m=r(19946);let h=(0,m.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var x=r(51154),f=r(85339),p=r(81284),b=r(69074);let v=(0,m.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var g=r(85690);let y=(0,m.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),w=e=>{try{return new Date(e).toLocaleString()}catch(e){return"Unknown date"}};function k(e){let{id:t}=e,r=(0,n.useRouter)(),[o,m]=(0,s.useState)(null),[k,j]=(0,s.useState)(!0),[N,A]=(0,s.useState)(null),C=e=>"workflow"in e&&e.workflow?e.workflow:e;(0,s.useEffect)(()=>{let e=async()=>{let e=(0,c.XI)();try{j(!0),A(null);let r="https://app-dev.rapidinnovation.dev/api/v1";console.log("Using API URL:",r);let a=await fetch("".concat(r,"/workflows/").concat(t),{method:"GET",headers:{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}});if(!a.ok)throw Error("Failed to fetch workflow details: ".concat(a.status," ").concat(a.statusText));let s=await a.json();console.log("Fetched workflow details:",s),m(s)}catch(e){console.error("Failed to fetch workflow details:",e),e instanceof Error?A("Failed to load workflow details: ".concat(e.message)):A("Failed to load workflow details. Please try again later.")}finally{j(!1)}};t&&e()},[t]);let _=()=>{r.push("/?workflow_id=".concat(t))},T=async()=>{try{let e=await fetch("".concat("https://app-dev.rapidinnovation.dev/api/v1","/workflows/").concat(t,"/execute"),{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to execute workflow: ".concat(e.status," ").concat(e.statusText));let r=await e.json();console.log("Execution result:",r),alert("Workflow execution started successfully!")}catch(e){console.error("Failed to execute workflow:",e),alert("Failed to execute workflow: ".concat(e instanceof Error?e.message:"Unknown error"))}};return(0,a.jsxs)("main",{className:"bg-background min-h-screen",children:[(0,a.jsx)("div",{className:"brand-gradient-indicator text-brand-white-text p-6 shadow-md",children:(0,a.jsx)("div",{className:"container mx-auto",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"rounded-md bg-white p-2 shadow-md",children:(0,a.jsx)(u.A,{className:"text-brand-primary h-6 w-6"})}),(0,a.jsx)("h1",{className:"font-primary text-2xl font-bold",children:"Workflow Details"})]}),(0,a.jsx)(i(),{href:"/workflows",children:(0,a.jsxs)(l.$,{variant:"outline",className:"text-brand-primary hover:bg-brand-card-hover border-brand-stroke bg-white",children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4"}),"Back to Workflows"]})})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:k?(0,a.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,a.jsx)(x.A,{className:"text-brand-primary h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"font-primary ml-2 text-lg",children:"Loading workflow details..."})]}):N?(0,a.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center text-center",children:[(0,a.jsx)(f.A,{className:"text-brand-unpublish mb-4 h-12 w-12"}),(0,a.jsx)("h3",{className:"font-primary mb-2 text-xl font-semibold",children:"Failed to Load Workflow Details"}),(0,a.jsx)("p",{className:"text-brand-secondary-font mb-4",children:N}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(l.$,{onClick:()=>window.location.reload(),className:"brand-gradient-indicator text-brand-white-text",children:"Try Again"}),(0,a.jsx)(i(),{href:"/workflows",children:(0,a.jsx)(l.$,{variant:"outline",className:"border-brand-stroke text-brand-primary hover:bg-brand-clicked",children:"Back to Workflows"})})]})]}):o?(()=>{let e=C(o);return(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,a.jsxs)(d.Zp,{className:"border-brand-stroke bg-brand-card lg:col-span-2",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsx)(d.ZB,{className:"font-primary text-brand-primary-font text-2xl",children:e.name||"Untitled Workflow"}),(0,a.jsx)(d.BT,{className:"font-secondary text-brand-secondary-font",children:e.description||"No description provided"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"text-brand-secondary h-5 w-5"}),(0,a.jsx)("span",{className:"font-primary font-medium",children:"ID:"}),(0,a.jsx)("span",{className:"text-brand-secondary-font",children:e.id})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"text-brand-secondary h-5 w-5"}),(0,a.jsx)("span",{className:"font-primary font-medium",children:"Created:"}),(0,a.jsx)("span",{className:"text-brand-secondary-font",children:e.created_at?w(e.created_at):"Unknown"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v,{className:"text-brand-secondary h-5 w-5"}),(0,a.jsx)("span",{className:"font-primary font-medium",children:"Last Updated:"}),(0,a.jsx)("span",{className:"text-brand-secondary-font",children:e.updated_at?w(e.updated_at):"Unknown"})]}),void 0!==e.execution_count&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"text-brand-secondary h-5 w-5"}),(0,a.jsx)("span",{className:"font-primary font-medium",children:"Executions:"}),(0,a.jsx)("span",{className:"text-brand-secondary-font",children:e.execution_count})]})]})}),(0,a.jsxs)(d.wL,{className:"flex justify-between",children:[(0,a.jsxs)(l.$,{variant:"outline",onClick:_,className:"border-brand-stroke text-brand-primary hover:bg-brand-clicked flex items-center gap-2",children:[(0,a.jsx)(y,{className:"h-4 w-4"}),"Open in Editor"]}),(0,a.jsxs)(l.$,{onClick:T,className:"brand-gradient-indicator text-brand-white-text flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),"Execute Workflow"]})]})]}),(0,a.jsxs)(d.Zp,{className:"border-brand-stroke bg-brand-card",children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{className:"font-primary text-brand-primary-font",children:"Workflow Metadata"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[e.builder_url&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Builder URL"}),(0,a.jsx)("p",{className:"text-brand-secondary-font text-xs break-all",children:e.builder_url})]}),e.user_id&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Owner"}),(0,a.jsx)("p",{className:"text-brand-secondary-font text-sm",children:e.user_id})]}),e.status&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Status"}),(0,a.jsx)("div",{className:"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ".concat("active"===e.status?"bg-brand-tick/20 text-brand-tick":"bg-brand-unpublish/20 text-brand-unpublish"),children:e.status})]}),e.tags&&Array.isArray(e.tags)&&e.tags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,t)=>(0,a.jsx)("span",{className:"bg-brand-primary/10 text-brand-primary inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",children:e},t))})]})]})})]})]})})():(0,a.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center text-center",children:[(0,a.jsx)(f.A,{className:"text-brand-unpublish mb-4 h-12 w-12"}),(0,a.jsx)("h3",{className:"font-primary mb-2 text-xl font-semibold",children:"Workflow Not Found"}),(0,a.jsx)("p",{className:"text-brand-secondary-font mb-4",children:"The workflow you're looking for doesn't exist or you don't have permission to view it."}),(0,a.jsx)(i(),{href:"/workflows",children:(0,a.jsx)(l.$,{className:"brand-gradient-indicator text-brand-white-text",children:"Back to Workflows"})})]})})]})}},54897:(e,t,r)=>{"use strict";r.d(t,{BW:()=>o,VT:()=>n,XI:()=>s,gW:()=>i});var a=r(57383);let s=()=>a.A.get("accessToken")||"",n=()=>{let e=a.A.get("accessToken");return console.log("Client-side access token check:",!!e),!!e},o=(e,t,r,s)=>{a.A.set("accessToken",e,{path:"/",domain:"localhost",secure:!0,sameSite:"lax",expires:r/86400})},i=()=>{a.A.remove("accessToken",{path:"/",domain:"localhost"}),a.A.remove("refreshToken",{path:"/",domain:"localhost"}),a.A.remove("refreshToken",{path:"/api/auth/refresh",domain:"localhost"}),console.log("Client-side cookie clearing attempted for both tokens")}},57383:(e,t,r)=>{"use strict";function a(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)e[a]=r[a]}return e}r.d(t,{A:()=>s});var s=function e(t,r){function s(e,s,n){if("undefined"!=typeof document){"number"==typeof(n=a({},r,n)).expires&&(n.expires=new Date(Date.now()+864e5*n.expires)),n.expires&&(n.expires=n.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var i in n)n[i]&&(o+="; "+i,!0!==n[i]&&(o+="="+n[i].split(";")[0]));return document.cookie=e+"="+t.write(s,e)+o}}return Object.create({set:s,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],a={},s=0;s<r.length;s++){var n=r[s].split("="),o=n.slice(1).join("=");try{var i=decodeURIComponent(n[0]);if(a[i]=t.read(o,i),e===i)break}catch(e){}}return e?a[e]:a}},remove:function(e,t){s(e,"",a({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,a({},this.attributes,t))},withConverter:function(t){return e(a({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})}({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n,s:()=>o});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function o(e,t){let r=null;return function(){for(var a=arguments.length,s=Array(a),n=0;n<a;n++)s[n]=arguments[n];null!==r&&clearTimeout(r),r=setTimeout(()=>{r=null,e(...s)},t)}}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>d});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-3 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},69074:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},81284:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},85690:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[9352,6874,8441,1684,7358],()=>t(7254)),_N_E=e.O()}]);