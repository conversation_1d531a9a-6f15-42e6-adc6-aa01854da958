(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4700],{9438:(e,s,t)=>{Promise.resolve().then(t.bind(t,84436))},17759:(e,s,t)=>{"use strict";t.d(s,{C5:()=>w,MJ:()=>f,eI:()=>h,lR:()=>g,lV:()=>m,zB:()=>c});var a=t(95155),r=t(12115),n=t(99708),l=t(62177),o=t(59434),i=t(85057);let m=l.Op,d=r.createContext({}),c=e=>{let{...s}=e;return(0,a.jsx)(d.Provider,{value:{name:s.name},children:(0,a.jsx)(l.xI,{...s})})},u=()=>{let e=r.useContext(d),s=r.useContext(x),{getFieldState:t}=(0,l.xW)(),a=(0,l.lN)({name:e.name}),n=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=s;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...n}},x=r.createContext({});function h(e){let{className:s,...t}=e,n=r.useId();return(0,a.jsx)(x.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",s),...t})})}function g(e){let{className:s,...t}=e,{error:r,formItemId:n}=u();return(0,a.jsx)(i.J,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",s),htmlFor:n,...t})}function f(e){let{...s}=e,{error:t,formItemId:r,formDescriptionId:l,formMessageId:o}=u();return(0,a.jsx)(n.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!t,...s})}function w(e){var s;let{className:t,...r}=e,{error:n,formMessageId:l}=u(),i=n?String(null!==(s=null==n?void 0:n.message)&&void 0!==s?s:""):r.children;return i?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,o.cn)("text-destructive text-sm",t),...r,children:i}):null}},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48282:(e,s,t)=>{"use strict";t.d(s,{O5:()=>r,Oh:()=>l,Oj:()=>o,jc:()=>n});var a=t(55594);a.z.object({email:a.z.string().email({message:"Please enter a valid email address."}),password:a.z.string().min(1,{message:"Password is required."})}),a.z.object({email:a.z.string().email({message:"Please enter a valid email address."})});let r=a.z.object({fullName:a.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:a.z.string().email({message:"Please enter a valid email address."}),password:a.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:a.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),n=a.z.object({email:a.z.string().email({message:"Please enter a valid email address."})});a.z.object({newPassword:a.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:a.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]});let l=a.z.object({password:a.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:a.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});function o(e){return{length:e.length>=6&&e.length<=15,hasNumber:/[0-9]/.test(e),hasSymbol:/[!@#$%^&*(),.?":{}|<>]/.test(e)}}},51154:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},84436:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(95155),r=t(12115),n=t(62177),l=t(90221),o=t(48282),i=t(30285),m=t(62523),d=t(17759),c=t(40646),u=t(51154),x=t(27997);function h(){let[e,s]=(0,r.useState)(!1),[t,h]=(0,r.useState)(!1),g=(0,n.mN)({resolver:(0,l.u)(o.jc),defaultValues:{email:""}}),f=async e=>{s(!0);try{await x.authApi.forgotPassword(e.email),h(!0)}catch(e){console.error("Reset password error:",e),g.setError("root",{type:"manual",message:e.message||"Failed to send reset link. Please try again."})}finally{s(!1)}};return t?(0,a.jsx)("div",{className:"space-y-6 text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"rounded-full bg-green-100 p-3",children:(0,a.jsx)(c.A,{className:"h-10 w-10 text-green-600"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Reset Link Sent"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"We've sent a password reset link to your email. Please check your inbox and follow the instructions to reset your password."})]})}):(0,a.jsx)(d.lV,{...g,children:(0,a.jsxs)("form",{onSubmit:g.handleSubmit(f),className:"space-y-6",children:[g.formState.errors.root&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-3 text-sm text-red-500",children:g.formState.errors.root.message}),(0,a.jsx)(d.zB,{control:g.control,name:"email",render:s=>{let{field:t}=s;return(0,a.jsxs)(d.eI,{children:[(0,a.jsx)(d.lR,{children:"Email"}),(0,a.jsx)(d.MJ,{children:(0,a.jsx)(m.p,{placeholder:"Enter your email",type:"email",disabled:e,...t})}),(0,a.jsx)(d.C5,{})]})}}),(0,a.jsxs)(i.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Send Reset Link"]})]})})}var g=t(6874),f=t.n(g),w=t(40081);function p(){return(0,a.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,a.jsx)(w.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Reset Password"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Enter your email to receive a password reset link"})]}),(0,a.jsx)(h,{}),(0,a.jsx)("div",{className:"text-center text-sm",children:(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Remember your password?"," ",(0,a.jsx)(f(),{href:"/login",className:"text-primary font-medium hover:underline",children:"Log in"})]})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9352,3291,6874,8517,2313,7764,7997,8441,1684,7358],()=>s(9438)),_N_E=e.O()}]);