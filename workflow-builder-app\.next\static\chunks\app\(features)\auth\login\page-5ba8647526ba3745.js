(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7818],{8699:(e,t,s)=>{"use strict";s.d(t,{w:()=>a});var r=s(34477);let a=(0,r.createServerReference)("7f918b96ddb9292f8563a124aa443704dbe4f5b95f",r.callServer,void 0,r.findSourceMapURL,"setAuthCookies")},12874:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]])},17759:(e,t,s)=>{"use strict";s.d(t,{C5:()=>f,MJ:()=>g,eI:()=>p,lR:()=>v,lV:()=>d,zB:()=>u});var r=s(95155),a=s(12115),i=s(99708),n=s(62177),o=s(59434),l=s(85057);let d=n.Op,c=a.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(c.Provider,{value:{name:t.name},children:(0,r.jsx)(n.xI,{...t})})},m=()=>{let e=a.useContext(c),t=a.useContext(h),{getFieldState:s}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),i=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...i}},h=a.createContext({});function p(e){let{className:t,...s}=e,i=a.useId();return(0,r.jsx)(h.Provider,{value:{id:i},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...s})})}function v(e){let{className:t,...s}=e,{error:a,formItemId:i}=m();return(0,r.jsx)(l.J,{"data-slot":"form-label","data-error":!!a,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:i,...s})}function g(e){let{...t}=e,{error:s,formItemId:a,formDescriptionId:n,formMessageId:o}=m();return(0,r.jsx)(i.DX,{"data-slot":"form-control",id:a,"aria-describedby":s?"".concat(n," ").concat(o):"".concat(n),"aria-invalid":!!s,...t})}function f(e){var t;let{className:s,...a}=e,{error:i,formMessageId:n}=m(),l=i?String(null!==(t=null==i?void 0:i.message)&&void 0!==t?t:""):a.children;return l?(0,r.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",s),...a,children:l}):null}},20870:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var r=s(95155),a=s(12115),i=s(90221),n=s(62177),o=s(78749),l=s(92657),d=s(30285),c=s(62523),u=s(17759),m=s(34560),h=s(7165),p=s(25910),v=s(52020),g=class extends p.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,v.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,v.EN)(t.mutationKey)!==(0,v.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#a(),this.#i()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#a(){let e=this.#s?.state??(0,m.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){h.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},f=s(26715);function x(){}function w(e,t){var s,r;let i=(0,f.jE)(t),[n]=a.useState(()=>new g(i,e));a.useEffect(()=>{n.setOptions(e)},[n,e]);let o=a.useSyncExternalStore(a.useCallback(e=>n.subscribe(h.jG.batchCalls(e)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),l=a.useCallback((e,t)=>{n.mutate(e,t).catch(x)},[n]);if(o.error&&(s=n.options.throwOnError,r=[o.error],"function"==typeof s?s(...r):!!s))throw o.error;return{...o,mutate:l,mutateAsync:o.mutate}}var y=s(56671),b=s(55594);let j=b.z.object({email:b.z.string().email({message:"Please enter a valid email address."}),password:b.z.string().min(1,{message:"Password is required."})}),k=b.z.object({email:b.z.string().email({message:"Please enter a valid email address."})});b.z.object({fullName:b.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:b.z.string().email({message:"Please enter a valid email address."}),password:b.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:b.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),b.z.object({email:b.z.string().email({message:"Please enter a valid email address."})}),b.z.object({newPassword:b.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:b.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]}),b.z.object({password:b.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:b.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});var P=s(4982),N=s(8699),S=s(38378),E=s(54897),M=s(54361),R=s(89761),C=s(40619);let O={login:async e=>{let{email:t,password:s}=e;try{let e=await P.ZQ.post(R.Sn.AUTH.LOGIN,{email:t,password:s});if(!e.data.accessToken)throw Error("Login failed: Unexpected response from server.");await (0,N.w)(e.data.accessToken,e.data.refreshToken||"",3600,86400),(0,E.BW)(e.data.accessToken,e.data.refreshToken||"",3600,86400);let r=C.C$;return M.k.getState().setUser({fullName:e.data.user.name||"",email:e.data.user.email,accessToken:e.data.accessToken}),{user:e.data.user,redirectPath:r}}catch(e){var r,a,i,n,o,l;if(M.k.getState().clearUser(),(null===(r=e.response)||void 0===r?void 0:r.status)===404)throw Error("User not found.");if((null===(a=e.response)||void 0===a?void 0:a.status)===412)throw Error("Account inactive. Please check your email for verification.");throw Error((null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.detail)||(null===(l=e.response)||void 0===l?void 0:null===(o=l.data)||void 0===o?void 0:o.message)||"Invalid Credentials")}},signup:async e=>{try{let{email:t,fullName:s,password:r}=e;return(await P.ZQ.post(R.Sn.AUTH.REGISTER,{name:s,email:t,password:r})).data}catch(e){var t,s,r,a,i,n,o;if((null===(t=e.response)||void 0===t?void 0:t.status)===409)throw Error((null===(o=e.response)||void 0===o?void 0:null===(n=o.data)||void 0===n?void 0:n.detail)||"Email already registered.");throw Error((null===(r=e.response)||void 0===r?void 0:null===(s=r.data)||void 0===s?void 0:s.detail)||(null===(i=e.response)||void 0===i?void 0:null===(a=i.data)||void 0===a?void 0:a.message)||"Signup failed")}},logout:async()=>{try{await (0,S.h)(),(0,E.gW)(),M.k.getState().clearUser(),window.location.href=C.VV}catch(e){console.error("Error during logout:",e),await (0,S.h)(),(0,E.gW)(),M.k.getState().clearUser(),window.location.href=C.VV}},forgotPassword:async e=>{try{return(await P.ZQ.post(R.Sn.AUTH.FORGOT_PASSWORD,{email:e})).data}catch(e){var t,s,r,a;throw Error((null===(s=e.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.detail)||(null===(a=e.response)||void 0===a?void 0:null===(r=a.data)||void 0===r?void 0:r.message)||"Failed to send password reset email")}},resetPassword:async(e,t)=>{let{newPassword:s}=t;try{return(await P.ZQ.post(R.Sn.AUTH.UPDATE_PASSWORD,{token:e,password:s})).data}catch(e){var r,a,i,n;throw Error((null===(a=e.response)||void 0===a?void 0:null===(r=a.data)||void 0===r?void 0:r.detail)||(null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.message)||"Password reset failed")}},getCurrentUser:async()=>{try{if(!(0,E.XI)())throw Error("No access token available");return(await P.ZQ.get("/me")).data}catch(i){var e,t,s,r,a;throw(null===(e=i.response)||void 0===e?void 0:e.status)===403&&(M.k.getState().clearUser(),(0,E.gW)()),Error((null===(s=i.response)||void 0===s?void 0:null===(t=s.data)||void 0===t?void 0:t.detail)||(null===(a=i.response)||void 0===a?void 0:null===(r=a.data)||void 0===r?void 0:r.message)||"Failed to fetch user details")}},isLoggedIn:async()=>{try{return await O.getCurrentUser(),!0}catch(e){return!1}},isAuthenticated:async()=>(0,E.VT)()};var A=s(35695),z=s(12874);function L(e){let{title:t,message:s,email:a,onBackToLogin:i}=e;return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-6 text-center",children:[(0,r.jsx)("div",{className:"bg-primary/10 rounded-full p-3",children:(0,r.jsx)(z.A,{className:"text-primary h-6 w-6"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:t}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:[s,(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-foreground font-medium",children:a})]})]}),(0,r.jsx)(d.$,{variant:"link",onClick:i,className:"text-primary hover:text-primary/80 text-sm",children:"Back to Login"})]})}function U(){let[e,t]=(0,a.useState)(!1),[s,m]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),[v,g]=(0,a.useState)(!1),f=(0,A.useRouter)(),x=(0,n.mN)({resolver:(0,i.u)(s?k:j),defaultValues:{email:"",password:""}}),{mutate:b,isPending:P}=w({mutationFn:O.login,onSuccess:e=>{y.toast.success("Login successful!"),f.push(e.redirectPath)},onError:e=>{y.toast.error(e.message||"Login failed. Please try again.")}}),{mutate:N,isPending:S}=w({mutationFn:O.forgotPassword,onSuccess:()=>{g(!0)},onError:e=>{y.toast.error(e.message||"Failed to send reset link. Please try again.")}}),E=P||S,M=()=>{let e=x.getValues("email");if(s)return!!(e&&e.length>0);{let t=x.getValues("password");return!!(e&&e.length>0&&t&&t.length>0)}};async function R(e){s?N(e.email):b(e)}(0,a.useEffect)(()=>{let e=x.watch(()=>{p(M())});return()=>e.unsubscribe()},[x,s]);let C=()=>{g(!1),m(!1),x.reset(),x.clearErrors(),p(!1)};return v?(0,r.jsx)(L,{title:"Reset Password",message:"We have sent a link to your email to reset your password. Kindly check your inbox (and if you don't find it there, kindly check your spam folder).",email:x.getValues("email"),onBackToLogin:C}):(0,r.jsx)(u.lV,{...x,children:(0,r.jsxs)("form",{onSubmit:x.handleSubmit(R),className:"space-y-8",children:[s?(0,r.jsx)("h2",{className:"text-center text-2xl font-semibold",children:"Reset Your Password"}):(0,r.jsx)("h2",{className:"text-center text-2xl font-semibold",children:"Login"}),(0,r.jsx)(u.zB,{control:x.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:s?"Enter your email address":"Email Address"}),(0,r.jsx)(u.MJ,{children:(0,r.jsx)(c.p,{placeholder:"Enter your email address",type:"email",...t,disabled:E})}),(0,r.jsx)(u.C5,{})]})}}),!s&&(0,r.jsx)(u.zB,{control:x.control,name:"password",render:s=>{let{field:a}=s;return(0,r.jsxs)(u.eI,{children:[(0,r.jsx)(u.lR,{children:"Password"}),(0,r.jsx)(u.MJ,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(c.p,{placeholder:"Enter your password",type:e?"text":"password",...a,disabled:E}),(0,r.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>t(e=>!e),disabled:E,"aria-label":e?"Hide password":"Show password",children:e?(0,r.jsx)(o.A,{className:"h-4 w-4 text-gray-500"}):(0,r.jsx)(l.A,{className:"h-4 w-4 text-gray-500"})})]})}),(0,r.jsx)(u.C5,{})]})}}),(0,r.jsx)(d.$,{type:"submit",className:"w-full",disabled:!h||E,children:s?S?"Sending Link...":"Send Reset Link":P?"Logging in...":"Login"}),!s&&(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)(d.$,{type:"button",variant:"link",className:"text-primary h-auto p-0 text-sm hover:underline",onClick:()=>{m(!0);let e=x.getValues("email");x.reset({email:e,password:""}),x.clearErrors(),p(!!(e&&e.length>0))},children:"Forget Password?"})}),s&&(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)(d.$,{type:"button",variant:"link",className:"text-primary h-auto p-0 text-sm hover:underline",onClick:C,children:"Back to Login"})})]})})}var T=s(6874),I=s.n(T),F=s(40081);function V(){return(0,r.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,r.jsx)(F.A,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Workflow Builder"}),(0,r.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Log in to access your workflows"})]}),(0,r.jsx)(U,{}),(0,r.jsx)("div",{className:"text-center text-sm",children:(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Don't have an account?"," ",(0,r.jsx)(I(),{href:"/signup",className:"text-primary font-medium hover:underline",children:"Sign up"})]})})]})})}},38378:(e,t,s)=>{"use strict";s.d(t,{h:()=>a});var r=s(34477);let a=(0,r.createServerReference)("7f4f3df198d69edddaa774690ffaa844c9a5b9ebda",r.callServer,void 0,r.findSourceMapURL,"clearAuthCookies")},78749:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},81615:(e,t,s)=>{Promise.resolve().then(s.bind(s,20870))},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[9352,3291,6671,6874,8517,2313,4766,7764,8441,1684,7358],()=>t(81615)),_N_E=e.O()}]);