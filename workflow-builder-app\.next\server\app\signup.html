<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/940018d4805e35d7.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-c24ca988b8c86a1d.js"/><script src="/_next/static/chunks/4bd1b696-35d63e82222b9a20.js" async=""></script><script src="/_next/static/chunks/1684-c6324801e8a4e70b.js" async=""></script><script src="/_next/static/chunks/main-app-adb8bed3920ae4b0.js" async=""></script><script src="/_next/static/chunks/3291-50bd181800545647.js" async=""></script><script src="/_next/static/chunks/6671-a8c26f670d082532.js" async=""></script><script src="/_next/static/chunks/4766-7031d698975a3144.js" async=""></script><script src="/_next/static/chunks/4720-b1b7051d8a12e717.js" async=""></script><script src="/_next/static/chunks/7997-275fa46732e6b474.js" async=""></script><script src="/_next/static/chunks/app/layout-91ca3da93030ddbf.js" async=""></script><script src="/_next/static/chunks/9352-7a620227c8bd8866.js" async=""></script><script src="/_next/static/chunks/6874-fb0c1de9033b59b3.js" async=""></script><script src="/_next/static/chunks/8517-831b5b845916db51.js" async=""></script><script src="/_next/static/chunks/2313-1caf54d29a209c52.js" async=""></script><script src="/_next/static/chunks/8343-624288ba301e067a.js" async=""></script><script src="/_next/static/chunks/7764-5a5c577cb8574a2a.js" async=""></script><script src="/_next/static/chunks/app/signup/page-18c638bf06936851.js" async=""></script><title>Workflow Builder</title><meta name="description" content="Visual workflow builder for automation"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><link rel="icon" href="/favicon.svg" type="image/svg+xml"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 __variable_fb6e91 __variable_3a3c2d antialiased"><script>((e,t,n,r,a,i,o,s)=>{let c=document.documentElement,u=["light","dark"];function l(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&i?a.map(e=>i[e]||e):a;n?(c.classList.remove(...r),c.classList.add(i&&i[t]?i[t]:t)):c.setAttribute(e,t)}),n=t,s&&u.includes(n)&&(c.style.colorScheme=n)}if(r)l(r);else try{let e=localStorage.getItem(t)||n,r=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;l(r)}catch(e){}})("class","theme","system",null,["light","dark"],null,true,true)</script><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><div class="bg-background flex min-h-screen flex-col items-center justify-center p-4"><div class="bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg"><div class="flex flex-col items-center space-y-2"><div class="rounded-md bg-blue-600 p-2 shadow-md"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-workflow h-8 w-8 text-white"><rect width="8" height="8" x="3" y="3" rx="2"></rect><path d="M7 11v4a2 2 0 0 0 2 2h4"></path><rect width="8" height="8" x="13" y="13" rx="2"></rect></svg></div><h1 class="text-center text-2xl font-bold">Workflow Builder</h1><p class="text-muted-foreground text-center text-sm">Create an account to get started</p></div><form class="space-y-6"><div data-slot="form-item" class="grid gap-2"><label data-slot="form-label" class="flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 data-[error=true]:text-destructive" data-error="false" for="«R33rlb»-form-item">Full Name</label><input data-slot="form-control" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive" placeholder="Enter your full name" id="«R33rlb»-form-item" aria-describedby="«R33rlb»-form-item-description" aria-invalid="false" name="fullName" value=""/></div><div data-slot="form-item" class="grid gap-2"><label data-slot="form-label" class="flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 data-[error=true]:text-destructive" data-error="false" for="«R53rlb»-form-item">Email Address</label><input type="email" data-slot="form-control" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive" placeholder="Enter your email address" id="«R53rlb»-form-item" aria-describedby="«R53rlb»-form-item-description" aria-invalid="false" name="email" value=""/></div><div data-slot="form-item" class="grid gap-2"><label data-slot="form-label" class="flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 data-[error=true]:text-destructive" data-error="false" for="«R73rlb»-form-item">Password</label><div class="relative" data-slot="form-control" id="«R73rlb»-form-item" aria-describedby="«R73rlb»-form-item-description" aria-invalid="false"><input type="password" data-slot="input" class="file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive" placeholder="Enter a unique password" name="password" value=""/><button data-slot="button" class="focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 hover:text-accent-foreground dark:hover:bg-accent/50 size-9 absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent" type="button" aria-label="Show password"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye h-4 w-4 text-gray-500"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg></button></div></div><div data-slot="form-item" class="gap-2 flex flex-row items-center"><button type="button" role="checkbox" aria-checked="false" data-state="unchecked" value="on" data-slot="form-control" class="peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50" id="«R93rlb»-form-item" aria-describedby="«R93rlb»-form-item-description" aria-invalid="false"></button><input type="checkbox" aria-hidden="true" style="transform:translateX(-100%);position:absolute;pointer-events:none;opacity:0;margin:0" tabindex="-1" value="on"/><div class=""><label data-slot="form-label" class="flex items-center gap-2 select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50 data-[error=true]:text-destructive text-sm font-semibold" data-error="false" for="«R93rlb»-form-item">I agree to the<!-- --> <a href="#" class="text-primary hover:underline hover:opacity-80">Terms</a> <!-- -->&amp;<!-- --> <a href="#" class="text-primary hover:underline hover:opacity-80">Privacy Policy</a></label></div></div><div class="flex flex-col gap-4 py-4 text-sm"><h3 class="mb-3 font-medium">Your password must have:</h3><div class="space-y-4"><div class="flex items-center gap-2"><span class="text-muted-foreground">6-15 characters</span></div><div class="flex items-center gap-2"><span class="text-muted-foreground">At least one number (0-9)</span></div><div class="flex items-center gap-2"><span class="text-muted-foreground">At least one symbol (@, #, $, %, etc.)</span></div></div></div><button data-slot="button" class="focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs h-9 px-4 py-2 has-[&gt;svg]:px-3 w-full" type="submit" disabled="">Signup</button></form><div class="text-center text-sm"><p class="text-muted-foreground">Already have an account?<!-- --> <a class="text-primary font-medium hover:underline" href="/login">Log in</a></p></div></div></div><script src="/_next/static/chunks/webpack-c24ca988b8c86a1d.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[77890,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"ThemeProvider\"]\n3:I[70139,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"QueryProvider\"]\n4:I[94819,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"AuthProvider\"]\n5:I[42618,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"ToastProvider\"]\n6:I[87555,[],\"\"]\n7:I[31295,[],\"\"]\n8:I[90894,[],\"ClientPageRoot\"]\n9:I[46818,[\"9352\",\"static/chunks/9352-7a620227c8bd8866.js\",\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"6874\",\"static/chunks/6874-fb0c1de9033b59b3.js\",\"8517\",\"static/chunks/8517-831b5b845916db51.js\",\"2313\",\"static/chunks/2313-1caf54d29a209c52.js\",\"8343\",\"static/chunks/8343-624288ba301e067a.js\",\"7764\",\"static/chunks/7764-5a5c577cb8574a2a.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"879\",\"static/chunks/app/signup/page-18c638bf06936851.js\"],\"default\"]\nc:I[59665,[],\"OutletBoundary\"]\nf:I[59665,[],\"ViewportBoundary\"]\n11:I[59665,[],\"MetadataBoundary\"]\n13:I[26614,[],\"\"]\n:HL[\"/_next/static/css/940018d4805e35d7.cs"])</script><script>self.__next_f.push([1,"s\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"1QAyynrQ8SZ0fgEBA9EXT\",\"p\":\"\",\"c\":[\"\",\"signup\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"signup\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/940018d4805e35d7.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 __variable_fb6e91 __variable_3a3c2d antialiased\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[[\"$\",\"$L5\",null,{}],[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]}]}]}]}]]}],{\"children\":[\"signup\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L8\",null,{\"Component\":\"$9\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@a\",\"$@b\"]}],\"$undefined\",null,[\"$\",\"$Lc\",null,{\"children\":[\"$Ld\",\"$Le\",null]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"RbhL0WZq0umLa_fJxePsD\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],[\"$\",\"$L11\",null,{\"children\":\"$L12\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$13\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"a:{}\nb:{}\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nd:null\n"])</script><script>self.__next_f.push([1,"e:null\n12:[[\"$\",\"title\",\"0\",{\"children\":\"Workflow Builder\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Visual workflow builder for automation\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.svg\",\"type\":\"image/svg+xml\"}]]\n"])</script></body></html>