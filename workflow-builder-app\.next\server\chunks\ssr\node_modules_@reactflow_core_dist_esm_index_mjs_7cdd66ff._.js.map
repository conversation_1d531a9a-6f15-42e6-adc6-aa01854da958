{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/node_modules/%40reactflow/core/dist/esm/index.mjs"], "sourcesContent": ["import React, { createContext, useContext, useMemo, memo, useRef, useState, useEffect, forwardRef, useCallback } from 'react';\nimport cc from 'classcat';\nimport { useStoreWithEqualityFn, createWithEqualityFn } from 'zustand/traditional';\nimport { shallow } from 'zustand/shallow';\nimport { zoomIdentity, zoom } from 'd3-zoom';\nimport { select, pointer } from 'd3-selection';\nimport { drag } from 'd3-drag';\nimport { createPortal } from 'react-dom';\n\nconst StoreContext = createContext(null);\nconst Provider$1 = StoreContext.Provider;\n\nconst errorMessages = {\n    error001: () => '[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001',\n    error002: () => \"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.\",\n    error003: (nodeType) => `Node type \"${nodeType}\" not found. Using fallback type \"default\".`,\n    error004: () => 'The React Flow parent container needs a width and a height to render the graph.',\n    error005: () => 'Only child nodes can use a parent extent.',\n    error006: () => \"Can't create edge. An edge needs a source and a target.\",\n    error007: (id) => `The old edge with id=${id} does not exist.`,\n    error009: (type) => `Marker type \"${type}\" doesn't exist.`,\n    error008: (sourceHandle, edge) => `Couldn't create edge for ${!sourceHandle ? 'source' : 'target'} handle id: \"${!sourceHandle ? edge.sourceHandle : edge.targetHandle}\", edge id: ${edge.id}.`,\n    error010: () => 'Handle: No node id found. Make sure to only use a Handle inside a custom Node.',\n    error011: (edgeType) => `Edge type \"${edgeType}\" not found. Using fallback type \"default\".`,\n    error012: (id) => `Node with id \"${id}\" does not exist, it may have been removed. This can happen when a node is deleted before the \"onNodeClick\" handler is called.`,\n};\n\nconst zustandErrorMessage = errorMessages['error001']();\nfunction useStore(selector, equalityFn) {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useStoreWithEqualityFn(store, selector, equalityFn);\n}\nconst useStoreApi = () => {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useMemo(() => ({\n        getState: store.getState,\n        setState: store.setState,\n        subscribe: store.subscribe,\n        destroy: store.destroy,\n    }), [store]);\n};\n\nconst selector$g = (s) => (s.userSelectionActive ? 'none' : 'all');\nfunction Panel({ position, children, className, style, ...rest }) {\n    const pointerEvents = useStore(selector$g);\n    const positionClasses = `${position}`.split('-');\n    return (React.createElement(\"div\", { className: cc(['react-flow__panel', className, ...positionClasses]), style: { ...style, pointerEvents }, ...rest }, children));\n}\n\nfunction Attribution({ proOptions, position = 'bottom-right' }) {\n    if (proOptions?.hideAttribution) {\n        return null;\n    }\n    return (React.createElement(Panel, { position: position, className: \"react-flow__attribution\", \"data-message\": \"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro\" },\n        React.createElement(\"a\", { href: \"https://reactflow.dev\", target: \"_blank\", rel: \"noopener noreferrer\", \"aria-label\": \"React Flow attribution\" }, \"React Flow\")));\n}\n\nconst EdgeText = ({ x, y, label, labelStyle = {}, labelShowBg = true, labelBgStyle = {}, labelBgPadding = [2, 4], labelBgBorderRadius = 2, children, className, ...rest }) => {\n    const edgeRef = useRef(null);\n    const [edgeTextBbox, setEdgeTextBbox] = useState({ x: 0, y: 0, width: 0, height: 0 });\n    const edgeTextClasses = cc(['react-flow__edge-textwrapper', className]);\n    useEffect(() => {\n        if (edgeRef.current) {\n            const textBbox = edgeRef.current.getBBox();\n            setEdgeTextBbox({\n                x: textBbox.x,\n                y: textBbox.y,\n                width: textBbox.width,\n                height: textBbox.height,\n            });\n        }\n    }, [label]);\n    if (typeof label === 'undefined' || !label) {\n        return null;\n    }\n    return (React.createElement(\"g\", { transform: `translate(${x - edgeTextBbox.width / 2} ${y - edgeTextBbox.height / 2})`, className: edgeTextClasses, visibility: edgeTextBbox.width ? 'visible' : 'hidden', ...rest },\n        labelShowBg && (React.createElement(\"rect\", { width: edgeTextBbox.width + 2 * labelBgPadding[0], x: -labelBgPadding[0], y: -labelBgPadding[1], height: edgeTextBbox.height + 2 * labelBgPadding[1], className: \"react-flow__edge-textbg\", style: labelBgStyle, rx: labelBgBorderRadius, ry: labelBgBorderRadius })),\n        React.createElement(\"text\", { className: \"react-flow__edge-text\", y: edgeTextBbox.height / 2, dy: \"0.3em\", ref: edgeRef, style: labelStyle }, label),\n        children));\n};\nvar EdgeText$1 = memo(EdgeText);\n\nconst getDimensions = (node) => ({\n    width: node.offsetWidth,\n    height: node.offsetHeight,\n});\nconst clamp = (val, min = 0, max = 1) => Math.min(Math.max(val, min), max);\nconst clampPosition = (position = { x: 0, y: 0 }, extent) => ({\n    x: clamp(position.x, extent[0][0], extent[1][0]),\n    y: clamp(position.y, extent[0][1], extent[1][1]),\n});\n// returns a number between 0 and 1 that represents the velocity of the movement\n// when the mouse is close to the edge of the canvas\nconst calcAutoPanVelocity = (value, min, max) => {\n    if (value < min) {\n        return clamp(Math.abs(value - min), 1, 50) / 50;\n    }\n    else if (value > max) {\n        return -clamp(Math.abs(value - max), 1, 50) / 50;\n    }\n    return 0;\n};\nconst calcAutoPan = (pos, bounds) => {\n    const xMovement = calcAutoPanVelocity(pos.x, 35, bounds.width - 35) * 20;\n    const yMovement = calcAutoPanVelocity(pos.y, 35, bounds.height - 35) * 20;\n    return [xMovement, yMovement];\n};\nconst getHostForElement = (element) => element.getRootNode?.() || window?.document;\nconst getBoundsOfBoxes = (box1, box2) => ({\n    x: Math.min(box1.x, box2.x),\n    y: Math.min(box1.y, box2.y),\n    x2: Math.max(box1.x2, box2.x2),\n    y2: Math.max(box1.y2, box2.y2),\n});\nconst rectToBox = ({ x, y, width, height }) => ({\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n});\nconst boxToRect = ({ x, y, x2, y2 }) => ({\n    x,\n    y,\n    width: x2 - x,\n    height: y2 - y,\n});\nconst nodeToRect = (node) => ({\n    ...(node.positionAbsolute || { x: 0, y: 0 }),\n    width: node.width || 0,\n    height: node.height || 0,\n});\nconst getBoundsOfRects = (rect1, rect2) => boxToRect(getBoundsOfBoxes(rectToBox(rect1), rectToBox(rect2)));\nconst getOverlappingArea = (rectA, rectB) => {\n    const xOverlap = Math.max(0, Math.min(rectA.x + rectA.width, rectB.x + rectB.width) - Math.max(rectA.x, rectB.x));\n    const yOverlap = Math.max(0, Math.min(rectA.y + rectA.height, rectB.y + rectB.height) - Math.max(rectA.y, rectB.y));\n    return Math.ceil(xOverlap * yOverlap);\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isRectObject = (obj) => isNumeric(obj.width) && isNumeric(obj.height) && isNumeric(obj.x) && isNumeric(obj.y);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nconst isNumeric = (n) => !isNaN(n) && isFinite(n);\nconst internalsSymbol = Symbol.for('internals');\n// used for a11y key board controls for nodes and edges\nconst elementSelectionKeys = ['Enter', ' ', 'Escape'];\nconst devWarn = (id, message) => {\n    if (process.env.NODE_ENV === 'development') {\n        console.warn(`[React Flow]: ${message} Help: https://reactflow.dev/error#${id}`);\n    }\n};\nconst isReactKeyboardEvent = (event) => 'nativeEvent' in event;\nfunction isInputDOMNode(event) {\n    const kbEvent = isReactKeyboardEvent(event) ? event.nativeEvent : event;\n    // using composed path for handling shadow dom\n    const target = (kbEvent.composedPath?.()?.[0] || event.target);\n    const isInput = ['INPUT', 'SELECT', 'TEXTAREA'].includes(target?.nodeName) || target?.hasAttribute('contenteditable');\n    // when an input field is focused we don't want to trigger deletion or movement of nodes\n    return isInput || !!target?.closest('.nokey');\n}\nconst isMouseEvent = (event) => 'clientX' in event;\nconst getEventPosition = (event, bounds) => {\n    const isMouseTriggered = isMouseEvent(event);\n    const evtX = isMouseTriggered ? event.clientX : event.touches?.[0].clientX;\n    const evtY = isMouseTriggered ? event.clientY : event.touches?.[0].clientY;\n    return {\n        x: evtX - (bounds?.left ?? 0),\n        y: evtY - (bounds?.top ?? 0),\n    };\n};\nconst isMacOs = () => typeof navigator !== 'undefined' && navigator?.userAgent?.indexOf('Mac') >= 0;\n\nconst BaseEdge = ({ id, path, labelX, labelY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth = 20, }) => {\n    return (React.createElement(React.Fragment, null,\n        React.createElement(\"path\", { id: id, style: style, d: path, fill: \"none\", className: \"react-flow__edge-path\", markerEnd: markerEnd, markerStart: markerStart }),\n        interactionWidth && (React.createElement(\"path\", { d: path, fill: \"none\", strokeOpacity: 0, strokeWidth: interactionWidth, className: \"react-flow__edge-interaction\" })),\n        label && isNumeric(labelX) && isNumeric(labelY) ? (React.createElement(EdgeText$1, { x: labelX, y: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius })) : null));\n};\nBaseEdge.displayName = 'BaseEdge';\n\nconst getMarkerEnd = (markerType, markerEndId) => {\n    if (typeof markerEndId !== 'undefined' && markerEndId) {\n        return `url(#${markerEndId})`;\n    }\n    return typeof markerType !== 'undefined' ? `url(#react-flow__${markerType})` : 'none';\n};\nfunction getMouseHandler$1(id, getState, handler) {\n    return handler === undefined\n        ? handler\n        : (event) => {\n            const edge = getState().edges.find((e) => e.id === id);\n            if (edge) {\n                handler(event, { ...edge });\n            }\n        };\n}\n// this is used for straight edges and simple smoothstep edges (LTR, RTL, BTT, TTB)\nfunction getEdgeCenter({ sourceX, sourceY, targetX, targetY, }) {\n    const xOffset = Math.abs(targetX - sourceX) / 2;\n    const centerX = targetX < sourceX ? targetX + xOffset : targetX - xOffset;\n    const yOffset = Math.abs(targetY - sourceY) / 2;\n    const centerY = targetY < sourceY ? targetY + yOffset : targetY - yOffset;\n    return [centerX, centerY, xOffset, yOffset];\n}\nfunction getBezierEdgeCenter({ sourceX, sourceY, targetX, targetY, sourceControlX, sourceControlY, targetControlX, targetControlY, }) {\n    // cubic bezier t=0.5 mid point, not the actual mid point, but easy to calculate\n    // https://stackoverflow.com/questions/67516101/how-to-find-distance-mid-point-of-bezier-curve\n    const centerX = sourceX * 0.125 + sourceControlX * 0.375 + targetControlX * 0.375 + targetX * 0.125;\n    const centerY = sourceY * 0.125 + sourceControlY * 0.375 + targetControlY * 0.375 + targetY * 0.125;\n    const offsetX = Math.abs(centerX - sourceX);\n    const offsetY = Math.abs(centerY - sourceY);\n    return [centerX, centerY, offsetX, offsetY];\n}\n\nvar ConnectionMode;\n(function (ConnectionMode) {\n    ConnectionMode[\"Strict\"] = \"strict\";\n    ConnectionMode[\"Loose\"] = \"loose\";\n})(ConnectionMode || (ConnectionMode = {}));\nvar PanOnScrollMode;\n(function (PanOnScrollMode) {\n    PanOnScrollMode[\"Free\"] = \"free\";\n    PanOnScrollMode[\"Vertical\"] = \"vertical\";\n    PanOnScrollMode[\"Horizontal\"] = \"horizontal\";\n})(PanOnScrollMode || (PanOnScrollMode = {}));\nvar SelectionMode;\n(function (SelectionMode) {\n    SelectionMode[\"Partial\"] = \"partial\";\n    SelectionMode[\"Full\"] = \"full\";\n})(SelectionMode || (SelectionMode = {}));\n\nvar ConnectionLineType;\n(function (ConnectionLineType) {\n    ConnectionLineType[\"Bezier\"] = \"default\";\n    ConnectionLineType[\"Straight\"] = \"straight\";\n    ConnectionLineType[\"Step\"] = \"step\";\n    ConnectionLineType[\"SmoothStep\"] = \"smoothstep\";\n    ConnectionLineType[\"SimpleBezier\"] = \"simplebezier\";\n})(ConnectionLineType || (ConnectionLineType = {}));\nvar MarkerType;\n(function (MarkerType) {\n    MarkerType[\"Arrow\"] = \"arrow\";\n    MarkerType[\"ArrowClosed\"] = \"arrowclosed\";\n})(MarkerType || (MarkerType = {}));\n\nvar Position;\n(function (Position) {\n    Position[\"Left\"] = \"left\";\n    Position[\"Top\"] = \"top\";\n    Position[\"Right\"] = \"right\";\n    Position[\"Bottom\"] = \"bottom\";\n})(Position || (Position = {}));\n\nfunction getControl({ pos, x1, y1, x2, y2 }) {\n    if (pos === Position.Left || pos === Position.Right) {\n        return [0.5 * (x1 + x2), y1];\n    }\n    return [x1, 0.5 * (y1 + y2)];\n}\nfunction getSimpleBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, }) {\n    const [sourceControlX, sourceControlY] = getControl({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n    });\n    const [targetControlX, targetControlY] = getControl({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nconst SimpleBezierEdge = memo(({ sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n    const [path, labelX, labelY] = getSimpleBezierPath({\n        sourceX,\n        sourceY,\n        sourcePosition,\n        targetX,\n        targetY,\n        targetPosition,\n    });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nSimpleBezierEdge.displayName = 'SimpleBezierEdge';\n\nconst handleDirections = {\n    [Position.Left]: { x: -1, y: 0 },\n    [Position.Right]: { x: 1, y: 0 },\n    [Position.Top]: { x: 0, y: -1 },\n    [Position.Bottom]: { x: 0, y: 1 },\n};\nconst getDirection = ({ source, sourcePosition = Position.Bottom, target, }) => {\n    if (sourcePosition === Position.Left || sourcePosition === Position.Right) {\n        return source.x < target.x ? { x: 1, y: 0 } : { x: -1, y: 0 };\n    }\n    return source.y < target.y ? { x: 0, y: 1 } : { x: 0, y: -1 };\n};\nconst distance = (a, b) => Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));\n// ith this function we try to mimic a orthogonal edge routing behaviour\n// It's not as good as a real orthogonal edge routing but it's faster and good enough as a default for step and smooth step edges\nfunction getPoints({ source, sourcePosition = Position.Bottom, target, targetPosition = Position.Top, center, offset, }) {\n    const sourceDir = handleDirections[sourcePosition];\n    const targetDir = handleDirections[targetPosition];\n    const sourceGapped = { x: source.x + sourceDir.x * offset, y: source.y + sourceDir.y * offset };\n    const targetGapped = { x: target.x + targetDir.x * offset, y: target.y + targetDir.y * offset };\n    const dir = getDirection({\n        source: sourceGapped,\n        sourcePosition,\n        target: targetGapped,\n    });\n    const dirAccessor = dir.x !== 0 ? 'x' : 'y';\n    const currDir = dir[dirAccessor];\n    let points = [];\n    let centerX, centerY;\n    const sourceGapOffset = { x: 0, y: 0 };\n    const targetGapOffset = { x: 0, y: 0 };\n    const [defaultCenterX, defaultCenterY, defaultOffsetX, defaultOffsetY] = getEdgeCenter({\n        sourceX: source.x,\n        sourceY: source.y,\n        targetX: target.x,\n        targetY: target.y,\n    });\n    // opposite handle positions, default case\n    if (sourceDir[dirAccessor] * targetDir[dirAccessor] === -1) {\n        centerX = center.x ?? defaultCenterX;\n        centerY = center.y ?? defaultCenterY;\n        //    --->\n        //    |\n        // >---\n        const verticalSplit = [\n            { x: centerX, y: sourceGapped.y },\n            { x: centerX, y: targetGapped.y },\n        ];\n        //    |\n        //  ---\n        //  |\n        const horizontalSplit = [\n            { x: sourceGapped.x, y: centerY },\n            { x: targetGapped.x, y: centerY },\n        ];\n        if (sourceDir[dirAccessor] === currDir) {\n            points = dirAccessor === 'x' ? verticalSplit : horizontalSplit;\n        }\n        else {\n            points = dirAccessor === 'x' ? horizontalSplit : verticalSplit;\n        }\n    }\n    else {\n        // sourceTarget means we take x from source and y from target, targetSource is the opposite\n        const sourceTarget = [{ x: sourceGapped.x, y: targetGapped.y }];\n        const targetSource = [{ x: targetGapped.x, y: sourceGapped.y }];\n        // this handles edges with same handle positions\n        if (dirAccessor === 'x') {\n            points = sourceDir.x === currDir ? targetSource : sourceTarget;\n        }\n        else {\n            points = sourceDir.y === currDir ? sourceTarget : targetSource;\n        }\n        if (sourcePosition === targetPosition) {\n            const diff = Math.abs(source[dirAccessor] - target[dirAccessor]);\n            // if an edge goes from right to right for example (sourcePosition === targetPosition) and the distance between source.x and target.x is less than the offset, the added point and the gapped source/target will overlap. This leads to a weird edge path. To avoid this we add a gapOffset to the source/target\n            if (diff <= offset) {\n                const gapOffset = Math.min(offset - 1, offset - diff);\n                if (sourceDir[dirAccessor] === currDir) {\n                    sourceGapOffset[dirAccessor] = (sourceGapped[dirAccessor] > source[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n                else {\n                    targetGapOffset[dirAccessor] = (targetGapped[dirAccessor] > target[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n            }\n        }\n        // these are conditions for handling mixed handle positions like Right -> Bottom for example\n        if (sourcePosition !== targetPosition) {\n            const dirAccessorOpposite = dirAccessor === 'x' ? 'y' : 'x';\n            const isSameDir = sourceDir[dirAccessor] === targetDir[dirAccessorOpposite];\n            const sourceGtTargetOppo = sourceGapped[dirAccessorOpposite] > targetGapped[dirAccessorOpposite];\n            const sourceLtTargetOppo = sourceGapped[dirAccessorOpposite] < targetGapped[dirAccessorOpposite];\n            const flipSourceTarget = (sourceDir[dirAccessor] === 1 && ((!isSameDir && sourceGtTargetOppo) || (isSameDir && sourceLtTargetOppo))) ||\n                (sourceDir[dirAccessor] !== 1 && ((!isSameDir && sourceLtTargetOppo) || (isSameDir && sourceGtTargetOppo)));\n            if (flipSourceTarget) {\n                points = dirAccessor === 'x' ? sourceTarget : targetSource;\n            }\n        }\n        const sourceGapPoint = { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y };\n        const targetGapPoint = { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y };\n        const maxXDistance = Math.max(Math.abs(sourceGapPoint.x - points[0].x), Math.abs(targetGapPoint.x - points[0].x));\n        const maxYDistance = Math.max(Math.abs(sourceGapPoint.y - points[0].y), Math.abs(targetGapPoint.y - points[0].y));\n        // we want to place the label on the longest segment of the edge\n        if (maxXDistance >= maxYDistance) {\n            centerX = (sourceGapPoint.x + targetGapPoint.x) / 2;\n            centerY = points[0].y;\n        }\n        else {\n            centerX = points[0].x;\n            centerY = (sourceGapPoint.y + targetGapPoint.y) / 2;\n        }\n    }\n    const pathPoints = [\n        source,\n        { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y },\n        ...points,\n        { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y },\n        target,\n    ];\n    return [pathPoints, centerX, centerY, defaultOffsetX, defaultOffsetY];\n}\nfunction getBend(a, b, c, size) {\n    const bendSize = Math.min(distance(a, b) / 2, distance(b, c) / 2, size);\n    const { x, y } = b;\n    // no bend\n    if ((a.x === x && x === c.x) || (a.y === y && y === c.y)) {\n        return `L${x} ${y}`;\n    }\n    // first segment is horizontal\n    if (a.y === y) {\n        const xDir = a.x < c.x ? -1 : 1;\n        const yDir = a.y < c.y ? 1 : -1;\n        return `L ${x + bendSize * xDir},${y}Q ${x},${y} ${x},${y + bendSize * yDir}`;\n    }\n    const xDir = a.x < c.x ? 1 : -1;\n    const yDir = a.y < c.y ? -1 : 1;\n    return `L ${x},${y + bendSize * yDir}Q ${x},${y} ${x + bendSize * xDir},${y}`;\n}\nfunction getSmoothStepPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, borderRadius = 5, centerX, centerY, offset = 20, }) {\n    const [points, labelX, labelY, offsetX, offsetY] = getPoints({\n        source: { x: sourceX, y: sourceY },\n        sourcePosition,\n        target: { x: targetX, y: targetY },\n        targetPosition,\n        center: { x: centerX, y: centerY },\n        offset,\n    });\n    const path = points.reduce((res, p, i) => {\n        let segment = '';\n        if (i > 0 && i < points.length - 1) {\n            segment = getBend(points[i - 1], p, points[i + 1], borderRadius);\n        }\n        else {\n            segment = `${i === 0 ? 'M' : 'L'}${p.x} ${p.y}`;\n        }\n        res += segment;\n        return res;\n    }, '');\n    return [path, labelX, labelY, offsetX, offsetY];\n}\nconst SmoothStepEdge = memo(({ sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition = Position.Bottom, targetPosition = Position.Top, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n    const [path, labelX, labelY] = getSmoothStepPath({\n        sourceX,\n        sourceY,\n        sourcePosition,\n        targetX,\n        targetY,\n        targetPosition,\n        borderRadius: pathOptions?.borderRadius,\n        offset: pathOptions?.offset,\n    });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nSmoothStepEdge.displayName = 'SmoothStepEdge';\n\nconst StepEdge = memo((props) => (React.createElement(SmoothStepEdge, { ...props, pathOptions: useMemo(() => ({ borderRadius: 0, offset: props.pathOptions?.offset }), [props.pathOptions?.offset]) })));\nStepEdge.displayName = 'StepEdge';\n\nfunction getStraightPath({ sourceX, sourceY, targetX, targetY, }) {\n    const [labelX, labelY, offsetX, offsetY] = getEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n    });\n    return [`M ${sourceX},${sourceY}L ${targetX},${targetY}`, labelX, labelY, offsetX, offsetY];\n}\nconst StraightEdge = memo(({ sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n    const [path, labelX, labelY] = getStraightPath({ sourceX, sourceY, targetX, targetY });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nStraightEdge.displayName = 'StraightEdge';\n\nfunction calculateControlOffset(distance, curvature) {\n    if (distance >= 0) {\n        return 0.5 * distance;\n    }\n    return curvature * 25 * Math.sqrt(-distance);\n}\nfunction getControlWithCurvature({ pos, x1, y1, x2, y2, c }) {\n    switch (pos) {\n        case Position.Left:\n            return [x1 - calculateControlOffset(x1 - x2, c), y1];\n        case Position.Right:\n            return [x1 + calculateControlOffset(x2 - x1, c), y1];\n        case Position.Top:\n            return [x1, y1 - calculateControlOffset(y1 - y2, c)];\n        case Position.Bottom:\n            return [x1, y1 + calculateControlOffset(y2 - y1, c)];\n    }\n}\nfunction getBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, curvature = 0.25, }) {\n    const [sourceControlX, sourceControlY] = getControlWithCurvature({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n        c: curvature,\n    });\n    const [targetControlX, targetControlY] = getControlWithCurvature({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n        c: curvature,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nconst BezierEdge = memo(({ sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n    const [path, labelX, labelY] = getBezierPath({\n        sourceX,\n        sourceY,\n        sourcePosition,\n        targetX,\n        targetY,\n        targetPosition,\n        curvature: pathOptions?.curvature,\n    });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nBezierEdge.displayName = 'BezierEdge';\n\nconst NodeIdContext = createContext(null);\nconst Provider = NodeIdContext.Provider;\nNodeIdContext.Consumer;\nconst useNodeId = () => {\n    const nodeId = useContext(NodeIdContext);\n    return nodeId;\n};\n\nconst isEdge = (element) => 'id' in element && 'source' in element && 'target' in element;\nconst isNode = (element) => 'id' in element && !('source' in element) && !('target' in element);\nconst getOutgoers = (node, nodes, edges) => {\n    if (!isNode(node)) {\n        return [];\n    }\n    const outgoerIds = edges.filter((e) => e.source === node.id).map((e) => e.target);\n    return nodes.filter((n) => outgoerIds.includes(n.id));\n};\nconst getIncomers = (node, nodes, edges) => {\n    if (!isNode(node)) {\n        return [];\n    }\n    const incomersIds = edges.filter((e) => e.target === node.id).map((e) => e.source);\n    return nodes.filter((n) => incomersIds.includes(n.id));\n};\nconst getEdgeId = ({ source, sourceHandle, target, targetHandle }) => `reactflow__edge-${source}${sourceHandle || ''}-${target}${targetHandle || ''}`;\nconst getMarkerId = (marker, rfId) => {\n    if (typeof marker === 'undefined') {\n        return '';\n    }\n    if (typeof marker === 'string') {\n        return marker;\n    }\n    const idPrefix = rfId ? `${rfId}__` : '';\n    return `${idPrefix}${Object.keys(marker)\n        .sort()\n        .map((key) => `${key}=${marker[key]}`)\n        .join('&')}`;\n};\nconst connectionExists = (edge, edges) => {\n    return edges.some((el) => el.source === edge.source &&\n        el.target === edge.target &&\n        (el.sourceHandle === edge.sourceHandle || (!el.sourceHandle && !edge.sourceHandle)) &&\n        (el.targetHandle === edge.targetHandle || (!el.targetHandle && !edge.targetHandle)));\n};\nconst addEdge = (edgeParams, edges) => {\n    if (!edgeParams.source || !edgeParams.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    let edge;\n    if (isEdge(edgeParams)) {\n        edge = { ...edgeParams };\n    }\n    else {\n        edge = {\n            ...edgeParams,\n            id: getEdgeId(edgeParams),\n        };\n    }\n    if (connectionExists(edge, edges)) {\n        return edges;\n    }\n    return edges.concat(edge);\n};\nconst reconnectEdge = (oldEdge, newConnection, edges, options = { shouldReplaceId: true }) => {\n    const { id: oldEdgeId, ...rest } = oldEdge;\n    if (!newConnection.source || !newConnection.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    const foundEdge = edges.find((e) => e.id === oldEdgeId);\n    if (!foundEdge) {\n        devWarn('007', errorMessages['error007'](oldEdgeId));\n        return edges;\n    }\n    // Remove old edge and create the new edge with parameters of old edge.\n    const edge = {\n        ...rest,\n        id: options.shouldReplaceId ? getEdgeId(newConnection) : oldEdgeId,\n        source: newConnection.source,\n        target: newConnection.target,\n        sourceHandle: newConnection.sourceHandle,\n        targetHandle: newConnection.targetHandle,\n    };\n    return edges.filter((e) => e.id !== oldEdgeId).concat(edge);\n};\n/**\n *\n * @deprecated Use `reconnectEdge` instead.\n */\nconst updateEdge = (oldEdge, newConnection, edges, options = { shouldReplaceId: true }) => {\n    console.warn('[DEPRECATED] `updateEdge` is deprecated. Instead use `reconnectEdge` https://reactflow.dev/api-reference/utils/reconnect-edge');\n    return reconnectEdge(oldEdge, newConnection, edges, options);\n};\nconst pointToRendererPoint = ({ x, y }, [tx, ty, tScale], snapToGrid, [snapX, snapY]) => {\n    const position = {\n        x: (x - tx) / tScale,\n        y: (y - ty) / tScale,\n    };\n    if (snapToGrid) {\n        return {\n            x: snapX * Math.round(position.x / snapX),\n            y: snapY * Math.round(position.y / snapY),\n        };\n    }\n    return position;\n};\nconst rendererPointToPoint = ({ x, y }, [tx, ty, tScale]) => {\n    return {\n        x: x * tScale + tx,\n        y: y * tScale + ty,\n    };\n};\nconst getNodePositionWithOrigin = (node, nodeOrigin = [0, 0]) => {\n    if (!node) {\n        return {\n            x: 0,\n            y: 0,\n            positionAbsolute: {\n                x: 0,\n                y: 0,\n            },\n        };\n    }\n    const offsetX = (node.width ?? 0) * nodeOrigin[0];\n    const offsetY = (node.height ?? 0) * nodeOrigin[1];\n    const position = {\n        x: node.position.x - offsetX,\n        y: node.position.y - offsetY,\n    };\n    return {\n        ...position,\n        positionAbsolute: node.positionAbsolute\n            ? {\n                x: node.positionAbsolute.x - offsetX,\n                y: node.positionAbsolute.y - offsetY,\n            }\n            : position,\n    };\n};\nconst getNodesBounds = (nodes, nodeOrigin = [0, 0]) => {\n    if (nodes.length === 0) {\n        return { x: 0, y: 0, width: 0, height: 0 };\n    }\n    const box = nodes.reduce((currBox, node) => {\n        const { x, y } = getNodePositionWithOrigin(node, nodeOrigin).positionAbsolute;\n        return getBoundsOfBoxes(currBox, rectToBox({\n            x,\n            y,\n            width: node.width || 0,\n            height: node.height || 0,\n        }));\n    }, { x: Infinity, y: Infinity, x2: -Infinity, y2: -Infinity });\n    return boxToRect(box);\n};\n// @deprecated Use `getNodesBounds`.\nconst getRectOfNodes = (nodes, nodeOrigin = [0, 0]) => {\n    console.warn('[DEPRECATED] `getRectOfNodes` is deprecated. Instead use `getNodesBounds` https://reactflow.dev/api-reference/utils/get-nodes-bounds.');\n    return getNodesBounds(nodes, nodeOrigin);\n};\nconst getNodesInside = (nodeInternals, rect, [tx, ty, tScale] = [0, 0, 1], partially = false, \n// set excludeNonSelectableNodes if you want to pay attention to the nodes \"selectable\" attribute\nexcludeNonSelectableNodes = false, nodeOrigin = [0, 0]) => {\n    const paneRect = {\n        x: (rect.x - tx) / tScale,\n        y: (rect.y - ty) / tScale,\n        width: rect.width / tScale,\n        height: rect.height / tScale,\n    };\n    const visibleNodes = [];\n    nodeInternals.forEach((node) => {\n        const { width, height, selectable = true, hidden = false } = node;\n        if ((excludeNonSelectableNodes && !selectable) || hidden) {\n            return false;\n        }\n        const { positionAbsolute } = getNodePositionWithOrigin(node, nodeOrigin);\n        const nodeRect = {\n            x: positionAbsolute.x,\n            y: positionAbsolute.y,\n            width: width || 0,\n            height: height || 0,\n        };\n        const overlappingArea = getOverlappingArea(paneRect, nodeRect);\n        const notInitialized = typeof width === 'undefined' || typeof height === 'undefined' || width === null || height === null;\n        const partiallyVisible = partially && overlappingArea > 0;\n        const area = (width || 0) * (height || 0);\n        const isVisible = notInitialized || partiallyVisible || overlappingArea >= area;\n        if (isVisible || node.dragging) {\n            visibleNodes.push(node);\n        }\n    });\n    return visibleNodes;\n};\nconst getConnectedEdges = (nodes, edges) => {\n    const nodeIds = nodes.map((node) => node.id);\n    return edges.filter((edge) => nodeIds.includes(edge.source) || nodeIds.includes(edge.target));\n};\n// @deprecated Use `getViewportForBounds`.\nconst getTransformForBounds = (bounds, width, height, minZoom, maxZoom, padding = 0.1) => {\n    const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, padding);\n    console.warn('[DEPRECATED] `getTransformForBounds` is deprecated. Instead use `getViewportForBounds`. Beware that the return value is type Viewport (`{ x: number, y: number, zoom: number }`) instead of Transform (`[number, number, number]`). https://reactflow.dev/api-reference/utils/get-viewport-for-bounds');\n    return [x, y, zoom];\n};\nconst getViewportForBounds = (bounds, width, height, minZoom, maxZoom, padding = 0.1) => {\n    const xZoom = width / (bounds.width * (1 + padding));\n    const yZoom = height / (bounds.height * (1 + padding));\n    const zoom = Math.min(xZoom, yZoom);\n    const clampedZoom = clamp(zoom, minZoom, maxZoom);\n    const boundsCenterX = bounds.x + bounds.width / 2;\n    const boundsCenterY = bounds.y + bounds.height / 2;\n    const x = width / 2 - boundsCenterX * clampedZoom;\n    const y = height / 2 - boundsCenterY * clampedZoom;\n    return { x, y, zoom: clampedZoom };\n};\nconst getD3Transition = (selection, duration = 0) => {\n    return selection.transition().duration(duration);\n};\n\n// this functions collects all handles and adds an absolute position\n// so that we can later find the closest handle to the mouse position\nfunction getHandles(node, handleBounds, type, currentHandle) {\n    return (handleBounds[type] || []).reduce((res, h) => {\n        if (`${node.id}-${h.id}-${type}` !== currentHandle) {\n            res.push({\n                id: h.id || null,\n                type,\n                nodeId: node.id,\n                x: (node.positionAbsolute?.x ?? 0) + h.x + h.width / 2,\n                y: (node.positionAbsolute?.y ?? 0) + h.y + h.height / 2,\n            });\n        }\n        return res;\n    }, []);\n}\nfunction getClosestHandle(event, doc, pos, connectionRadius, handles, validator) {\n    // we always want to prioritize the handle below the mouse cursor over the closest distance handle,\n    // because it could be that the center of another handle is closer to the mouse pointer than the handle below the cursor\n    const { x, y } = getEventPosition(event);\n    const domNodes = doc.elementsFromPoint(x, y);\n    const handleBelow = domNodes.find((el) => el.classList.contains('react-flow__handle'));\n    if (handleBelow) {\n        const handleNodeId = handleBelow.getAttribute('data-nodeid');\n        if (handleNodeId) {\n            const handleType = getHandleType(undefined, handleBelow);\n            const handleId = handleBelow.getAttribute('data-handleid');\n            const validHandleResult = validator({ nodeId: handleNodeId, id: handleId, type: handleType });\n            if (validHandleResult) {\n                const handle = handles.find((h) => h.nodeId === handleNodeId && h.type === handleType && h.id === handleId);\n                return {\n                    handle: {\n                        id: handleId,\n                        type: handleType,\n                        nodeId: handleNodeId,\n                        x: handle?.x || pos.x,\n                        y: handle?.y || pos.y,\n                    },\n                    validHandleResult,\n                };\n            }\n        }\n    }\n    // if we couldn't find a handle below the mouse cursor we look for the closest distance based on the connectionRadius\n    let closestHandles = [];\n    let minDistance = Infinity;\n    handles.forEach((handle) => {\n        const distance = Math.sqrt((handle.x - pos.x) ** 2 + (handle.y - pos.y) ** 2);\n        if (distance <= connectionRadius) {\n            const validHandleResult = validator(handle);\n            if (distance <= minDistance) {\n                if (distance < minDistance) {\n                    closestHandles = [{ handle, validHandleResult }];\n                }\n                else if (distance === minDistance) {\n                    // when multiple handles are on the same distance we collect all of them\n                    closestHandles.push({\n                        handle,\n                        validHandleResult,\n                    });\n                }\n                minDistance = distance;\n            }\n        }\n    });\n    if (!closestHandles.length) {\n        return { handle: null, validHandleResult: defaultResult() };\n    }\n    if (closestHandles.length === 1) {\n        return closestHandles[0];\n    }\n    const hasValidHandle = closestHandles.some(({ validHandleResult }) => validHandleResult.isValid);\n    const hasTargetHandle = closestHandles.some(({ handle }) => handle.type === 'target');\n    // if multiple handles are layouted on top of each other we prefer the one with type = target and the one that is valid\n    return (closestHandles.find(({ handle, validHandleResult }) => hasTargetHandle ? handle.type === 'target' : (hasValidHandle ? validHandleResult.isValid : true)) || closestHandles[0]);\n}\nconst nullConnection = { source: null, target: null, sourceHandle: null, targetHandle: null };\nconst defaultResult = () => ({\n    handleDomNode: null,\n    isValid: false,\n    connection: nullConnection,\n    endHandle: null,\n});\n// checks if  and returns connection in fom of an object { source: 123, target: 312 }\nfunction isValidHandle(handle, connectionMode, fromNodeId, fromHandleId, fromType, isValidConnection, doc) {\n    const isTarget = fromType === 'target';\n    const handleToCheck = doc.querySelector(`.react-flow__handle[data-id=\"${handle?.nodeId}-${handle?.id}-${handle?.type}\"]`);\n    const result = {\n        ...defaultResult(),\n        handleDomNode: handleToCheck,\n    };\n    if (handleToCheck) {\n        const handleType = getHandleType(undefined, handleToCheck);\n        const handleNodeId = handleToCheck.getAttribute('data-nodeid');\n        const handleId = handleToCheck.getAttribute('data-handleid');\n        const connectable = handleToCheck.classList.contains('connectable');\n        const connectableEnd = handleToCheck.classList.contains('connectableend');\n        const connection = {\n            source: isTarget ? handleNodeId : fromNodeId,\n            sourceHandle: isTarget ? handleId : fromHandleId,\n            target: isTarget ? fromNodeId : handleNodeId,\n            targetHandle: isTarget ? fromHandleId : handleId,\n        };\n        result.connection = connection;\n        const isConnectable = connectable && connectableEnd;\n        // in strict mode we don't allow target to target or source to source connections\n        const isValid = isConnectable &&\n            (connectionMode === ConnectionMode.Strict\n                ? (isTarget && handleType === 'source') || (!isTarget && handleType === 'target')\n                : handleNodeId !== fromNodeId || handleId !== fromHandleId);\n        if (isValid) {\n            result.endHandle = {\n                nodeId: handleNodeId,\n                handleId,\n                type: handleType,\n            };\n            result.isValid = isValidConnection(connection);\n        }\n    }\n    return result;\n}\nfunction getHandleLookup({ nodes, nodeId, handleId, handleType }) {\n    return nodes.reduce((res, node) => {\n        if (node[internalsSymbol]) {\n            const { handleBounds } = node[internalsSymbol];\n            let sourceHandles = [];\n            let targetHandles = [];\n            if (handleBounds) {\n                sourceHandles = getHandles(node, handleBounds, 'source', `${nodeId}-${handleId}-${handleType}`);\n                targetHandles = getHandles(node, handleBounds, 'target', `${nodeId}-${handleId}-${handleType}`);\n            }\n            res.push(...sourceHandles, ...targetHandles);\n        }\n        return res;\n    }, []);\n}\nfunction getHandleType(edgeUpdaterType, handleDomNode) {\n    if (edgeUpdaterType) {\n        return edgeUpdaterType;\n    }\n    else if (handleDomNode?.classList.contains('target')) {\n        return 'target';\n    }\n    else if (handleDomNode?.classList.contains('source')) {\n        return 'source';\n    }\n    return null;\n}\nfunction resetRecentHandle(handleDomNode) {\n    handleDomNode?.classList.remove('valid', 'connecting', 'react-flow__handle-valid', 'react-flow__handle-connecting');\n}\nfunction getConnectionStatus(isInsideConnectionRadius, isHandleValid) {\n    let connectionStatus = null;\n    if (isHandleValid) {\n        connectionStatus = 'valid';\n    }\n    else if (isInsideConnectionRadius && !isHandleValid) {\n        connectionStatus = 'invalid';\n    }\n    return connectionStatus;\n}\n\nfunction handlePointerDown({ event, handleId, nodeId, onConnect, isTarget, getState, setState, isValidConnection, edgeUpdaterType, onReconnectEnd, }) {\n    // when react-flow is used inside a shadow root we can't use document\n    const doc = getHostForElement(event.target);\n    const { connectionMode, domNode, autoPanOnConnect, connectionRadius, onConnectStart, panBy, getNodes, cancelConnection, } = getState();\n    let autoPanId = 0;\n    let closestHandle;\n    const { x, y } = getEventPosition(event);\n    const clickedHandle = doc?.elementFromPoint(x, y);\n    const handleType = getHandleType(edgeUpdaterType, clickedHandle);\n    const containerBounds = domNode?.getBoundingClientRect();\n    if (!containerBounds || !handleType) {\n        return;\n    }\n    let prevActiveHandle;\n    let connectionPosition = getEventPosition(event, containerBounds);\n    let autoPanStarted = false;\n    let connection = null;\n    let isValid = false;\n    let handleDomNode = null;\n    const handleLookup = getHandleLookup({\n        nodes: getNodes(),\n        nodeId,\n        handleId,\n        handleType,\n    });\n    // when the user is moving the mouse close to the edge of the canvas while connecting we move the canvas\n    const autoPan = () => {\n        if (!autoPanOnConnect) {\n            return;\n        }\n        const [xMovement, yMovement] = calcAutoPan(connectionPosition, containerBounds);\n        panBy({ x: xMovement, y: yMovement });\n        autoPanId = requestAnimationFrame(autoPan);\n    };\n    setState({\n        connectionPosition,\n        connectionStatus: null,\n        // connectionNodeId etc will be removed in the next major in favor of connectionStartHandle\n        connectionNodeId: nodeId,\n        connectionHandleId: handleId,\n        connectionHandleType: handleType,\n        connectionStartHandle: {\n            nodeId,\n            handleId,\n            type: handleType,\n        },\n        connectionEndHandle: null,\n    });\n    onConnectStart?.(event, { nodeId, handleId, handleType });\n    function onPointerMove(event) {\n        const { transform } = getState();\n        connectionPosition = getEventPosition(event, containerBounds);\n        const { handle, validHandleResult } = getClosestHandle(event, doc, pointToRendererPoint(connectionPosition, transform, false, [1, 1]), connectionRadius, handleLookup, (handle) => isValidHandle(handle, connectionMode, nodeId, handleId, isTarget ? 'target' : 'source', isValidConnection, doc));\n        closestHandle = handle;\n        if (!autoPanStarted) {\n            autoPan();\n            autoPanStarted = true;\n        }\n        handleDomNode = validHandleResult.handleDomNode;\n        connection = validHandleResult.connection;\n        isValid = validHandleResult.isValid;\n        setState({\n            connectionPosition: closestHandle && isValid\n                ? rendererPointToPoint({\n                    x: closestHandle.x,\n                    y: closestHandle.y,\n                }, transform)\n                : connectionPosition,\n            connectionStatus: getConnectionStatus(!!closestHandle, isValid),\n            connectionEndHandle: validHandleResult.endHandle,\n        });\n        if (!closestHandle && !isValid && !handleDomNode) {\n            return resetRecentHandle(prevActiveHandle);\n        }\n        if (connection.source !== connection.target && handleDomNode) {\n            resetRecentHandle(prevActiveHandle);\n            prevActiveHandle = handleDomNode;\n            // @todo: remove the old class names \"react-flow__handle-\" in the next major version\n            handleDomNode.classList.add('connecting', 'react-flow__handle-connecting');\n            handleDomNode.classList.toggle('valid', isValid);\n            handleDomNode.classList.toggle('react-flow__handle-valid', isValid);\n        }\n    }\n    function onPointerUp(event) {\n        if ((closestHandle || handleDomNode) && connection && isValid) {\n            onConnect?.(connection);\n        }\n        // it's important to get a fresh reference from the store here\n        // in order to get the latest state of onConnectEnd\n        getState().onConnectEnd?.(event);\n        if (edgeUpdaterType) {\n            onReconnectEnd?.(event);\n        }\n        resetRecentHandle(prevActiveHandle);\n        cancelConnection();\n        cancelAnimationFrame(autoPanId);\n        autoPanStarted = false;\n        isValid = false;\n        connection = null;\n        handleDomNode = null;\n        doc.removeEventListener('mousemove', onPointerMove);\n        doc.removeEventListener('mouseup', onPointerUp);\n        doc.removeEventListener('touchmove', onPointerMove);\n        doc.removeEventListener('touchend', onPointerUp);\n    }\n    doc.addEventListener('mousemove', onPointerMove);\n    doc.addEventListener('mouseup', onPointerUp);\n    doc.addEventListener('touchmove', onPointerMove);\n    doc.addEventListener('touchend', onPointerUp);\n}\n\nconst alwaysValid = () => true;\nconst selector$f = (s) => ({\n    connectionStartHandle: s.connectionStartHandle,\n    connectOnClick: s.connectOnClick,\n    noPanClassName: s.noPanClassName,\n});\nconst connectingSelector = (nodeId, handleId, type) => (state) => {\n    const { connectionStartHandle: startHandle, connectionEndHandle: endHandle, connectionClickStartHandle: clickHandle, } = state;\n    return {\n        connecting: (startHandle?.nodeId === nodeId && startHandle?.handleId === handleId && startHandle?.type === type) ||\n            (endHandle?.nodeId === nodeId && endHandle?.handleId === handleId && endHandle?.type === type),\n        clickConnecting: clickHandle?.nodeId === nodeId && clickHandle?.handleId === handleId && clickHandle?.type === type,\n    };\n};\nconst Handle = forwardRef(({ type = 'source', position = Position.Top, isValidConnection, isConnectable = true, isConnectableStart = true, isConnectableEnd = true, id, onConnect, children, className, onMouseDown, onTouchStart, ...rest }, ref) => {\n    const handleId = id || null;\n    const isTarget = type === 'target';\n    const store = useStoreApi();\n    const nodeId = useNodeId();\n    const { connectOnClick, noPanClassName } = useStore(selector$f, shallow);\n    const { connecting, clickConnecting } = useStore(connectingSelector(nodeId, handleId, type), shallow);\n    if (!nodeId) {\n        store.getState().onError?.('010', errorMessages['error010']());\n    }\n    const onConnectExtended = (params) => {\n        const { defaultEdgeOptions, onConnect: onConnectAction, hasDefaultEdges } = store.getState();\n        const edgeParams = {\n            ...defaultEdgeOptions,\n            ...params,\n        };\n        if (hasDefaultEdges) {\n            const { edges, setEdges } = store.getState();\n            setEdges(addEdge(edgeParams, edges));\n        }\n        onConnectAction?.(edgeParams);\n        onConnect?.(edgeParams);\n    };\n    const onPointerDown = (event) => {\n        if (!nodeId) {\n            return;\n        }\n        const isMouseTriggered = isMouseEvent(event);\n        if (isConnectableStart && ((isMouseTriggered && event.button === 0) || !isMouseTriggered)) {\n            handlePointerDown({\n                event,\n                handleId,\n                nodeId,\n                onConnect: onConnectExtended,\n                isTarget,\n                getState: store.getState,\n                setState: store.setState,\n                isValidConnection: isValidConnection || store.getState().isValidConnection || alwaysValid,\n            });\n        }\n        if (isMouseTriggered) {\n            onMouseDown?.(event);\n        }\n        else {\n            onTouchStart?.(event);\n        }\n    };\n    const onClick = (event) => {\n        const { onClickConnectStart, onClickConnectEnd, connectionClickStartHandle, connectionMode, isValidConnection: isValidConnectionStore, } = store.getState();\n        if (!nodeId || (!connectionClickStartHandle && !isConnectableStart)) {\n            return;\n        }\n        if (!connectionClickStartHandle) {\n            onClickConnectStart?.(event, { nodeId, handleId, handleType: type });\n            store.setState({ connectionClickStartHandle: { nodeId, type, handleId } });\n            return;\n        }\n        const doc = getHostForElement(event.target);\n        const isValidConnectionHandler = isValidConnection || isValidConnectionStore || alwaysValid;\n        const { connection, isValid } = isValidHandle({\n            nodeId,\n            id: handleId,\n            type,\n        }, connectionMode, connectionClickStartHandle.nodeId, connectionClickStartHandle.handleId || null, connectionClickStartHandle.type, isValidConnectionHandler, doc);\n        if (isValid) {\n            onConnectExtended(connection);\n        }\n        onClickConnectEnd?.(event);\n        store.setState({ connectionClickStartHandle: null });\n    };\n    return (React.createElement(\"div\", { \"data-handleid\": handleId, \"data-nodeid\": nodeId, \"data-handlepos\": position, \"data-id\": `${nodeId}-${handleId}-${type}`, className: cc([\n            'react-flow__handle',\n            `react-flow__handle-${position}`,\n            'nodrag',\n            noPanClassName,\n            className,\n            {\n                source: !isTarget,\n                target: isTarget,\n                connectable: isConnectable,\n                connectablestart: isConnectableStart,\n                connectableend: isConnectableEnd,\n                connecting: clickConnecting,\n                // this class is used to style the handle when the user is connecting\n                connectionindicator: isConnectable && ((isConnectableStart && !connecting) || (isConnectableEnd && connecting)),\n            },\n        ]), onMouseDown: onPointerDown, onTouchStart: onPointerDown, onClick: connectOnClick ? onClick : undefined, ref: ref, ...rest }, children));\n});\nHandle.displayName = 'Handle';\nvar Handle$1 = memo(Handle);\n\nconst DefaultNode = ({ data, isConnectable, targetPosition = Position.Top, sourcePosition = Position.Bottom, }) => {\n    return (React.createElement(React.Fragment, null,\n        React.createElement(Handle$1, { type: \"target\", position: targetPosition, isConnectable: isConnectable }),\n        data?.label,\n        React.createElement(Handle$1, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })));\n};\nDefaultNode.displayName = 'DefaultNode';\nvar DefaultNode$1 = memo(DefaultNode);\n\nconst InputNode = ({ data, isConnectable, sourcePosition = Position.Bottom }) => (React.createElement(React.Fragment, null,\n    data?.label,\n    React.createElement(Handle$1, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })));\nInputNode.displayName = 'InputNode';\nvar InputNode$1 = memo(InputNode);\n\nconst OutputNode = ({ data, isConnectable, targetPosition = Position.Top }) => (React.createElement(React.Fragment, null,\n    React.createElement(Handle$1, { type: \"target\", position: targetPosition, isConnectable: isConnectable }),\n    data?.label));\nOutputNode.displayName = 'OutputNode';\nvar OutputNode$1 = memo(OutputNode);\n\nconst GroupNode = () => null;\nGroupNode.displayName = 'GroupNode';\n\nconst selector$e = (s) => ({\n    selectedNodes: s.getNodes().filter((n) => n.selected),\n    selectedEdges: s.edges.filter((e) => e.selected).map((e) => ({ ...e })),\n});\nconst selectId = (obj) => obj.id;\nfunction areEqual(a, b) {\n    return (shallow(a.selectedNodes.map(selectId), b.selectedNodes.map(selectId)) &&\n        shallow(a.selectedEdges.map(selectId), b.selectedEdges.map(selectId)));\n}\n// This is just a helper component for calling the onSelectionChange listener.\n// @TODO: Now that we have the onNodesChange and on EdgesChange listeners, do we still need this component?\nconst SelectionListener = memo(({ onSelectionChange }) => {\n    const store = useStoreApi();\n    const { selectedNodes, selectedEdges } = useStore(selector$e, areEqual);\n    useEffect(() => {\n        const params = { nodes: selectedNodes, edges: selectedEdges };\n        onSelectionChange?.(params);\n        store.getState().onSelectionChange.forEach((fn) => fn(params));\n    }, [selectedNodes, selectedEdges, onSelectionChange]);\n    return null;\n});\nSelectionListener.displayName = 'SelectionListener';\nconst changeSelector = (s) => !!s.onSelectionChange;\nfunction Wrapper$1({ onSelectionChange }) {\n    const storeHasSelectionChange = useStore(changeSelector);\n    if (onSelectionChange || storeHasSelectionChange) {\n        return React.createElement(SelectionListener, { onSelectionChange: onSelectionChange });\n    }\n    return null;\n}\n\nconst selector$d = (s) => ({\n    setNodes: s.setNodes,\n    setEdges: s.setEdges,\n    setDefaultNodesAndEdges: s.setDefaultNodesAndEdges,\n    setMinZoom: s.setMinZoom,\n    setMaxZoom: s.setMaxZoom,\n    setTranslateExtent: s.setTranslateExtent,\n    setNodeExtent: s.setNodeExtent,\n    reset: s.reset,\n});\nfunction useStoreUpdater(value, setStoreState) {\n    useEffect(() => {\n        if (typeof value !== 'undefined') {\n            setStoreState(value);\n        }\n    }, [value]);\n}\n// updates with values in store that don't have a dedicated setter function\nfunction useDirectStoreUpdater(key, value, setState) {\n    useEffect(() => {\n        if (typeof value !== 'undefined') {\n            setState({ [key]: value });\n        }\n    }, [value]);\n}\nconst StoreUpdater = ({ nodes, edges, defaultNodes, defaultEdges, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, nodesDraggable, nodesConnectable, nodesFocusable, edgesFocusable, edgesUpdatable, elevateNodesOnSelect, minZoom, maxZoom, nodeExtent, onNodesChange, onEdgesChange, elementsSelectable, connectionMode, snapGrid, snapToGrid, translateExtent, connectOnClick, defaultEdgeOptions, fitView, fitViewOptions, onNodesDelete, onEdgesDelete, onNodeDrag, onNodeDragStart, onNodeDragStop, onSelectionDrag, onSelectionDragStart, onSelectionDragStop, noPanClassName, nodeOrigin, rfId, autoPanOnConnect, autoPanOnNodeDrag, onError, connectionRadius, isValidConnection, nodeDragThreshold, }) => {\n    const { setNodes, setEdges, setDefaultNodesAndEdges, setMinZoom, setMaxZoom, setTranslateExtent, setNodeExtent, reset, } = useStore(selector$d, shallow);\n    const store = useStoreApi();\n    useEffect(() => {\n        const edgesWithDefaults = defaultEdges?.map((e) => ({ ...e, ...defaultEdgeOptions }));\n        setDefaultNodesAndEdges(defaultNodes, edgesWithDefaults);\n        return () => {\n            reset();\n        };\n    }, []);\n    useDirectStoreUpdater('defaultEdgeOptions', defaultEdgeOptions, store.setState);\n    useDirectStoreUpdater('connectionMode', connectionMode, store.setState);\n    useDirectStoreUpdater('onConnect', onConnect, store.setState);\n    useDirectStoreUpdater('onConnectStart', onConnectStart, store.setState);\n    useDirectStoreUpdater('onConnectEnd', onConnectEnd, store.setState);\n    useDirectStoreUpdater('onClickConnectStart', onClickConnectStart, store.setState);\n    useDirectStoreUpdater('onClickConnectEnd', onClickConnectEnd, store.setState);\n    useDirectStoreUpdater('nodesDraggable', nodesDraggable, store.setState);\n    useDirectStoreUpdater('nodesConnectable', nodesConnectable, store.setState);\n    useDirectStoreUpdater('nodesFocusable', nodesFocusable, store.setState);\n    useDirectStoreUpdater('edgesFocusable', edgesFocusable, store.setState);\n    useDirectStoreUpdater('edgesUpdatable', edgesUpdatable, store.setState);\n    useDirectStoreUpdater('elementsSelectable', elementsSelectable, store.setState);\n    useDirectStoreUpdater('elevateNodesOnSelect', elevateNodesOnSelect, store.setState);\n    useDirectStoreUpdater('snapToGrid', snapToGrid, store.setState);\n    useDirectStoreUpdater('snapGrid', snapGrid, store.setState);\n    useDirectStoreUpdater('onNodesChange', onNodesChange, store.setState);\n    useDirectStoreUpdater('onEdgesChange', onEdgesChange, store.setState);\n    useDirectStoreUpdater('connectOnClick', connectOnClick, store.setState);\n    useDirectStoreUpdater('fitViewOnInit', fitView, store.setState);\n    useDirectStoreUpdater('fitViewOnInitOptions', fitViewOptions, store.setState);\n    useDirectStoreUpdater('onNodesDelete', onNodesDelete, store.setState);\n    useDirectStoreUpdater('onEdgesDelete', onEdgesDelete, store.setState);\n    useDirectStoreUpdater('onNodeDrag', onNodeDrag, store.setState);\n    useDirectStoreUpdater('onNodeDragStart', onNodeDragStart, store.setState);\n    useDirectStoreUpdater('onNodeDragStop', onNodeDragStop, store.setState);\n    useDirectStoreUpdater('onSelectionDrag', onSelectionDrag, store.setState);\n    useDirectStoreUpdater('onSelectionDragStart', onSelectionDragStart, store.setState);\n    useDirectStoreUpdater('onSelectionDragStop', onSelectionDragStop, store.setState);\n    useDirectStoreUpdater('noPanClassName', noPanClassName, store.setState);\n    useDirectStoreUpdater('nodeOrigin', nodeOrigin, store.setState);\n    useDirectStoreUpdater('rfId', rfId, store.setState);\n    useDirectStoreUpdater('autoPanOnConnect', autoPanOnConnect, store.setState);\n    useDirectStoreUpdater('autoPanOnNodeDrag', autoPanOnNodeDrag, store.setState);\n    useDirectStoreUpdater('onError', onError, store.setState);\n    useDirectStoreUpdater('connectionRadius', connectionRadius, store.setState);\n    useDirectStoreUpdater('isValidConnection', isValidConnection, store.setState);\n    useDirectStoreUpdater('nodeDragThreshold', nodeDragThreshold, store.setState);\n    useStoreUpdater(nodes, setNodes);\n    useStoreUpdater(edges, setEdges);\n    useStoreUpdater(minZoom, setMinZoom);\n    useStoreUpdater(maxZoom, setMaxZoom);\n    useStoreUpdater(translateExtent, setTranslateExtent);\n    useStoreUpdater(nodeExtent, setNodeExtent);\n    return null;\n};\n\nconst style = { display: 'none' };\nconst ariaLiveStyle = {\n    position: 'absolute',\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0px, 0px, 0px, 0px)',\n    clipPath: 'inset(100%)',\n};\nconst ARIA_NODE_DESC_KEY = 'react-flow__node-desc';\nconst ARIA_EDGE_DESC_KEY = 'react-flow__edge-desc';\nconst ARIA_LIVE_MESSAGE = 'react-flow__aria-live';\nconst selector$c = (s) => s.ariaLiveMessage;\nfunction AriaLiveMessage({ rfId }) {\n    const ariaLiveMessage = useStore(selector$c);\n    return (React.createElement(\"div\", { id: `${ARIA_LIVE_MESSAGE}-${rfId}`, \"aria-live\": \"assertive\", \"aria-atomic\": \"true\", style: ariaLiveStyle }, ariaLiveMessage));\n}\nfunction A11yDescriptions({ rfId, disableKeyboardA11y }) {\n    return (React.createElement(React.Fragment, null,\n        React.createElement(\"div\", { id: `${ARIA_NODE_DESC_KEY}-${rfId}`, style: style },\n            \"Press enter or space to select a node.\",\n            !disableKeyboardA11y && 'You can then use the arrow keys to move the node around.',\n            \" Press delete to remove it and escape to cancel.\",\n            ' '),\n        React.createElement(\"div\", { id: `${ARIA_EDGE_DESC_KEY}-${rfId}`, style: style }, \"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.\"),\n        !disableKeyboardA11y && React.createElement(AriaLiveMessage, { rfId: rfId })));\n}\n\n// the keycode can be a string 'a' or an array of strings ['a', 'a+d']\n// a string means a single key 'a' or a combination when '+' is used 'a+d'\n// an array means different possibilities. Explainer: ['a', 'd+s'] here the\n// user can use the single key 'a' or the combination 'd' + 's'\nvar useKeyPress = (keyCode = null, options = { actInsideInputWithModifier: true }) => {\n    const [keyPressed, setKeyPressed] = useState(false);\n    // we need to remember if a modifier key is pressed in order to track it\n    const modifierPressed = useRef(false);\n    // we need to remember the pressed keys in order to support combinations\n    const pressedKeys = useRef(new Set([]));\n    // keyCodes = array with single keys [['a']] or key combinations [['a', 's']]\n    // keysToWatch = array with all keys flattened ['a', 'd', 'ShiftLeft']\n    // used to check if we store event.code or event.key. When the code is in the list of keysToWatch\n    // we use the code otherwise the key. Explainer: When you press the left \"command\" key, the code is \"MetaLeft\"\n    // and the key is \"Meta\". We want users to be able to pass keys and codes so we assume that the key is meant when\n    // we can't find it in the list of keysToWatch.\n    const [keyCodes, keysToWatch] = useMemo(() => {\n        if (keyCode !== null) {\n            const keyCodeArr = Array.isArray(keyCode) ? keyCode : [keyCode];\n            const keys = keyCodeArr.filter((kc) => typeof kc === 'string').map((kc) => kc.split('+'));\n            const keysFlat = keys.reduce((res, item) => res.concat(...item), []);\n            return [keys, keysFlat];\n        }\n        return [[], []];\n    }, [keyCode]);\n    useEffect(() => {\n        const doc = typeof document !== 'undefined' ? document : null;\n        const target = options?.target || doc;\n        if (keyCode !== null) {\n            const downHandler = (event) => {\n                modifierPressed.current = event.ctrlKey || event.metaKey || event.shiftKey;\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !options.actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                pressedKeys.current.add(event[keyOrCode]);\n                if (isMatchingKey(keyCodes, pressedKeys.current, false)) {\n                    event.preventDefault();\n                    setKeyPressed(true);\n                }\n            };\n            const upHandler = (event) => {\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !options.actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                if (isMatchingKey(keyCodes, pressedKeys.current, true)) {\n                    setKeyPressed(false);\n                    pressedKeys.current.clear();\n                }\n                else {\n                    pressedKeys.current.delete(event[keyOrCode]);\n                }\n                // fix for Mac: when cmd key is pressed, keyup is not triggered for any other key, see: https://stackoverflow.com/questions/27380018/when-cmd-key-is-kept-pressed-keyup-is-not-triggered-for-any-other-key\n                if (event.key === 'Meta') {\n                    pressedKeys.current.clear();\n                }\n                modifierPressed.current = false;\n            };\n            const resetHandler = () => {\n                pressedKeys.current.clear();\n                setKeyPressed(false);\n            };\n            target?.addEventListener('keydown', downHandler);\n            target?.addEventListener('keyup', upHandler);\n            window.addEventListener('blur', resetHandler);\n            return () => {\n                target?.removeEventListener('keydown', downHandler);\n                target?.removeEventListener('keyup', upHandler);\n                window.removeEventListener('blur', resetHandler);\n            };\n        }\n    }, [keyCode, setKeyPressed]);\n    return keyPressed;\n};\n// utils\nfunction isMatchingKey(keyCodes, pressedKeys, isUp) {\n    return (keyCodes\n        // we only want to compare same sizes of keyCode definitions\n        // and pressed keys. When the user specified 'Meta' as a key somewhere\n        // this would also be truthy without this filter when user presses 'Meta' + 'r'\n        .filter((keys) => isUp || keys.length === pressedKeys.size)\n        // since we want to support multiple possibilities only one of the\n        // combinations need to be part of the pressed keys\n        .some((keys) => keys.every((k) => pressedKeys.has(k))));\n}\nfunction useKeyOrCode(eventCode, keysToWatch) {\n    return keysToWatch.includes(eventCode) ? 'code' : 'key';\n}\n\nfunction calculateXYZPosition(node, nodeInternals, result, nodeOrigin) {\n    const parentId = node.parentNode || node.parentId;\n    if (!parentId) {\n        return result;\n    }\n    const parentNode = nodeInternals.get(parentId);\n    const parentNodePosition = getNodePositionWithOrigin(parentNode, nodeOrigin);\n    return calculateXYZPosition(parentNode, nodeInternals, {\n        x: (result.x ?? 0) + parentNodePosition.x,\n        y: (result.y ?? 0) + parentNodePosition.y,\n        z: (parentNode[internalsSymbol]?.z ?? 0) > (result.z ?? 0) ? parentNode[internalsSymbol]?.z ?? 0 : result.z ?? 0,\n    }, nodeOrigin);\n}\nfunction updateAbsoluteNodePositions(nodeInternals, nodeOrigin, parentNodes) {\n    nodeInternals.forEach((node) => {\n        const parentId = node.parentNode || node.parentId;\n        if (parentId && !nodeInternals.has(parentId)) {\n            throw new Error(`Parent node ${parentId} not found`);\n        }\n        if (parentId || parentNodes?.[node.id]) {\n            const { x, y, z } = calculateXYZPosition(node, nodeInternals, {\n                ...node.position,\n                z: node[internalsSymbol]?.z ?? 0,\n            }, nodeOrigin);\n            node.positionAbsolute = {\n                x,\n                y,\n            };\n            node[internalsSymbol].z = z;\n            if (parentNodes?.[node.id]) {\n                node[internalsSymbol].isParent = true;\n            }\n        }\n    });\n}\nfunction createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect) {\n    const nextNodeInternals = new Map();\n    const parentNodes = {};\n    const selectedNodeZ = elevateNodesOnSelect ? 1000 : 0;\n    nodes.forEach((node) => {\n        const z = (isNumeric(node.zIndex) ? node.zIndex : 0) + (node.selected ? selectedNodeZ : 0);\n        const currInternals = nodeInternals.get(node.id);\n        const internals = {\n            ...node,\n            positionAbsolute: {\n                x: node.position.x,\n                y: node.position.y,\n            },\n        };\n        const parentId = node.parentNode || node.parentId;\n        if (parentId) {\n            parentNodes[parentId] = true;\n        }\n        const resetHandleBounds = currInternals?.type && currInternals?.type !== node.type;\n        Object.defineProperty(internals, internalsSymbol, {\n            enumerable: false,\n            value: {\n                handleBounds: resetHandleBounds ? undefined : currInternals?.[internalsSymbol]?.handleBounds,\n                z,\n            },\n        });\n        nextNodeInternals.set(node.id, internals);\n    });\n    updateAbsoluteNodePositions(nextNodeInternals, nodeOrigin, parentNodes);\n    return nextNodeInternals;\n}\nfunction fitView(get, options = {}) {\n    const { getNodes, width, height, minZoom, maxZoom, d3Zoom, d3Selection, fitViewOnInitDone, fitViewOnInit, nodeOrigin, } = get();\n    const isInitialFitView = options.initial && !fitViewOnInitDone && fitViewOnInit;\n    const d3initialized = d3Zoom && d3Selection;\n    if (d3initialized && (isInitialFitView || !options.initial)) {\n        const nodes = getNodes().filter((n) => {\n            const isVisible = options.includeHiddenNodes ? n.width && n.height : !n.hidden;\n            if (options.nodes?.length) {\n                return isVisible && options.nodes.some((optionNode) => optionNode.id === n.id);\n            }\n            return isVisible;\n        });\n        const nodesInitialized = nodes.every((n) => n.width && n.height);\n        if (nodes.length > 0 && nodesInitialized) {\n            const bounds = getNodesBounds(nodes, nodeOrigin);\n            const { x, y, zoom } = getViewportForBounds(bounds, width, height, options.minZoom ?? minZoom, options.maxZoom ?? maxZoom, options.padding ?? 0.1);\n            const nextTransform = zoomIdentity.translate(x, y).scale(zoom);\n            if (typeof options.duration === 'number' && options.duration > 0) {\n                d3Zoom.transform(getD3Transition(d3Selection, options.duration), nextTransform);\n            }\n            else {\n                d3Zoom.transform(d3Selection, nextTransform);\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction handleControlledNodeSelectionChange(nodeChanges, nodeInternals) {\n    nodeChanges.forEach((change) => {\n        const node = nodeInternals.get(change.id);\n        if (node) {\n            nodeInternals.set(node.id, {\n                ...node,\n                [internalsSymbol]: node[internalsSymbol],\n                selected: change.selected,\n            });\n        }\n    });\n    return new Map(nodeInternals);\n}\nfunction handleControlledEdgeSelectionChange(edgeChanges, edges) {\n    return edges.map((e) => {\n        const change = edgeChanges.find((change) => change.id === e.id);\n        if (change) {\n            e.selected = change.selected;\n        }\n        return e;\n    });\n}\nfunction updateNodesAndEdgesSelections({ changedNodes, changedEdges, get, set }) {\n    const { nodeInternals, edges, onNodesChange, onEdgesChange, hasDefaultNodes, hasDefaultEdges } = get();\n    if (changedNodes?.length) {\n        if (hasDefaultNodes) {\n            set({ nodeInternals: handleControlledNodeSelectionChange(changedNodes, nodeInternals) });\n        }\n        onNodesChange?.(changedNodes);\n    }\n    if (changedEdges?.length) {\n        if (hasDefaultEdges) {\n            set({ edges: handleControlledEdgeSelectionChange(changedEdges, edges) });\n        }\n        onEdgesChange?.(changedEdges);\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => { };\nconst initialViewportHelper = {\n    zoomIn: noop,\n    zoomOut: noop,\n    zoomTo: noop,\n    getZoom: () => 1,\n    setViewport: noop,\n    getViewport: () => ({ x: 0, y: 0, zoom: 1 }),\n    fitView: () => false,\n    setCenter: noop,\n    fitBounds: noop,\n    project: (position) => position,\n    screenToFlowPosition: (position) => position,\n    flowToScreenPosition: (position) => position,\n    viewportInitialized: false,\n};\nconst selector$b = (s) => ({\n    d3Zoom: s.d3Zoom,\n    d3Selection: s.d3Selection,\n});\nconst useViewportHelper = () => {\n    const store = useStoreApi();\n    const { d3Zoom, d3Selection } = useStore(selector$b, shallow);\n    const viewportHelperFunctions = useMemo(() => {\n        if (d3Selection && d3Zoom) {\n            return {\n                zoomIn: (options) => d3Zoom.scaleBy(getD3Transition(d3Selection, options?.duration), 1.2),\n                zoomOut: (options) => d3Zoom.scaleBy(getD3Transition(d3Selection, options?.duration), 1 / 1.2),\n                zoomTo: (zoomLevel, options) => d3Zoom.scaleTo(getD3Transition(d3Selection, options?.duration), zoomLevel),\n                getZoom: () => store.getState().transform[2],\n                setViewport: (transform, options) => {\n                    const [x, y, zoom] = store.getState().transform;\n                    const nextTransform = zoomIdentity\n                        .translate(transform.x ?? x, transform.y ?? y)\n                        .scale(transform.zoom ?? zoom);\n                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), nextTransform);\n                },\n                getViewport: () => {\n                    const [x, y, zoom] = store.getState().transform;\n                    return { x, y, zoom };\n                },\n                fitView: (options) => fitView(store.getState, options),\n                setCenter: (x, y, options) => {\n                    const { width, height, maxZoom } = store.getState();\n                    const nextZoom = typeof options?.zoom !== 'undefined' ? options.zoom : maxZoom;\n                    const centerX = width / 2 - x * nextZoom;\n                    const centerY = height / 2 - y * nextZoom;\n                    const transform = zoomIdentity.translate(centerX, centerY).scale(nextZoom);\n                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), transform);\n                },\n                fitBounds: (bounds, options) => {\n                    const { width, height, minZoom, maxZoom } = store.getState();\n                    const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, options?.padding ?? 0.1);\n                    const transform = zoomIdentity.translate(x, y).scale(zoom);\n                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), transform);\n                },\n                // @deprecated Use `screenToFlowPosition`.\n                project: (position) => {\n                    const { transform, snapToGrid, snapGrid } = store.getState();\n                    console.warn('[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position');\n                    return pointToRendererPoint(position, transform, snapToGrid, snapGrid);\n                },\n                screenToFlowPosition: (position) => {\n                    const { transform, snapToGrid, snapGrid, domNode } = store.getState();\n                    if (!domNode) {\n                        return position;\n                    }\n                    const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                    const relativePosition = {\n                        x: position.x - domX,\n                        y: position.y - domY,\n                    };\n                    return pointToRendererPoint(relativePosition, transform, snapToGrid, snapGrid);\n                },\n                flowToScreenPosition: (position) => {\n                    const { transform, domNode } = store.getState();\n                    if (!domNode) {\n                        return position;\n                    }\n                    const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                    const rendererPosition = rendererPointToPoint(position, transform);\n                    return {\n                        x: rendererPosition.x + domX,\n                        y: rendererPosition.y + domY,\n                    };\n                },\n                viewportInitialized: true,\n            };\n        }\n        return initialViewportHelper;\n    }, [d3Zoom, d3Selection]);\n    return viewportHelperFunctions;\n};\n\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nfunction useReactFlow() {\n    const viewportHelper = useViewportHelper();\n    const store = useStoreApi();\n    const getNodes = useCallback(() => {\n        return store\n            .getState()\n            .getNodes()\n            .map((n) => ({ ...n }));\n    }, []);\n    const getNode = useCallback((id) => {\n        return store.getState().nodeInternals.get(id);\n    }, []);\n    const getEdges = useCallback(() => {\n        const { edges = [] } = store.getState();\n        return edges.map((e) => ({ ...e }));\n    }, []);\n    const getEdge = useCallback((id) => {\n        const { edges = [] } = store.getState();\n        return edges.find((e) => e.id === id);\n    }, []);\n    const setNodes = useCallback((payload) => {\n        const { getNodes, setNodes, hasDefaultNodes, onNodesChange } = store.getState();\n        const nodes = getNodes();\n        const nextNodes = typeof payload === 'function' ? payload(nodes) : payload;\n        if (hasDefaultNodes) {\n            setNodes(nextNodes);\n        }\n        else if (onNodesChange) {\n            const changes = nextNodes.length === 0\n                ? nodes.map((node) => ({ type: 'remove', id: node.id }))\n                : nextNodes.map((node) => ({ item: node, type: 'reset' }));\n            onNodesChange(changes);\n        }\n    }, []);\n    const setEdges = useCallback((payload) => {\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange } = store.getState();\n        const nextEdges = typeof payload === 'function' ? payload(edges) : payload;\n        if (hasDefaultEdges) {\n            setEdges(nextEdges);\n        }\n        else if (onEdgesChange) {\n            const changes = nextEdges.length === 0\n                ? edges.map((edge) => ({ type: 'remove', id: edge.id }))\n                : nextEdges.map((edge) => ({ item: edge, type: 'reset' }));\n            onEdgesChange(changes);\n        }\n    }, []);\n    const addNodes = useCallback((payload) => {\n        const nodes = Array.isArray(payload) ? payload : [payload];\n        const { getNodes, setNodes, hasDefaultNodes, onNodesChange } = store.getState();\n        if (hasDefaultNodes) {\n            const currentNodes = getNodes();\n            const nextNodes = [...currentNodes, ...nodes];\n            setNodes(nextNodes);\n        }\n        else if (onNodesChange) {\n            const changes = nodes.map((node) => ({ item: node, type: 'add' }));\n            onNodesChange(changes);\n        }\n    }, []);\n    const addEdges = useCallback((payload) => {\n        const nextEdges = Array.isArray(payload) ? payload : [payload];\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange } = store.getState();\n        if (hasDefaultEdges) {\n            setEdges([...edges, ...nextEdges]);\n        }\n        else if (onEdgesChange) {\n            const changes = nextEdges.map((edge) => ({ item: edge, type: 'add' }));\n            onEdgesChange(changes);\n        }\n    }, []);\n    const toObject = useCallback(() => {\n        const { getNodes, edges = [], transform } = store.getState();\n        const [x, y, zoom] = transform;\n        return {\n            nodes: getNodes().map((n) => ({ ...n })),\n            edges: edges.map((e) => ({ ...e })),\n            viewport: {\n                x,\n                y,\n                zoom,\n            },\n        };\n    }, []);\n    const deleteElements = useCallback(({ nodes: nodesDeleted, edges: edgesDeleted }) => {\n        const { nodeInternals, getNodes, edges, hasDefaultNodes, hasDefaultEdges, onNodesDelete, onEdgesDelete, onNodesChange, onEdgesChange, } = store.getState();\n        const nodeIds = (nodesDeleted || []).map((node) => node.id);\n        const edgeIds = (edgesDeleted || []).map((edge) => edge.id);\n        const nodesToRemove = getNodes().reduce((res, node) => {\n            const parentId = node.parentNode || node.parentId;\n            const parentHit = !nodeIds.includes(node.id) && parentId && res.find((n) => n.id === parentId);\n            const deletable = typeof node.deletable === 'boolean' ? node.deletable : true;\n            if (deletable && (nodeIds.includes(node.id) || parentHit)) {\n                res.push(node);\n            }\n            return res;\n        }, []);\n        const deletableEdges = edges.filter((e) => (typeof e.deletable === 'boolean' ? e.deletable : true));\n        const initialHitEdges = deletableEdges.filter((e) => edgeIds.includes(e.id));\n        if (nodesToRemove || initialHitEdges) {\n            const connectedEdges = getConnectedEdges(nodesToRemove, deletableEdges);\n            const edgesToRemove = [...initialHitEdges, ...connectedEdges];\n            const edgeIdsToRemove = edgesToRemove.reduce((res, edge) => {\n                if (!res.includes(edge.id)) {\n                    res.push(edge.id);\n                }\n                return res;\n            }, []);\n            if (hasDefaultEdges || hasDefaultNodes) {\n                if (hasDefaultEdges) {\n                    store.setState({\n                        edges: edges.filter((e) => !edgeIdsToRemove.includes(e.id)),\n                    });\n                }\n                if (hasDefaultNodes) {\n                    nodesToRemove.forEach((node) => {\n                        nodeInternals.delete(node.id);\n                    });\n                    store.setState({\n                        nodeInternals: new Map(nodeInternals),\n                    });\n                }\n            }\n            if (edgeIdsToRemove.length > 0) {\n                onEdgesDelete?.(edgesToRemove);\n                if (onEdgesChange) {\n                    onEdgesChange(edgeIdsToRemove.map((id) => ({\n                        id,\n                        type: 'remove',\n                    })));\n                }\n            }\n            if (nodesToRemove.length > 0) {\n                onNodesDelete?.(nodesToRemove);\n                if (onNodesChange) {\n                    const nodeChanges = nodesToRemove.map((n) => ({ id: n.id, type: 'remove' }));\n                    onNodesChange(nodeChanges);\n                }\n            }\n        }\n    }, []);\n    const getNodeRect = useCallback((nodeOrRect) => {\n        const isRect = isRectObject(nodeOrRect);\n        const node = isRect ? null : store.getState().nodeInternals.get(nodeOrRect.id);\n        if (!isRect && !node) {\n            return [null, null, isRect];\n        }\n        const nodeRect = isRect ? nodeOrRect : nodeToRect(node);\n        return [nodeRect, node, isRect];\n    }, []);\n    const getIntersectingNodes = useCallback((nodeOrRect, partially = true, nodes) => {\n        const [nodeRect, node, isRect] = getNodeRect(nodeOrRect);\n        if (!nodeRect) {\n            return [];\n        }\n        return (nodes || store.getState().getNodes()).filter((n) => {\n            if (!isRect && (n.id === node.id || !n.positionAbsolute)) {\n                return false;\n            }\n            const currNodeRect = nodeToRect(n);\n            const overlappingArea = getOverlappingArea(currNodeRect, nodeRect);\n            const partiallyVisible = partially && overlappingArea > 0;\n            return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n        });\n    }, []);\n    const isNodeIntersecting = useCallback((nodeOrRect, area, partially = true) => {\n        const [nodeRect] = getNodeRect(nodeOrRect);\n        if (!nodeRect) {\n            return false;\n        }\n        const overlappingArea = getOverlappingArea(nodeRect, area);\n        const partiallyVisible = partially && overlappingArea > 0;\n        return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n    }, []);\n    return useMemo(() => {\n        return {\n            ...viewportHelper,\n            getNodes,\n            getNode,\n            getEdges,\n            getEdge,\n            setNodes,\n            setEdges,\n            addNodes,\n            addEdges,\n            toObject,\n            deleteElements,\n            getIntersectingNodes,\n            isNodeIntersecting,\n        };\n    }, [\n        viewportHelper,\n        getNodes,\n        getNode,\n        getEdges,\n        getEdge,\n        setNodes,\n        setEdges,\n        addNodes,\n        addEdges,\n        toObject,\n        deleteElements,\n        getIntersectingNodes,\n        isNodeIntersecting,\n    ]);\n}\n\nconst deleteKeyOptions = { actInsideInputWithModifier: false };\nvar useGlobalKeyHandler = ({ deleteKeyCode, multiSelectionKeyCode }) => {\n    const store = useStoreApi();\n    const { deleteElements } = useReactFlow();\n    const deleteKeyPressed = useKeyPress(deleteKeyCode, deleteKeyOptions);\n    const multiSelectionKeyPressed = useKeyPress(multiSelectionKeyCode);\n    useEffect(() => {\n        if (deleteKeyPressed) {\n            const { edges, getNodes } = store.getState();\n            const selectedNodes = getNodes().filter((node) => node.selected);\n            const selectedEdges = edges.filter((edge) => edge.selected);\n            deleteElements({ nodes: selectedNodes, edges: selectedEdges });\n            store.setState({ nodesSelectionActive: false });\n        }\n    }, [deleteKeyPressed]);\n    useEffect(() => {\n        store.setState({ multiSelectionActive: multiSelectionKeyPressed });\n    }, [multiSelectionKeyPressed]);\n};\n\nfunction useResizeHandler(rendererNode) {\n    const store = useStoreApi();\n    useEffect(() => {\n        let resizeObserver;\n        const updateDimensions = () => {\n            if (!rendererNode.current) {\n                return;\n            }\n            const size = getDimensions(rendererNode.current);\n            if (size.height === 0 || size.width === 0) {\n                store.getState().onError?.('004', errorMessages['error004']());\n            }\n            store.setState({ width: size.width || 500, height: size.height || 500 });\n        };\n        updateDimensions();\n        window.addEventListener('resize', updateDimensions);\n        if (rendererNode.current) {\n            resizeObserver = new ResizeObserver(() => updateDimensions());\n            resizeObserver.observe(rendererNode.current);\n        }\n        return () => {\n            window.removeEventListener('resize', updateDimensions);\n            if (resizeObserver && rendererNode.current) {\n                resizeObserver.unobserve(rendererNode.current);\n            }\n        };\n    }, []);\n}\n\nconst containerStyle = {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    top: 0,\n    left: 0,\n};\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nconst viewChanged = (prevViewport, eventTransform) => prevViewport.x !== eventTransform.x || prevViewport.y !== eventTransform.y || prevViewport.zoom !== eventTransform.k;\nconst eventToFlowTransform = (eventTransform) => ({\n    x: eventTransform.x,\n    y: eventTransform.y,\n    zoom: eventTransform.k,\n});\nconst isWrappedWithClass = (event, className) => event.target.closest(`.${className}`);\nconst isRightClickPan = (panOnDrag, usedButton) => usedButton === 2 && Array.isArray(panOnDrag) && panOnDrag.includes(2);\nconst wheelDelta = (event) => {\n    const factor = event.ctrlKey && isMacOs() ? 10 : 1;\n    return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * factor;\n};\nconst selector$a = (s) => ({\n    d3Zoom: s.d3Zoom,\n    d3Selection: s.d3Selection,\n    d3ZoomHandler: s.d3ZoomHandler,\n    userSelectionActive: s.userSelectionActive,\n});\nconst ZoomPane = ({ onMove, onMoveStart, onMoveEnd, onPaneContextMenu, zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, elementsSelectable, panOnDrag = true, defaultViewport, translateExtent, minZoom, maxZoom, zoomActivationKeyCode, preventScrolling = true, children, noWheelClassName, noPanClassName, }) => {\n    const timerId = useRef();\n    const store = useStoreApi();\n    const isZoomingOrPanning = useRef(false);\n    const zoomedWithRightMouseButton = useRef(false);\n    const zoomPane = useRef(null);\n    const prevTransform = useRef({ x: 0, y: 0, zoom: 0 });\n    const { d3Zoom, d3Selection, d3ZoomHandler, userSelectionActive } = useStore(selector$a, shallow);\n    const zoomActivationKeyPressed = useKeyPress(zoomActivationKeyCode);\n    const mouseButton = useRef(0);\n    const isPanScrolling = useRef(false);\n    const panScrollTimeout = useRef();\n    useResizeHandler(zoomPane);\n    useEffect(() => {\n        if (zoomPane.current) {\n            const bbox = zoomPane.current.getBoundingClientRect();\n            const d3ZoomInstance = zoom().scaleExtent([minZoom, maxZoom]).translateExtent(translateExtent);\n            const selection = select(zoomPane.current).call(d3ZoomInstance);\n            const updatedTransform = zoomIdentity\n                .translate(defaultViewport.x, defaultViewport.y)\n                .scale(clamp(defaultViewport.zoom, minZoom, maxZoom));\n            const extent = [\n                [0, 0],\n                [bbox.width, bbox.height],\n            ];\n            const constrainedTransform = d3ZoomInstance.constrain()(updatedTransform, extent, translateExtent);\n            d3ZoomInstance.transform(selection, constrainedTransform);\n            d3ZoomInstance.wheelDelta(wheelDelta);\n            store.setState({\n                d3Zoom: d3ZoomInstance,\n                d3Selection: selection,\n                d3ZoomHandler: selection.on('wheel.zoom'),\n                // we need to pass transform because zoom handler is not registered when we set the initial transform\n                transform: [constrainedTransform.x, constrainedTransform.y, constrainedTransform.k],\n                domNode: zoomPane.current.closest('.react-flow'),\n            });\n        }\n    }, []);\n    useEffect(() => {\n        if (d3Selection && d3Zoom) {\n            if (panOnScroll && !zoomActivationKeyPressed && !userSelectionActive) {\n                d3Selection.on('wheel.zoom', (event) => {\n                    if (isWrappedWithClass(event, noWheelClassName)) {\n                        return false;\n                    }\n                    event.preventDefault();\n                    event.stopImmediatePropagation();\n                    const currentZoom = d3Selection.property('__zoom').k || 1;\n                    // macos and win set ctrlKey=true for pinch gesture on a trackpad\n                    if (event.ctrlKey && zoomOnPinch) {\n                        const point = pointer(event);\n                        const pinchDelta = wheelDelta(event);\n                        const zoom = currentZoom * Math.pow(2, pinchDelta);\n                        // @ts-ignore\n                        d3Zoom.scaleTo(d3Selection, zoom, point, event);\n                        return;\n                    }\n                    // increase scroll speed in firefox\n                    // firefox: deltaMode === 1; chrome: deltaMode === 0\n                    const deltaNormalize = event.deltaMode === 1 ? 20 : 1;\n                    let deltaX = panOnScrollMode === PanOnScrollMode.Vertical ? 0 : event.deltaX * deltaNormalize;\n                    let deltaY = panOnScrollMode === PanOnScrollMode.Horizontal ? 0 : event.deltaY * deltaNormalize;\n                    // this enables vertical scrolling with shift + scroll on windows\n                    if (!isMacOs() && event.shiftKey && panOnScrollMode !== PanOnScrollMode.Vertical) {\n                        deltaX = event.deltaY * deltaNormalize;\n                        deltaY = 0;\n                    }\n                    d3Zoom.translateBy(d3Selection, -(deltaX / currentZoom) * panOnScrollSpeed, -(deltaY / currentZoom) * panOnScrollSpeed, \n                    // @ts-ignore\n                    { internal: true });\n                    const nextViewport = eventToFlowTransform(d3Selection.property('__zoom'));\n                    const { onViewportChangeStart, onViewportChange, onViewportChangeEnd } = store.getState();\n                    clearTimeout(panScrollTimeout.current);\n                    // for pan on scroll we need to handle the event calls on our own\n                    // we can't use the start, zoom and end events from d3-zoom\n                    // because start and move gets called on every scroll event and not once at the beginning\n                    if (!isPanScrolling.current) {\n                        isPanScrolling.current = true;\n                        onMoveStart?.(event, nextViewport);\n                        onViewportChangeStart?.(nextViewport);\n                    }\n                    if (isPanScrolling.current) {\n                        onMove?.(event, nextViewport);\n                        onViewportChange?.(nextViewport);\n                        panScrollTimeout.current = setTimeout(() => {\n                            onMoveEnd?.(event, nextViewport);\n                            onViewportChangeEnd?.(nextViewport);\n                            isPanScrolling.current = false;\n                        }, 150);\n                    }\n                }, { passive: false });\n            }\n            else if (typeof d3ZoomHandler !== 'undefined') {\n                d3Selection.on('wheel.zoom', function (event, d) {\n                    // we still want to enable pinch zooming even if preventScrolling is set to false\n                    const invalidEvent = !preventScrolling && event.type === 'wheel' && !event.ctrlKey;\n                    if (invalidEvent || isWrappedWithClass(event, noWheelClassName)) {\n                        return null;\n                    }\n                    event.preventDefault();\n                    d3ZoomHandler.call(this, event, d);\n                }, { passive: false });\n            }\n        }\n    }, [\n        userSelectionActive,\n        panOnScroll,\n        panOnScrollMode,\n        d3Selection,\n        d3Zoom,\n        d3ZoomHandler,\n        zoomActivationKeyPressed,\n        zoomOnPinch,\n        preventScrolling,\n        noWheelClassName,\n        onMoveStart,\n        onMove,\n        onMoveEnd,\n    ]);\n    useEffect(() => {\n        if (d3Zoom) {\n            d3Zoom.on('start', (event) => {\n                if (!event.sourceEvent || event.sourceEvent.internal) {\n                    return null;\n                }\n                // we need to remember it here, because it's always 0 in the \"zoom\" event\n                mouseButton.current = event.sourceEvent?.button;\n                const { onViewportChangeStart } = store.getState();\n                const flowTransform = eventToFlowTransform(event.transform);\n                isZoomingOrPanning.current = true;\n                prevTransform.current = flowTransform;\n                if (event.sourceEvent?.type === 'mousedown') {\n                    store.setState({ paneDragging: true });\n                }\n                onViewportChangeStart?.(flowTransform);\n                onMoveStart?.(event.sourceEvent, flowTransform);\n            });\n        }\n    }, [d3Zoom, onMoveStart]);\n    useEffect(() => {\n        if (d3Zoom) {\n            if (userSelectionActive && !isZoomingOrPanning.current) {\n                d3Zoom.on('zoom', null);\n            }\n            else if (!userSelectionActive) {\n                d3Zoom.on('zoom', (event) => {\n                    const { onViewportChange } = store.getState();\n                    store.setState({ transform: [event.transform.x, event.transform.y, event.transform.k] });\n                    zoomedWithRightMouseButton.current = !!(onPaneContextMenu && isRightClickPan(panOnDrag, mouseButton.current ?? 0));\n                    if ((onMove || onViewportChange) && !event.sourceEvent?.internal) {\n                        const flowTransform = eventToFlowTransform(event.transform);\n                        onViewportChange?.(flowTransform);\n                        onMove?.(event.sourceEvent, flowTransform);\n                    }\n                });\n            }\n        }\n    }, [userSelectionActive, d3Zoom, onMove, panOnDrag, onPaneContextMenu]);\n    useEffect(() => {\n        if (d3Zoom) {\n            d3Zoom.on('end', (event) => {\n                if (!event.sourceEvent || event.sourceEvent.internal) {\n                    return null;\n                }\n                const { onViewportChangeEnd } = store.getState();\n                isZoomingOrPanning.current = false;\n                store.setState({ paneDragging: false });\n                if (onPaneContextMenu &&\n                    isRightClickPan(panOnDrag, mouseButton.current ?? 0) &&\n                    !zoomedWithRightMouseButton.current) {\n                    onPaneContextMenu(event.sourceEvent);\n                }\n                zoomedWithRightMouseButton.current = false;\n                if ((onMoveEnd || onViewportChangeEnd) && viewChanged(prevTransform.current, event.transform)) {\n                    const flowTransform = eventToFlowTransform(event.transform);\n                    prevTransform.current = flowTransform;\n                    clearTimeout(timerId.current);\n                    timerId.current = setTimeout(() => {\n                        onViewportChangeEnd?.(flowTransform);\n                        onMoveEnd?.(event.sourceEvent, flowTransform);\n                    }, panOnScroll ? 150 : 0);\n                }\n            });\n        }\n    }, [d3Zoom, panOnScroll, panOnDrag, onMoveEnd, onPaneContextMenu]);\n    useEffect(() => {\n        if (d3Zoom) {\n            d3Zoom.filter((event) => {\n                const zoomScroll = zoomActivationKeyPressed || zoomOnScroll;\n                const pinchZoom = zoomOnPinch && event.ctrlKey;\n                if ((panOnDrag === true || (Array.isArray(panOnDrag) && panOnDrag.includes(1))) &&\n                    event.button === 1 &&\n                    event.type === 'mousedown' &&\n                    (isWrappedWithClass(event, 'react-flow__node') || isWrappedWithClass(event, 'react-flow__edge'))) {\n                    return true;\n                }\n                // if all interactions are disabled, we prevent all zoom events\n                if (!panOnDrag && !zoomScroll && !panOnScroll && !zoomOnDoubleClick && !zoomOnPinch) {\n                    return false;\n                }\n                // during a selection we prevent all other interactions\n                if (userSelectionActive) {\n                    return false;\n                }\n                // if zoom on double click is disabled, we prevent the double click event\n                if (!zoomOnDoubleClick && event.type === 'dblclick') {\n                    return false;\n                }\n                // if the target element is inside an element with the nowheel class, we prevent zooming\n                if (isWrappedWithClass(event, noWheelClassName) && event.type === 'wheel') {\n                    return false;\n                }\n                // if the target element is inside an element with the nopan class, we prevent panning\n                if (isWrappedWithClass(event, noPanClassName) &&\n                    (event.type !== 'wheel' || (panOnScroll && event.type === 'wheel' && !zoomActivationKeyPressed))) {\n                    return false;\n                }\n                if (!zoomOnPinch && event.ctrlKey && event.type === 'wheel') {\n                    return false;\n                }\n                // when there is no scroll handling enabled, we prevent all wheel events\n                if (!zoomScroll && !panOnScroll && !pinchZoom && event.type === 'wheel') {\n                    return false;\n                }\n                // if the pane is not movable, we prevent dragging it with mousestart or touchstart\n                if (!panOnDrag && (event.type === 'mousedown' || event.type === 'touchstart')) {\n                    return false;\n                }\n                // if the pane is only movable using allowed clicks\n                if (Array.isArray(panOnDrag) && !panOnDrag.includes(event.button) && event.type === 'mousedown') {\n                    return false;\n                }\n                // We only allow right clicks if pan on drag is set to right click\n                const buttonAllowed = (Array.isArray(panOnDrag) && panOnDrag.includes(event.button)) || !event.button || event.button <= 1;\n                // default filter for d3-zoom\n                return (!event.ctrlKey || event.type === 'wheel') && buttonAllowed;\n            });\n        }\n    }, [\n        userSelectionActive,\n        d3Zoom,\n        zoomOnScroll,\n        zoomOnPinch,\n        panOnScroll,\n        zoomOnDoubleClick,\n        panOnDrag,\n        elementsSelectable,\n        zoomActivationKeyPressed,\n    ]);\n    return (React.createElement(\"div\", { className: \"react-flow__renderer\", ref: zoomPane, style: containerStyle }, children));\n};\n\nconst selector$9 = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    userSelectionRect: s.userSelectionRect,\n});\nfunction UserSelection() {\n    const { userSelectionActive, userSelectionRect } = useStore(selector$9, shallow);\n    const isActive = userSelectionActive && userSelectionRect;\n    if (!isActive) {\n        return null;\n    }\n    return (React.createElement(\"div\", { className: \"react-flow__selection react-flow__container\", style: {\n            width: userSelectionRect.width,\n            height: userSelectionRect.height,\n            transform: `translate(${userSelectionRect.x}px, ${userSelectionRect.y}px)`,\n        } }));\n}\n\nfunction handleParentExpand(res, updateItem) {\n    const parentId = updateItem.parentNode || updateItem.parentId;\n    const parent = res.find((e) => e.id === parentId);\n    if (parent) {\n        const extendWidth = updateItem.position.x + updateItem.width - parent.width;\n        const extendHeight = updateItem.position.y + updateItem.height - parent.height;\n        if (extendWidth > 0 || extendHeight > 0 || updateItem.position.x < 0 || updateItem.position.y < 0) {\n            parent.style = { ...parent.style } || {};\n            parent.style.width = parent.style.width ?? parent.width;\n            parent.style.height = parent.style.height ?? parent.height;\n            if (extendWidth > 0) {\n                parent.style.width += extendWidth;\n            }\n            if (extendHeight > 0) {\n                parent.style.height += extendHeight;\n            }\n            if (updateItem.position.x < 0) {\n                const xDiff = Math.abs(updateItem.position.x);\n                parent.position.x = parent.position.x - xDiff;\n                parent.style.width += xDiff;\n                updateItem.position.x = 0;\n            }\n            if (updateItem.position.y < 0) {\n                const yDiff = Math.abs(updateItem.position.y);\n                parent.position.y = parent.position.y - yDiff;\n                parent.style.height += yDiff;\n                updateItem.position.y = 0;\n            }\n            parent.width = parent.style.width;\n            parent.height = parent.style.height;\n        }\n    }\n}\nfunction applyChanges(changes, elements) {\n    // we need this hack to handle the setNodes and setEdges function of the useReactFlow hook for controlled flows\n    if (changes.some((c) => c.type === 'reset')) {\n        return changes.filter((c) => c.type === 'reset').map((c) => c.item);\n    }\n    const initElements = changes.filter((c) => c.type === 'add').map((c) => c.item);\n    return elements.reduce((res, item) => {\n        const currentChanges = changes.filter((c) => c.id === item.id);\n        if (currentChanges.length === 0) {\n            res.push(item);\n            return res;\n        }\n        const updateItem = { ...item };\n        for (const currentChange of currentChanges) {\n            if (currentChange) {\n                switch (currentChange.type) {\n                    case 'select': {\n                        updateItem.selected = currentChange.selected;\n                        break;\n                    }\n                    case 'position': {\n                        if (typeof currentChange.position !== 'undefined') {\n                            updateItem.position = currentChange.position;\n                        }\n                        if (typeof currentChange.positionAbsolute !== 'undefined') {\n                            updateItem.positionAbsolute = currentChange.positionAbsolute;\n                        }\n                        if (typeof currentChange.dragging !== 'undefined') {\n                            updateItem.dragging = currentChange.dragging;\n                        }\n                        if (updateItem.expandParent) {\n                            handleParentExpand(res, updateItem);\n                        }\n                        break;\n                    }\n                    case 'dimensions': {\n                        if (typeof currentChange.dimensions !== 'undefined') {\n                            updateItem.width = currentChange.dimensions.width;\n                            updateItem.height = currentChange.dimensions.height;\n                        }\n                        if (typeof currentChange.updateStyle !== 'undefined') {\n                            updateItem.style = { ...(updateItem.style || {}), ...currentChange.dimensions };\n                        }\n                        if (typeof currentChange.resizing === 'boolean') {\n                            updateItem.resizing = currentChange.resizing;\n                        }\n                        if (updateItem.expandParent) {\n                            handleParentExpand(res, updateItem);\n                        }\n                        break;\n                    }\n                    case 'remove': {\n                        return res;\n                    }\n                }\n            }\n        }\n        res.push(updateItem);\n        return res;\n    }, initElements);\n}\nfunction applyNodeChanges(changes, nodes) {\n    return applyChanges(changes, nodes);\n}\nfunction applyEdgeChanges(changes, edges) {\n    return applyChanges(changes, edges);\n}\nconst createSelectionChange = (id, selected) => ({\n    id,\n    type: 'select',\n    selected,\n});\nfunction getSelectionChanges(items, selectedIds) {\n    return items.reduce((res, item) => {\n        const willBeSelected = selectedIds.includes(item.id);\n        if (!item.selected && willBeSelected) {\n            item.selected = true;\n            res.push(createSelectionChange(item.id, true));\n        }\n        else if (item.selected && !willBeSelected) {\n            item.selected = false;\n            res.push(createSelectionChange(item.id, false));\n        }\n        return res;\n    }, []);\n}\n\n/**\n * The user selection rectangle gets displayed when a user drags the mouse while pressing shift\n */\nconst wrapHandler = (handler, containerRef) => {\n    return (event) => {\n        if (event.target !== containerRef.current) {\n            return;\n        }\n        handler?.(event);\n    };\n};\nconst selector$8 = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    elementsSelectable: s.elementsSelectable,\n    dragging: s.paneDragging,\n});\nconst Pane = memo(({ isSelecting, selectionMode = SelectionMode.Full, panOnDrag, onSelectionStart, onSelectionEnd, onPaneClick, onPaneContextMenu, onPaneScroll, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, children, }) => {\n    const container = useRef(null);\n    const store = useStoreApi();\n    const prevSelectedNodesCount = useRef(0);\n    const prevSelectedEdgesCount = useRef(0);\n    const containerBounds = useRef();\n    const { userSelectionActive, elementsSelectable, dragging } = useStore(selector$8, shallow);\n    const resetUserSelection = () => {\n        store.setState({ userSelectionActive: false, userSelectionRect: null });\n        prevSelectedNodesCount.current = 0;\n        prevSelectedEdgesCount.current = 0;\n    };\n    const onClick = (event) => {\n        onPaneClick?.(event);\n        store.getState().resetSelectedElements();\n        store.setState({ nodesSelectionActive: false });\n    };\n    const onContextMenu = (event) => {\n        if (Array.isArray(panOnDrag) && panOnDrag?.includes(2)) {\n            event.preventDefault();\n            return;\n        }\n        onPaneContextMenu?.(event);\n    };\n    const onWheel = onPaneScroll ? (event) => onPaneScroll(event) : undefined;\n    const onMouseDown = (event) => {\n        const { resetSelectedElements, domNode } = store.getState();\n        containerBounds.current = domNode?.getBoundingClientRect();\n        if (!elementsSelectable ||\n            !isSelecting ||\n            event.button !== 0 ||\n            event.target !== container.current ||\n            !containerBounds.current) {\n            return;\n        }\n        const { x, y } = getEventPosition(event, containerBounds.current);\n        resetSelectedElements();\n        store.setState({\n            userSelectionRect: {\n                width: 0,\n                height: 0,\n                startX: x,\n                startY: y,\n                x,\n                y,\n            },\n        });\n        onSelectionStart?.(event);\n    };\n    const onMouseMove = (event) => {\n        const { userSelectionRect, nodeInternals, edges, transform, onNodesChange, onEdgesChange, nodeOrigin, getNodes } = store.getState();\n        if (!isSelecting || !containerBounds.current || !userSelectionRect) {\n            return;\n        }\n        store.setState({ userSelectionActive: true, nodesSelectionActive: false });\n        const mousePos = getEventPosition(event, containerBounds.current);\n        const startX = userSelectionRect.startX ?? 0;\n        const startY = userSelectionRect.startY ?? 0;\n        const nextUserSelectRect = {\n            ...userSelectionRect,\n            x: mousePos.x < startX ? mousePos.x : startX,\n            y: mousePos.y < startY ? mousePos.y : startY,\n            width: Math.abs(mousePos.x - startX),\n            height: Math.abs(mousePos.y - startY),\n        };\n        const nodes = getNodes();\n        const selectedNodes = getNodesInside(nodeInternals, nextUserSelectRect, transform, selectionMode === SelectionMode.Partial, true, nodeOrigin);\n        const selectedEdgeIds = getConnectedEdges(selectedNodes, edges).map((e) => e.id);\n        const selectedNodeIds = selectedNodes.map((n) => n.id);\n        if (prevSelectedNodesCount.current !== selectedNodeIds.length) {\n            prevSelectedNodesCount.current = selectedNodeIds.length;\n            const changes = getSelectionChanges(nodes, selectedNodeIds);\n            if (changes.length) {\n                onNodesChange?.(changes);\n            }\n        }\n        if (prevSelectedEdgesCount.current !== selectedEdgeIds.length) {\n            prevSelectedEdgesCount.current = selectedEdgeIds.length;\n            const changes = getSelectionChanges(edges, selectedEdgeIds);\n            if (changes.length) {\n                onEdgesChange?.(changes);\n            }\n        }\n        store.setState({\n            userSelectionRect: nextUserSelectRect,\n        });\n    };\n    const onMouseUp = (event) => {\n        if (event.button !== 0) {\n            return;\n        }\n        const { userSelectionRect } = store.getState();\n        // We only want to trigger click functions when in selection mode if\n        // the user did not move the mouse.\n        if (!userSelectionActive && userSelectionRect && event.target === container.current) {\n            onClick?.(event);\n        }\n        store.setState({ nodesSelectionActive: prevSelectedNodesCount.current > 0 });\n        resetUserSelection();\n        onSelectionEnd?.(event);\n    };\n    const onMouseLeave = (event) => {\n        if (userSelectionActive) {\n            store.setState({ nodesSelectionActive: prevSelectedNodesCount.current > 0 });\n            onSelectionEnd?.(event);\n        }\n        resetUserSelection();\n    };\n    const hasActiveSelection = elementsSelectable && (isSelecting || userSelectionActive);\n    return (React.createElement(\"div\", { className: cc(['react-flow__pane', { dragging, selection: isSelecting }]), onClick: hasActiveSelection ? undefined : wrapHandler(onClick, container), onContextMenu: wrapHandler(onContextMenu, container), onWheel: wrapHandler(onWheel, container), onMouseEnter: hasActiveSelection ? undefined : onPaneMouseEnter, onMouseDown: hasActiveSelection ? onMouseDown : undefined, onMouseMove: hasActiveSelection ? onMouseMove : onPaneMouseMove, onMouseUp: hasActiveSelection ? onMouseUp : undefined, onMouseLeave: hasActiveSelection ? onMouseLeave : onPaneMouseLeave, ref: container, style: containerStyle },\n        children,\n        React.createElement(UserSelection, null)));\n});\nPane.displayName = 'Pane';\n\nfunction isParentSelected(node, nodeInternals) {\n    const parentId = node.parentNode || node.parentId;\n    if (!parentId) {\n        return false;\n    }\n    const parentNode = nodeInternals.get(parentId);\n    if (!parentNode) {\n        return false;\n    }\n    if (parentNode.selected) {\n        return true;\n    }\n    return isParentSelected(parentNode, nodeInternals);\n}\nfunction hasSelector(target, selector, nodeRef) {\n    let current = target;\n    do {\n        if (current?.matches(selector))\n            return true;\n        if (current === nodeRef.current)\n            return false;\n        current = current.parentElement;\n    } while (current);\n    return false;\n}\n// looks for all selected nodes and created a NodeDragItem for each of them\nfunction getDragItems(nodeInternals, nodesDraggable, mousePos, nodeId) {\n    return Array.from(nodeInternals.values())\n        .filter((n) => (n.selected || n.id === nodeId) &&\n        (!n.parentNode || n.parentId || !isParentSelected(n, nodeInternals)) &&\n        (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined')))\n        .map((n) => ({\n        id: n.id,\n        position: n.position || { x: 0, y: 0 },\n        positionAbsolute: n.positionAbsolute || { x: 0, y: 0 },\n        distance: {\n            x: mousePos.x - (n.positionAbsolute?.x ?? 0),\n            y: mousePos.y - (n.positionAbsolute?.y ?? 0),\n        },\n        delta: {\n            x: 0,\n            y: 0,\n        },\n        extent: n.extent,\n        parentNode: n.parentNode || n.parentId,\n        parentId: n.parentNode || n.parentId,\n        width: n.width,\n        height: n.height,\n        expandParent: n.expandParent,\n    }));\n}\nfunction clampNodeExtent(node, extent) {\n    if (!extent || extent === 'parent') {\n        return extent;\n    }\n    return [extent[0], [extent[1][0] - (node.width || 0), extent[1][1] - (node.height || 0)]];\n}\nfunction calcNextPosition(node, nextPosition, nodeInternals, nodeExtent, nodeOrigin = [0, 0], onError) {\n    const clampedNodeExtent = clampNodeExtent(node, node.extent || nodeExtent);\n    let currentExtent = clampedNodeExtent;\n    const parentId = node.parentNode || node.parentId;\n    if (node.extent === 'parent' && !node.expandParent) {\n        if (parentId && node.width && node.height) {\n            const parent = nodeInternals.get(parentId);\n            const { x: parentX, y: parentY } = getNodePositionWithOrigin(parent, nodeOrigin).positionAbsolute;\n            currentExtent =\n                parent && isNumeric(parentX) && isNumeric(parentY) && isNumeric(parent.width) && isNumeric(parent.height)\n                    ? [\n                        [parentX + node.width * nodeOrigin[0], parentY + node.height * nodeOrigin[1]],\n                        [\n                            parentX + parent.width - node.width + node.width * nodeOrigin[0],\n                            parentY + parent.height - node.height + node.height * nodeOrigin[1],\n                        ],\n                    ]\n                    : currentExtent;\n        }\n        else {\n            onError?.('005', errorMessages['error005']());\n            currentExtent = clampedNodeExtent;\n        }\n    }\n    else if (node.extent && parentId && node.extent !== 'parent') {\n        const parent = nodeInternals.get(parentId);\n        const { x: parentX, y: parentY } = getNodePositionWithOrigin(parent, nodeOrigin).positionAbsolute;\n        currentExtent = [\n            [node.extent[0][0] + parentX, node.extent[0][1] + parentY],\n            [node.extent[1][0] + parentX, node.extent[1][1] + parentY],\n        ];\n    }\n    let parentPosition = { x: 0, y: 0 };\n    if (parentId) {\n        const parentNode = nodeInternals.get(parentId);\n        parentPosition = getNodePositionWithOrigin(parentNode, nodeOrigin).positionAbsolute;\n    }\n    const positionAbsolute = currentExtent && currentExtent !== 'parent'\n        ? clampPosition(nextPosition, currentExtent)\n        : nextPosition;\n    return {\n        position: {\n            x: positionAbsolute.x - parentPosition.x,\n            y: positionAbsolute.y - parentPosition.y,\n        },\n        positionAbsolute,\n    };\n}\n// returns two params:\n// 1. the dragged node (or the first of the list, if we are dragging a node selection)\n// 2. array of selected nodes (for multi selections)\nfunction getEventHandlerParams({ nodeId, dragItems, nodeInternals, }) {\n    const extentedDragItems = dragItems.map((n) => {\n        const node = nodeInternals.get(n.id);\n        return {\n            ...node,\n            position: n.position,\n            positionAbsolute: n.positionAbsolute,\n        };\n    });\n    return [nodeId ? extentedDragItems.find((n) => n.id === nodeId) : extentedDragItems[0], extentedDragItems];\n}\n\nconst getHandleBounds = (selector, nodeElement, zoom, nodeOrigin) => {\n    const handles = nodeElement.querySelectorAll(selector);\n    if (!handles || !handles.length) {\n        return null;\n    }\n    const handlesArray = Array.from(handles);\n    const nodeBounds = nodeElement.getBoundingClientRect();\n    const nodeOffset = {\n        x: nodeBounds.width * nodeOrigin[0],\n        y: nodeBounds.height * nodeOrigin[1],\n    };\n    return handlesArray.map((handle) => {\n        const handleBounds = handle.getBoundingClientRect();\n        return {\n            id: handle.getAttribute('data-handleid'),\n            position: handle.getAttribute('data-handlepos'),\n            x: (handleBounds.left - nodeBounds.left - nodeOffset.x) / zoom,\n            y: (handleBounds.top - nodeBounds.top - nodeOffset.y) / zoom,\n            ...getDimensions(handle),\n        };\n    });\n};\nfunction getMouseHandler(id, getState, handler) {\n    return handler === undefined\n        ? handler\n        : (event) => {\n            const node = getState().nodeInternals.get(id);\n            if (node) {\n                handler(event, { ...node });\n            }\n        };\n}\n// this handler is called by\n// 1. the click handler when node is not draggable or selectNodesOnDrag = false\n// or\n// 2. the on drag start handler when node is draggable and selectNodesOnDrag = true\nfunction handleNodeClick({ id, store, unselect = false, nodeRef, }) {\n    const { addSelectedNodes, unselectNodesAndEdges, multiSelectionActive, nodeInternals, onError } = store.getState();\n    const node = nodeInternals.get(id);\n    if (!node) {\n        onError?.('012', errorMessages['error012'](id));\n        return;\n    }\n    store.setState({ nodesSelectionActive: false });\n    if (!node.selected) {\n        addSelectedNodes([id]);\n    }\n    else if (unselect || (node.selected && multiSelectionActive)) {\n        unselectNodesAndEdges({ nodes: [node], edges: [] });\n        requestAnimationFrame(() => nodeRef?.current?.blur());\n    }\n}\n\nfunction useGetPointerPosition() {\n    const store = useStoreApi();\n    // returns the pointer position projected to the RF coordinate system\n    const getPointerPosition = useCallback(({ sourceEvent }) => {\n        const { transform, snapGrid, snapToGrid } = store.getState();\n        const x = sourceEvent.touches ? sourceEvent.touches[0].clientX : sourceEvent.clientX;\n        const y = sourceEvent.touches ? sourceEvent.touches[0].clientY : sourceEvent.clientY;\n        const pointerPos = {\n            x: (x - transform[0]) / transform[2],\n            y: (y - transform[1]) / transform[2],\n        };\n        // we need the snapped position in order to be able to skip unnecessary drag events\n        return {\n            xSnapped: snapToGrid ? snapGrid[0] * Math.round(pointerPos.x / snapGrid[0]) : pointerPos.x,\n            ySnapped: snapToGrid ? snapGrid[1] * Math.round(pointerPos.y / snapGrid[1]) : pointerPos.y,\n            ...pointerPos,\n        };\n    }, []);\n    return getPointerPosition;\n}\n\nfunction wrapSelectionDragFunc(selectionFunc) {\n    return (event, _, nodes) => selectionFunc?.(event, nodes);\n}\nfunction useDrag({ nodeRef, disabled = false, noDragClassName, handleSelector, nodeId, isSelectable, selectNodesOnDrag, }) {\n    const store = useStoreApi();\n    const [dragging, setDragging] = useState(false);\n    const dragItems = useRef([]);\n    const lastPos = useRef({ x: null, y: null });\n    const autoPanId = useRef(0);\n    const containerBounds = useRef(null);\n    const mousePosition = useRef({ x: 0, y: 0 });\n    const dragEvent = useRef(null);\n    const autoPanStarted = useRef(false);\n    const dragStarted = useRef(false);\n    const abortDrag = useRef(false);\n    const getPointerPosition = useGetPointerPosition();\n    useEffect(() => {\n        if (nodeRef?.current) {\n            const selection = select(nodeRef.current);\n            const updateNodes = ({ x, y }) => {\n                const { nodeInternals, onNodeDrag, onSelectionDrag, updateNodePositions, nodeExtent, snapGrid, snapToGrid, nodeOrigin, onError, } = store.getState();\n                lastPos.current = { x, y };\n                let hasChange = false;\n                let nodesBox = { x: 0, y: 0, x2: 0, y2: 0 };\n                if (dragItems.current.length > 1 && nodeExtent) {\n                    const rect = getNodesBounds(dragItems.current, nodeOrigin);\n                    nodesBox = rectToBox(rect);\n                }\n                dragItems.current = dragItems.current.map((n) => {\n                    const nextPosition = { x: x - n.distance.x, y: y - n.distance.y };\n                    if (snapToGrid) {\n                        nextPosition.x = snapGrid[0] * Math.round(nextPosition.x / snapGrid[0]);\n                        nextPosition.y = snapGrid[1] * Math.round(nextPosition.y / snapGrid[1]);\n                    }\n                    // if there is selection with multiple nodes and a node extent is set, we need to adjust the node extent for each node\n                    // based on its position so that the node stays at it's position relative to the selection.\n                    const adjustedNodeExtent = [\n                        [nodeExtent[0][0], nodeExtent[0][1]],\n                        [nodeExtent[1][0], nodeExtent[1][1]],\n                    ];\n                    if (dragItems.current.length > 1 && nodeExtent && !n.extent) {\n                        adjustedNodeExtent[0][0] = n.positionAbsolute.x - nodesBox.x + nodeExtent[0][0];\n                        adjustedNodeExtent[1][0] = n.positionAbsolute.x + (n.width ?? 0) - nodesBox.x2 + nodeExtent[1][0];\n                        adjustedNodeExtent[0][1] = n.positionAbsolute.y - nodesBox.y + nodeExtent[0][1];\n                        adjustedNodeExtent[1][1] = n.positionAbsolute.y + (n.height ?? 0) - nodesBox.y2 + nodeExtent[1][1];\n                    }\n                    const updatedPos = calcNextPosition(n, nextPosition, nodeInternals, adjustedNodeExtent, nodeOrigin, onError);\n                    // we want to make sure that we only fire a change event when there is a change\n                    hasChange = hasChange || n.position.x !== updatedPos.position.x || n.position.y !== updatedPos.position.y;\n                    n.position = updatedPos.position;\n                    n.positionAbsolute = updatedPos.positionAbsolute;\n                    return n;\n                });\n                if (!hasChange) {\n                    return;\n                }\n                updateNodePositions(dragItems.current, true, true);\n                setDragging(true);\n                const onDrag = nodeId ? onNodeDrag : wrapSelectionDragFunc(onSelectionDrag);\n                if (onDrag && dragEvent.current) {\n                    const [currentNode, nodes] = getEventHandlerParams({\n                        nodeId,\n                        dragItems: dragItems.current,\n                        nodeInternals,\n                    });\n                    onDrag(dragEvent.current, currentNode, nodes);\n                }\n            };\n            const autoPan = () => {\n                if (!containerBounds.current) {\n                    return;\n                }\n                const [xMovement, yMovement] = calcAutoPan(mousePosition.current, containerBounds.current);\n                if (xMovement !== 0 || yMovement !== 0) {\n                    const { transform, panBy } = store.getState();\n                    lastPos.current.x = (lastPos.current.x ?? 0) - xMovement / transform[2];\n                    lastPos.current.y = (lastPos.current.y ?? 0) - yMovement / transform[2];\n                    if (panBy({ x: xMovement, y: yMovement })) {\n                        updateNodes(lastPos.current);\n                    }\n                }\n                autoPanId.current = requestAnimationFrame(autoPan);\n            };\n            const startDrag = (event) => {\n                const { nodeInternals, multiSelectionActive, nodesDraggable, unselectNodesAndEdges, onNodeDragStart, onSelectionDragStart, } = store.getState();\n                dragStarted.current = true;\n                const onStart = nodeId ? onNodeDragStart : wrapSelectionDragFunc(onSelectionDragStart);\n                if ((!selectNodesOnDrag || !isSelectable) && !multiSelectionActive && nodeId) {\n                    if (!nodeInternals.get(nodeId)?.selected) {\n                        // we need to reset selected nodes when selectNodesOnDrag=false\n                        unselectNodesAndEdges();\n                    }\n                }\n                if (nodeId && isSelectable && selectNodesOnDrag) {\n                    handleNodeClick({\n                        id: nodeId,\n                        store,\n                        nodeRef: nodeRef,\n                    });\n                }\n                const pointerPos = getPointerPosition(event);\n                lastPos.current = pointerPos;\n                dragItems.current = getDragItems(nodeInternals, nodesDraggable, pointerPos, nodeId);\n                if (onStart && dragItems.current) {\n                    const [currentNode, nodes] = getEventHandlerParams({\n                        nodeId,\n                        dragItems: dragItems.current,\n                        nodeInternals,\n                    });\n                    onStart(event.sourceEvent, currentNode, nodes);\n                }\n            };\n            if (disabled) {\n                selection.on('.drag', null);\n            }\n            else {\n                const dragHandler = drag()\n                    .on('start', (event) => {\n                    const { domNode, nodeDragThreshold } = store.getState();\n                    if (nodeDragThreshold === 0) {\n                        startDrag(event);\n                    }\n                    abortDrag.current = false;\n                    const pointerPos = getPointerPosition(event);\n                    lastPos.current = pointerPos;\n                    containerBounds.current = domNode?.getBoundingClientRect() || null;\n                    mousePosition.current = getEventPosition(event.sourceEvent, containerBounds.current);\n                })\n                    .on('drag', (event) => {\n                    const pointerPos = getPointerPosition(event);\n                    const { autoPanOnNodeDrag, nodeDragThreshold } = store.getState();\n                    if (event.sourceEvent.type === 'touchmove' && event.sourceEvent.touches.length > 1) {\n                        abortDrag.current = true;\n                    }\n                    if (abortDrag.current) {\n                        return;\n                    }\n                    if (!autoPanStarted.current && dragStarted.current && autoPanOnNodeDrag) {\n                        autoPanStarted.current = true;\n                        autoPan();\n                    }\n                    if (!dragStarted.current) {\n                        const x = pointerPos.xSnapped - (lastPos?.current?.x ?? 0);\n                        const y = pointerPos.ySnapped - (lastPos?.current?.y ?? 0);\n                        const distance = Math.sqrt(x * x + y * y);\n                        if (distance > nodeDragThreshold) {\n                            startDrag(event);\n                        }\n                    }\n                    // skip events without movement\n                    if ((lastPos.current.x !== pointerPos.xSnapped || lastPos.current.y !== pointerPos.ySnapped) &&\n                        dragItems.current &&\n                        dragStarted.current) {\n                        dragEvent.current = event.sourceEvent;\n                        mousePosition.current = getEventPosition(event.sourceEvent, containerBounds.current);\n                        updateNodes(pointerPos);\n                    }\n                })\n                    .on('end', (event) => {\n                    if (!dragStarted.current || abortDrag.current) {\n                        return;\n                    }\n                    setDragging(false);\n                    autoPanStarted.current = false;\n                    dragStarted.current = false;\n                    cancelAnimationFrame(autoPanId.current);\n                    if (dragItems.current) {\n                        const { updateNodePositions, nodeInternals, onNodeDragStop, onSelectionDragStop } = store.getState();\n                        const onStop = nodeId ? onNodeDragStop : wrapSelectionDragFunc(onSelectionDragStop);\n                        updateNodePositions(dragItems.current, false, false);\n                        if (onStop) {\n                            const [currentNode, nodes] = getEventHandlerParams({\n                                nodeId,\n                                dragItems: dragItems.current,\n                                nodeInternals,\n                            });\n                            onStop(event.sourceEvent, currentNode, nodes);\n                        }\n                    }\n                })\n                    .filter((event) => {\n                    const target = event.target;\n                    const isDraggable = !event.button &&\n                        (!noDragClassName || !hasSelector(target, `.${noDragClassName}`, nodeRef)) &&\n                        (!handleSelector || hasSelector(target, handleSelector, nodeRef));\n                    return isDraggable;\n                });\n                selection.call(dragHandler);\n                return () => {\n                    selection.on('.drag', null);\n                };\n            }\n        }\n    }, [\n        nodeRef,\n        disabled,\n        noDragClassName,\n        handleSelector,\n        isSelectable,\n        store,\n        nodeId,\n        selectNodesOnDrag,\n        getPointerPosition,\n    ]);\n    return dragging;\n}\n\nfunction useUpdateNodePositions() {\n    const store = useStoreApi();\n    const updatePositions = useCallback((params) => {\n        const { nodeInternals, nodeExtent, updateNodePositions, getNodes, snapToGrid, snapGrid, onError, nodesDraggable } = store.getState();\n        const selectedNodes = getNodes().filter((n) => n.selected && (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined')));\n        // by default a node moves 5px on each key press, or 20px if shift is pressed\n        // if snap grid is enabled, we use that for the velocity.\n        const xVelo = snapToGrid ? snapGrid[0] : 5;\n        const yVelo = snapToGrid ? snapGrid[1] : 5;\n        const factor = params.isShiftPressed ? 4 : 1;\n        const positionDiffX = params.x * xVelo * factor;\n        const positionDiffY = params.y * yVelo * factor;\n        const nodeUpdates = selectedNodes.map((n) => {\n            if (n.positionAbsolute) {\n                const nextPosition = { x: n.positionAbsolute.x + positionDiffX, y: n.positionAbsolute.y + positionDiffY };\n                if (snapToGrid) {\n                    nextPosition.x = snapGrid[0] * Math.round(nextPosition.x / snapGrid[0]);\n                    nextPosition.y = snapGrid[1] * Math.round(nextPosition.y / snapGrid[1]);\n                }\n                const { positionAbsolute, position } = calcNextPosition(n, nextPosition, nodeInternals, nodeExtent, undefined, onError);\n                n.position = position;\n                n.positionAbsolute = positionAbsolute;\n            }\n            return n;\n        });\n        updateNodePositions(nodeUpdates, true, false);\n    }, []);\n    return updatePositions;\n}\n\nconst arrowKeyDiffs = {\n    ArrowUp: { x: 0, y: -1 },\n    ArrowDown: { x: 0, y: 1 },\n    ArrowLeft: { x: -1, y: 0 },\n    ArrowRight: { x: 1, y: 0 },\n};\nvar wrapNode = (NodeComponent) => {\n    const NodeWrapper = ({ id, type, data, xPos, yPos, xPosOrigin, yPosOrigin, selected, onClick, onMouseEnter, onMouseMove, onMouseLeave, onContextMenu, onDoubleClick, style, className, isDraggable, isSelectable, isConnectable, isFocusable, selectNodesOnDrag, sourcePosition, targetPosition, hidden, resizeObserver, dragHandle, zIndex, isParent, noDragClassName, noPanClassName, initialized, disableKeyboardA11y, ariaLabel, rfId, hasHandleBounds, }) => {\n        const store = useStoreApi();\n        const nodeRef = useRef(null);\n        const prevNodeRef = useRef(null);\n        const prevSourcePosition = useRef(sourcePosition);\n        const prevTargetPosition = useRef(targetPosition);\n        const prevType = useRef(type);\n        const hasPointerEvents = isSelectable || isDraggable || onClick || onMouseEnter || onMouseMove || onMouseLeave;\n        const updatePositions = useUpdateNodePositions();\n        const onMouseEnterHandler = getMouseHandler(id, store.getState, onMouseEnter);\n        const onMouseMoveHandler = getMouseHandler(id, store.getState, onMouseMove);\n        const onMouseLeaveHandler = getMouseHandler(id, store.getState, onMouseLeave);\n        const onContextMenuHandler = getMouseHandler(id, store.getState, onContextMenu);\n        const onDoubleClickHandler = getMouseHandler(id, store.getState, onDoubleClick);\n        const onSelectNodeHandler = (event) => {\n            const { nodeDragThreshold } = store.getState();\n            if (isSelectable && (!selectNodesOnDrag || !isDraggable || nodeDragThreshold > 0)) {\n                // this handler gets called within the drag start event when selectNodesOnDrag=true\n                handleNodeClick({\n                    id,\n                    store,\n                    nodeRef,\n                });\n            }\n            if (onClick) {\n                const node = store.getState().nodeInternals.get(id);\n                if (node) {\n                    onClick(event, { ...node });\n                }\n            }\n        };\n        const onKeyDown = (event) => {\n            if (isInputDOMNode(event)) {\n                return;\n            }\n            if (disableKeyboardA11y) {\n                return;\n            }\n            if (elementSelectionKeys.includes(event.key) && isSelectable) {\n                const unselect = event.key === 'Escape';\n                handleNodeClick({\n                    id,\n                    store,\n                    unselect,\n                    nodeRef,\n                });\n            }\n            else if (isDraggable && selected && Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n                store.setState({\n                    ariaLiveMessage: `Moved selected node ${event.key\n                        .replace('Arrow', '')\n                        .toLowerCase()}. New position, x: ${~~xPos}, y: ${~~yPos}`,\n                });\n                updatePositions({\n                    x: arrowKeyDiffs[event.key].x,\n                    y: arrowKeyDiffs[event.key].y,\n                    isShiftPressed: event.shiftKey,\n                });\n            }\n        };\n        useEffect(() => {\n            return () => {\n                if (prevNodeRef.current) {\n                    resizeObserver?.unobserve(prevNodeRef.current);\n                    prevNodeRef.current = null;\n                }\n            };\n        }, []);\n        useEffect(() => {\n            if (nodeRef.current && !hidden) {\n                const currNode = nodeRef.current;\n                if (!initialized || !hasHandleBounds || prevNodeRef.current !== currNode) {\n                    // At this point we always want to make sure that the node gets re-measured / re-initialized.\n                    // We need to unobserve it first in case it is still observed\n                    if (prevNodeRef.current) {\n                        resizeObserver?.unobserve(prevNodeRef.current);\n                    }\n                    resizeObserver?.observe(currNode);\n                    prevNodeRef.current = currNode;\n                }\n            }\n        }, [hidden, initialized, hasHandleBounds]);\n        useEffect(() => {\n            // when the user programmatically changes the source or handle position, we re-initialize the node\n            const typeChanged = prevType.current !== type;\n            const sourcePosChanged = prevSourcePosition.current !== sourcePosition;\n            const targetPosChanged = prevTargetPosition.current !== targetPosition;\n            if (nodeRef.current && (typeChanged || sourcePosChanged || targetPosChanged)) {\n                if (typeChanged) {\n                    prevType.current = type;\n                }\n                if (sourcePosChanged) {\n                    prevSourcePosition.current = sourcePosition;\n                }\n                if (targetPosChanged) {\n                    prevTargetPosition.current = targetPosition;\n                }\n                store.getState().updateNodeDimensions([{ id, nodeElement: nodeRef.current, forceUpdate: true }]);\n            }\n        }, [id, type, sourcePosition, targetPosition]);\n        const dragging = useDrag({\n            nodeRef,\n            disabled: hidden || !isDraggable,\n            noDragClassName,\n            handleSelector: dragHandle,\n            nodeId: id,\n            isSelectable,\n            selectNodesOnDrag,\n        });\n        if (hidden) {\n            return null;\n        }\n        return (React.createElement(\"div\", { className: cc([\n                'react-flow__node',\n                `react-flow__node-${type}`,\n                {\n                    // this is overwritable by passing `nopan` as a class name\n                    [noPanClassName]: isDraggable,\n                },\n                className,\n                {\n                    selected,\n                    selectable: isSelectable,\n                    parent: isParent,\n                    dragging,\n                },\n            ]), ref: nodeRef, style: {\n                zIndex,\n                transform: `translate(${xPosOrigin}px,${yPosOrigin}px)`,\n                pointerEvents: hasPointerEvents ? 'all' : 'none',\n                visibility: initialized ? 'visible' : 'hidden',\n                ...style,\n            }, \"data-id\": id, \"data-testid\": `rf__node-${id}`, onMouseEnter: onMouseEnterHandler, onMouseMove: onMouseMoveHandler, onMouseLeave: onMouseLeaveHandler, onContextMenu: onContextMenuHandler, onClick: onSelectNodeHandler, onDoubleClick: onDoubleClickHandler, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: isFocusable ? 'button' : undefined, \"aria-describedby\": disableKeyboardA11y ? undefined : `${ARIA_NODE_DESC_KEY}-${rfId}`, \"aria-label\": ariaLabel },\n            React.createElement(Provider, { value: id },\n                React.createElement(NodeComponent, { id: id, data: data, type: type, xPos: xPos, yPos: yPos, selected: selected, isConnectable: isConnectable, sourcePosition: sourcePosition, targetPosition: targetPosition, dragging: dragging, dragHandle: dragHandle, zIndex: zIndex }))));\n    };\n    NodeWrapper.displayName = 'NodeWrapper';\n    return memo(NodeWrapper);\n};\n\n/**\n * The nodes selection rectangle gets displayed when a user\n * made a selection with on or several nodes\n */\nconst selector$7 = (s) => {\n    const selectedNodes = s.getNodes().filter((n) => n.selected);\n    return {\n        ...getNodesBounds(selectedNodes, s.nodeOrigin),\n        transformString: `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`,\n        userSelectionActive: s.userSelectionActive,\n    };\n};\nfunction NodesSelection({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y }) {\n    const store = useStoreApi();\n    const { width, height, x: left, y: top, transformString, userSelectionActive } = useStore(selector$7, shallow);\n    const updatePositions = useUpdateNodePositions();\n    const nodeRef = useRef(null);\n    useEffect(() => {\n        if (!disableKeyboardA11y) {\n            nodeRef.current?.focus({\n                preventScroll: true,\n            });\n        }\n    }, [disableKeyboardA11y]);\n    useDrag({\n        nodeRef,\n    });\n    if (userSelectionActive || !width || !height) {\n        return null;\n    }\n    const onContextMenu = onSelectionContextMenu\n        ? (event) => {\n            const selectedNodes = store\n                .getState()\n                .getNodes()\n                .filter((n) => n.selected);\n            onSelectionContextMenu(event, selectedNodes);\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            updatePositions({\n                x: arrowKeyDiffs[event.key].x,\n                y: arrowKeyDiffs[event.key].y,\n                isShiftPressed: event.shiftKey,\n            });\n        }\n    };\n    return (React.createElement(\"div\", { className: cc(['react-flow__nodesselection', 'react-flow__container', noPanClassName]), style: {\n            transform: transformString,\n        } },\n        React.createElement(\"div\", { ref: nodeRef, className: \"react-flow__nodesselection-rect\", onContextMenu: onContextMenu, tabIndex: disableKeyboardA11y ? undefined : -1, onKeyDown: disableKeyboardA11y ? undefined : onKeyDown, style: {\n                width,\n                height,\n                top,\n                left,\n            } })));\n}\nvar NodesSelection$1 = memo(NodesSelection);\n\nconst selector$6 = (s) => s.nodesSelectionActive;\nconst FlowRenderer = ({ children, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneContextMenu, onPaneScroll, deleteKeyCode, onMove, onMoveStart, onMoveEnd, selectionKeyCode, selectionOnDrag, selectionMode, onSelectionStart, onSelectionEnd, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, elementsSelectable, zoomOnScroll, zoomOnPinch, panOnScroll: _panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag: _panOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, onSelectionContextMenu, noWheelClassName, noPanClassName, disableKeyboardA11y, }) => {\n    const nodesSelectionActive = useStore(selector$6);\n    const selectionKeyPressed = useKeyPress(selectionKeyCode);\n    const panActivationKeyPressed = useKeyPress(panActivationKeyCode);\n    const panOnDrag = panActivationKeyPressed || _panOnDrag;\n    const panOnScroll = panActivationKeyPressed || _panOnScroll;\n    const isSelecting = selectionKeyPressed || (selectionOnDrag && panOnDrag !== true);\n    useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode });\n    return (React.createElement(ZoomPane, { onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, onPaneContextMenu: onPaneContextMenu, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, zoomOnDoubleClick: zoomOnDoubleClick, panOnDrag: !selectionKeyPressed && panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, zoomActivationKeyCode: zoomActivationKeyCode, preventScrolling: preventScrolling, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName },\n        React.createElement(Pane, { onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, panOnDrag: panOnDrag, isSelecting: !!isSelecting, selectionMode: selectionMode },\n            children,\n            nodesSelectionActive && (React.createElement(NodesSelection$1, { onSelectionContextMenu: onSelectionContextMenu, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y })))));\n};\nFlowRenderer.displayName = 'FlowRenderer';\nvar FlowRenderer$1 = memo(FlowRenderer);\n\nfunction useVisibleNodes(onlyRenderVisible) {\n    const nodes = useStore(useCallback((s) => onlyRenderVisible\n        ? getNodesInside(s.nodeInternals, { x: 0, y: 0, width: s.width, height: s.height }, s.transform, true)\n        : s.getNodes(), [onlyRenderVisible]));\n    return nodes;\n}\n\nfunction createNodeTypes(nodeTypes) {\n    const standardTypes = {\n        input: wrapNode((nodeTypes.input || InputNode$1)),\n        default: wrapNode((nodeTypes.default || DefaultNode$1)),\n        output: wrapNode((nodeTypes.output || OutputNode$1)),\n        group: wrapNode((nodeTypes.group || GroupNode)),\n    };\n    const wrappedTypes = {};\n    const specialTypes = Object.keys(nodeTypes)\n        .filter((k) => !['input', 'default', 'output', 'group'].includes(k))\n        .reduce((res, key) => {\n        res[key] = wrapNode((nodeTypes[key] || DefaultNode$1));\n        return res;\n    }, wrappedTypes);\n    return {\n        ...standardTypes,\n        ...specialTypes,\n    };\n}\nconst getPositionWithOrigin = ({ x, y, width, height, origin, }) => {\n    if (!width || !height) {\n        return { x, y };\n    }\n    if (origin[0] < 0 || origin[1] < 0 || origin[0] > 1 || origin[1] > 1) {\n        return { x, y };\n    }\n    return {\n        x: x - width * origin[0],\n        y: y - height * origin[1],\n    };\n};\n\nconst selector$5 = (s) => ({\n    nodesDraggable: s.nodesDraggable,\n    nodesConnectable: s.nodesConnectable,\n    nodesFocusable: s.nodesFocusable,\n    elementsSelectable: s.elementsSelectable,\n    updateNodeDimensions: s.updateNodeDimensions,\n    onError: s.onError,\n});\nconst NodeRenderer = (props) => {\n    const { nodesDraggable, nodesConnectable, nodesFocusable, elementsSelectable, updateNodeDimensions, onError } = useStore(selector$5, shallow);\n    const nodes = useVisibleNodes(props.onlyRenderVisibleElements);\n    const resizeObserverRef = useRef();\n    const resizeObserver = useMemo(() => {\n        if (typeof ResizeObserver === 'undefined') {\n            return null;\n        }\n        const observer = new ResizeObserver((entries) => {\n            const updates = entries.map((entry) => ({\n                id: entry.target.getAttribute('data-id'),\n                nodeElement: entry.target,\n                forceUpdate: true,\n            }));\n            updateNodeDimensions(updates);\n        });\n        resizeObserverRef.current = observer;\n        return observer;\n    }, []);\n    useEffect(() => {\n        return () => {\n            resizeObserverRef?.current?.disconnect();\n        };\n    }, []);\n    return (React.createElement(\"div\", { className: \"react-flow__nodes\", style: containerStyle }, nodes.map((node) => {\n        let nodeType = node.type || 'default';\n        if (!props.nodeTypes[nodeType]) {\n            onError?.('003', errorMessages['error003'](nodeType));\n            nodeType = 'default';\n        }\n        const NodeComponent = (props.nodeTypes[nodeType] || props.nodeTypes.default);\n        const isDraggable = !!(node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'));\n        const isSelectable = !!(node.selectable || (elementsSelectable && typeof node.selectable === 'undefined'));\n        const isConnectable = !!(node.connectable || (nodesConnectable && typeof node.connectable === 'undefined'));\n        const isFocusable = !!(node.focusable || (nodesFocusable && typeof node.focusable === 'undefined'));\n        const clampedPosition = props.nodeExtent\n            ? clampPosition(node.positionAbsolute, props.nodeExtent)\n            : node.positionAbsolute;\n        const posX = clampedPosition?.x ?? 0;\n        const posY = clampedPosition?.y ?? 0;\n        const posOrigin = getPositionWithOrigin({\n            x: posX,\n            y: posY,\n            width: node.width ?? 0,\n            height: node.height ?? 0,\n            origin: props.nodeOrigin,\n        });\n        return (React.createElement(NodeComponent, { key: node.id, id: node.id, className: node.className, style: node.style, type: nodeType, data: node.data, sourcePosition: node.sourcePosition || Position.Bottom, targetPosition: node.targetPosition || Position.Top, hidden: node.hidden, xPos: posX, yPos: posY, xPosOrigin: posOrigin.x, yPosOrigin: posOrigin.y, selectNodesOnDrag: props.selectNodesOnDrag, onClick: props.onNodeClick, onMouseEnter: props.onNodeMouseEnter, onMouseMove: props.onNodeMouseMove, onMouseLeave: props.onNodeMouseLeave, onContextMenu: props.onNodeContextMenu, onDoubleClick: props.onNodeDoubleClick, selected: !!node.selected, isDraggable: isDraggable, isSelectable: isSelectable, isConnectable: isConnectable, isFocusable: isFocusable, resizeObserver: resizeObserver, dragHandle: node.dragHandle, zIndex: node[internalsSymbol]?.z ?? 0, isParent: !!node[internalsSymbol]?.isParent, noDragClassName: props.noDragClassName, noPanClassName: props.noPanClassName, initialized: !!node.width && !!node.height, rfId: props.rfId, disableKeyboardA11y: props.disableKeyboardA11y, ariaLabel: node.ariaLabel, hasHandleBounds: !!node[internalsSymbol]?.handleBounds }));\n    })));\n};\nNodeRenderer.displayName = 'NodeRenderer';\nvar NodeRenderer$1 = memo(NodeRenderer);\n\nconst shiftX = (x, shift, position) => {\n    if (position === Position.Left)\n        return x - shift;\n    if (position === Position.Right)\n        return x + shift;\n    return x;\n};\nconst shiftY = (y, shift, position) => {\n    if (position === Position.Top)\n        return y - shift;\n    if (position === Position.Bottom)\n        return y + shift;\n    return y;\n};\nconst EdgeUpdaterClassName = 'react-flow__edgeupdater';\nconst EdgeAnchor = ({ position, centerX, centerY, radius = 10, onMouseDown, onMouseEnter, onMouseOut, type, }) => (React.createElement(\"circle\", { onMouseDown: onMouseDown, onMouseEnter: onMouseEnter, onMouseOut: onMouseOut, className: cc([EdgeUpdaterClassName, `${EdgeUpdaterClassName}-${type}`]), cx: shiftX(centerX, radius, position), cy: shiftY(centerY, radius, position), r: radius, stroke: \"transparent\", fill: \"transparent\" }));\n\nconst alwaysValidConnection = () => true;\nvar wrapEdge = (EdgeComponent) => {\n    const EdgeWrapper = ({ id, className, type, data, onClick, onEdgeDoubleClick, selected, animated, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, source, target, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, elementsSelectable, hidden, sourceHandleId, targetHandleId, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, markerEnd, markerStart, rfId, ariaLabel, isFocusable, isReconnectable, pathOptions, interactionWidth, disableKeyboardA11y, }) => {\n        const edgeRef = useRef(null);\n        const [updateHover, setUpdateHover] = useState(false);\n        const [updating, setUpdating] = useState(false);\n        const store = useStoreApi();\n        const markerStartUrl = useMemo(() => `url('#${getMarkerId(markerStart, rfId)}')`, [markerStart, rfId]);\n        const markerEndUrl = useMemo(() => `url('#${getMarkerId(markerEnd, rfId)}')`, [markerEnd, rfId]);\n        if (hidden) {\n            return null;\n        }\n        const onEdgeClick = (event) => {\n            const { edges, addSelectedEdges, unselectNodesAndEdges, multiSelectionActive } = store.getState();\n            const edge = edges.find((e) => e.id === id);\n            if (!edge) {\n                return;\n            }\n            if (elementsSelectable) {\n                store.setState({ nodesSelectionActive: false });\n                if (edge.selected && multiSelectionActive) {\n                    unselectNodesAndEdges({ nodes: [], edges: [edge] });\n                    edgeRef.current?.blur();\n                }\n                else {\n                    addSelectedEdges([id]);\n                }\n            }\n            if (onClick) {\n                onClick(event, edge);\n            }\n        };\n        const onEdgeDoubleClickHandler = getMouseHandler$1(id, store.getState, onEdgeDoubleClick);\n        const onEdgeContextMenu = getMouseHandler$1(id, store.getState, onContextMenu);\n        const onEdgeMouseEnter = getMouseHandler$1(id, store.getState, onMouseEnter);\n        const onEdgeMouseMove = getMouseHandler$1(id, store.getState, onMouseMove);\n        const onEdgeMouseLeave = getMouseHandler$1(id, store.getState, onMouseLeave);\n        const handleEdgeUpdater = (event, isSourceHandle) => {\n            // avoid triggering edge updater if mouse btn is not left\n            if (event.button !== 0) {\n                return;\n            }\n            const { edges, isValidConnection: isValidConnectionStore } = store.getState();\n            const nodeId = isSourceHandle ? target : source;\n            const handleId = (isSourceHandle ? targetHandleId : sourceHandleId) || null;\n            const handleType = isSourceHandle ? 'target' : 'source';\n            const isValidConnection = isValidConnectionStore || alwaysValidConnection;\n            const isTarget = isSourceHandle;\n            const edge = edges.find((e) => e.id === id);\n            setUpdating(true);\n            onReconnectStart?.(event, edge, handleType);\n            const _onReconnectEnd = (evt) => {\n                setUpdating(false);\n                onReconnectEnd?.(evt, edge, handleType);\n            };\n            const onConnectEdge = (connection) => onReconnect?.(edge, connection);\n            handlePointerDown({\n                event,\n                handleId,\n                nodeId,\n                onConnect: onConnectEdge,\n                isTarget,\n                getState: store.getState,\n                setState: store.setState,\n                isValidConnection,\n                edgeUpdaterType: handleType,\n                onReconnectEnd: _onReconnectEnd,\n            });\n        };\n        const onEdgeUpdaterSourceMouseDown = (event) => handleEdgeUpdater(event, true);\n        const onEdgeUpdaterTargetMouseDown = (event) => handleEdgeUpdater(event, false);\n        const onEdgeUpdaterMouseEnter = () => setUpdateHover(true);\n        const onEdgeUpdaterMouseOut = () => setUpdateHover(false);\n        const inactive = !elementsSelectable && !onClick;\n        const onKeyDown = (event) => {\n            if (!disableKeyboardA11y && elementSelectionKeys.includes(event.key) && elementsSelectable) {\n                const { unselectNodesAndEdges, addSelectedEdges, edges } = store.getState();\n                const unselect = event.key === 'Escape';\n                if (unselect) {\n                    edgeRef.current?.blur();\n                    unselectNodesAndEdges({ edges: [edges.find((e) => e.id === id)] });\n                }\n                else {\n                    addSelectedEdges([id]);\n                }\n            }\n        };\n        return (React.createElement(\"g\", { className: cc([\n                'react-flow__edge',\n                `react-flow__edge-${type}`,\n                className,\n                { selected, animated, inactive, updating: updateHover },\n            ]), onClick: onEdgeClick, onDoubleClick: onEdgeDoubleClickHandler, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: isFocusable ? 'button' : 'img', \"data-testid\": `rf__edge-${id}`, \"aria-label\": ariaLabel === null ? undefined : ariaLabel ? ariaLabel : `Edge from ${source} to ${target}`, \"aria-describedby\": isFocusable ? `${ARIA_EDGE_DESC_KEY}-${rfId}` : undefined, ref: edgeRef },\n            !updating && (React.createElement(EdgeComponent, { id: id, source: source, target: target, selected: selected, animated: animated, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, data: data, style: style, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, sourceHandleId: sourceHandleId, targetHandleId: targetHandleId, markerStart: markerStartUrl, markerEnd: markerEndUrl, pathOptions: pathOptions, interactionWidth: interactionWidth })),\n            isReconnectable && (React.createElement(React.Fragment, null,\n                (isReconnectable === 'source' || isReconnectable === true) && (React.createElement(EdgeAnchor, { position: sourcePosition, centerX: sourceX, centerY: sourceY, radius: reconnectRadius, onMouseDown: onEdgeUpdaterSourceMouseDown, onMouseEnter: onEdgeUpdaterMouseEnter, onMouseOut: onEdgeUpdaterMouseOut, type: \"source\" })),\n                (isReconnectable === 'target' || isReconnectable === true) && (React.createElement(EdgeAnchor, { position: targetPosition, centerX: targetX, centerY: targetY, radius: reconnectRadius, onMouseDown: onEdgeUpdaterTargetMouseDown, onMouseEnter: onEdgeUpdaterMouseEnter, onMouseOut: onEdgeUpdaterMouseOut, type: \"target\" }))))));\n    };\n    EdgeWrapper.displayName = 'EdgeWrapper';\n    return memo(EdgeWrapper);\n};\n\nfunction createEdgeTypes(edgeTypes) {\n    const standardTypes = {\n        default: wrapEdge((edgeTypes.default || BezierEdge)),\n        straight: wrapEdge((edgeTypes.bezier || StraightEdge)),\n        step: wrapEdge((edgeTypes.step || StepEdge)),\n        smoothstep: wrapEdge((edgeTypes.step || SmoothStepEdge)),\n        simplebezier: wrapEdge((edgeTypes.simplebezier || SimpleBezierEdge)),\n    };\n    const wrappedTypes = {};\n    const specialTypes = Object.keys(edgeTypes)\n        .filter((k) => !['default', 'bezier'].includes(k))\n        .reduce((res, key) => {\n        res[key] = wrapEdge((edgeTypes[key] || BezierEdge));\n        return res;\n    }, wrappedTypes);\n    return {\n        ...standardTypes,\n        ...specialTypes,\n    };\n}\nfunction getHandlePosition(position, nodeRect, handle = null) {\n    const x = (handle?.x || 0) + nodeRect.x;\n    const y = (handle?.y || 0) + nodeRect.y;\n    const width = handle?.width || nodeRect.width;\n    const height = handle?.height || nodeRect.height;\n    switch (position) {\n        case Position.Top:\n            return {\n                x: x + width / 2,\n                y,\n            };\n        case Position.Right:\n            return {\n                x: x + width,\n                y: y + height / 2,\n            };\n        case Position.Bottom:\n            return {\n                x: x + width / 2,\n                y: y + height,\n            };\n        case Position.Left:\n            return {\n                x,\n                y: y + height / 2,\n            };\n    }\n}\nfunction getHandle(bounds, handleId) {\n    if (!bounds) {\n        return null;\n    }\n    if (bounds.length === 1 || !handleId) {\n        return bounds[0];\n    }\n    else if (handleId) {\n        return bounds.find((d) => d.id === handleId) || null;\n    }\n    return null;\n}\nconst getEdgePositions = (sourceNodeRect, sourceHandle, sourcePosition, targetNodeRect, targetHandle, targetPosition) => {\n    const sourceHandlePos = getHandlePosition(sourcePosition, sourceNodeRect, sourceHandle);\n    const targetHandlePos = getHandlePosition(targetPosition, targetNodeRect, targetHandle);\n    return {\n        sourceX: sourceHandlePos.x,\n        sourceY: sourceHandlePos.y,\n        targetX: targetHandlePos.x,\n        targetY: targetHandlePos.y,\n    };\n};\nfunction isEdgeVisible({ sourcePos, targetPos, sourceWidth, sourceHeight, targetWidth, targetHeight, width, height, transform, }) {\n    const edgeBox = {\n        x: Math.min(sourcePos.x, targetPos.x),\n        y: Math.min(sourcePos.y, targetPos.y),\n        x2: Math.max(sourcePos.x + sourceWidth, targetPos.x + targetWidth),\n        y2: Math.max(sourcePos.y + sourceHeight, targetPos.y + targetHeight),\n    };\n    if (edgeBox.x === edgeBox.x2) {\n        edgeBox.x2 += 1;\n    }\n    if (edgeBox.y === edgeBox.y2) {\n        edgeBox.y2 += 1;\n    }\n    const viewBox = rectToBox({\n        x: (0 - transform[0]) / transform[2],\n        y: (0 - transform[1]) / transform[2],\n        width: width / transform[2],\n        height: height / transform[2],\n    });\n    const xOverlap = Math.max(0, Math.min(viewBox.x2, edgeBox.x2) - Math.max(viewBox.x, edgeBox.x));\n    const yOverlap = Math.max(0, Math.min(viewBox.y2, edgeBox.y2) - Math.max(viewBox.y, edgeBox.y));\n    const overlappingArea = Math.ceil(xOverlap * yOverlap);\n    return overlappingArea > 0;\n}\nfunction getNodeData(node) {\n    const handleBounds = node?.[internalsSymbol]?.handleBounds || null;\n    const isValid = handleBounds &&\n        node?.width &&\n        node?.height &&\n        typeof node?.positionAbsolute?.x !== 'undefined' &&\n        typeof node?.positionAbsolute?.y !== 'undefined';\n    return [\n        {\n            x: node?.positionAbsolute?.x || 0,\n            y: node?.positionAbsolute?.y || 0,\n            width: node?.width || 0,\n            height: node?.height || 0,\n        },\n        handleBounds,\n        !!isValid,\n    ];\n}\n\nconst defaultEdgeTree = [{ level: 0, isMaxLevel: true, edges: [] }];\nfunction groupEdgesByZLevel(edges, nodeInternals, elevateEdgesOnSelect = false) {\n    let maxLevel = -1;\n    const levelLookup = edges.reduce((tree, edge) => {\n        const hasZIndex = isNumeric(edge.zIndex);\n        let z = hasZIndex ? edge.zIndex : 0;\n        if (elevateEdgesOnSelect) {\n            const targetNode = nodeInternals.get(edge.target);\n            const sourceNode = nodeInternals.get(edge.source);\n            const edgeOrConnectedNodeSelected = edge.selected || targetNode?.selected || sourceNode?.selected;\n            const selectedZIndex = Math.max(sourceNode?.[internalsSymbol]?.z || 0, targetNode?.[internalsSymbol]?.z || 0, 1000);\n            z = (hasZIndex ? edge.zIndex : 0) + (edgeOrConnectedNodeSelected ? selectedZIndex : 0);\n        }\n        if (tree[z]) {\n            tree[z].push(edge);\n        }\n        else {\n            tree[z] = [edge];\n        }\n        maxLevel = z > maxLevel ? z : maxLevel;\n        return tree;\n    }, {});\n    const edgeTree = Object.entries(levelLookup).map(([key, edges]) => {\n        const level = +key;\n        return {\n            edges,\n            level,\n            isMaxLevel: level === maxLevel,\n        };\n    });\n    if (edgeTree.length === 0) {\n        return defaultEdgeTree;\n    }\n    return edgeTree;\n}\nfunction useVisibleEdges(onlyRenderVisible, nodeInternals, elevateEdgesOnSelect) {\n    const edges = useStore(useCallback((s) => {\n        if (!onlyRenderVisible) {\n            return s.edges;\n        }\n        return s.edges.filter((e) => {\n            const sourceNode = nodeInternals.get(e.source);\n            const targetNode = nodeInternals.get(e.target);\n            return (sourceNode?.width &&\n                sourceNode?.height &&\n                targetNode?.width &&\n                targetNode?.height &&\n                isEdgeVisible({\n                    sourcePos: sourceNode.positionAbsolute || { x: 0, y: 0 },\n                    targetPos: targetNode.positionAbsolute || { x: 0, y: 0 },\n                    sourceWidth: sourceNode.width,\n                    sourceHeight: sourceNode.height,\n                    targetWidth: targetNode.width,\n                    targetHeight: targetNode.height,\n                    width: s.width,\n                    height: s.height,\n                    transform: s.transform,\n                }));\n        });\n    }, [onlyRenderVisible, nodeInternals]));\n    return groupEdgesByZLevel(edges, nodeInternals, elevateEdgesOnSelect);\n}\n\nconst ArrowSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (React.createElement(\"polyline\", { style: {\n            stroke: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", fill: \"none\", points: \"-5,-4 0,0 -5,4\" }));\n};\nconst ArrowClosedSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (React.createElement(\"polyline\", { style: {\n            stroke: color,\n            fill: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", points: \"-5,-4 0,0 -5,4 -5,-4\" }));\n};\nconst MarkerSymbols = {\n    [MarkerType.Arrow]: ArrowSymbol,\n    [MarkerType.ArrowClosed]: ArrowClosedSymbol,\n};\nfunction useMarkerSymbol(type) {\n    const store = useStoreApi();\n    const symbol = useMemo(() => {\n        const symbolExists = Object.prototype.hasOwnProperty.call(MarkerSymbols, type);\n        if (!symbolExists) {\n            store.getState().onError?.('009', errorMessages['error009'](type));\n            return null;\n        }\n        return MarkerSymbols[type];\n    }, [type]);\n    return symbol;\n}\n\nconst Marker = ({ id, type, color, width = 12.5, height = 12.5, markerUnits = 'strokeWidth', strokeWidth, orient = 'auto-start-reverse', }) => {\n    const Symbol = useMarkerSymbol(type);\n    if (!Symbol) {\n        return null;\n    }\n    return (React.createElement(\"marker\", { className: \"react-flow__arrowhead\", id: id, markerWidth: `${width}`, markerHeight: `${height}`, viewBox: \"-10 -10 20 20\", markerUnits: markerUnits, orient: orient, refX: \"0\", refY: \"0\" },\n        React.createElement(Symbol, { color: color, strokeWidth: strokeWidth })));\n};\nconst markerSelector = ({ defaultColor, rfId }) => (s) => {\n    const ids = [];\n    return s.edges\n        .reduce((markers, edge) => {\n        [edge.markerStart, edge.markerEnd].forEach((marker) => {\n            if (marker && typeof marker === 'object') {\n                const markerId = getMarkerId(marker, rfId);\n                if (!ids.includes(markerId)) {\n                    markers.push({ id: markerId, color: marker.color || defaultColor, ...marker });\n                    ids.push(markerId);\n                }\n            }\n        });\n        return markers;\n    }, [])\n        .sort((a, b) => a.id.localeCompare(b.id));\n};\n// when you have multiple flows on a page and you hide the first one, the other ones have no markers anymore\n// when they do have markers with the same ids. To prevent this the user can pass a unique id to the react flow wrapper\n// that we can then use for creating our unique marker ids\nconst MarkerDefinitions = ({ defaultColor, rfId }) => {\n    const markers = useStore(useCallback(markerSelector({ defaultColor, rfId }), [defaultColor, rfId]), \n    // the id includes all marker options, so we just need to look at that part of the marker\n    (a, b) => !(a.length !== b.length || a.some((m, i) => m.id !== b[i].id)));\n    return (React.createElement(\"defs\", null, markers.map((marker) => (React.createElement(Marker, { id: marker.id, key: marker.id, type: marker.type, color: marker.color, width: marker.width, height: marker.height, markerUnits: marker.markerUnits, strokeWidth: marker.strokeWidth, orient: marker.orient })))));\n};\nMarkerDefinitions.displayName = 'MarkerDefinitions';\nvar MarkerDefinitions$1 = memo(MarkerDefinitions);\n\nconst selector$4 = (s) => ({\n    nodesConnectable: s.nodesConnectable,\n    edgesFocusable: s.edgesFocusable,\n    edgesUpdatable: s.edgesUpdatable,\n    elementsSelectable: s.elementsSelectable,\n    width: s.width,\n    height: s.height,\n    connectionMode: s.connectionMode,\n    nodeInternals: s.nodeInternals,\n    onError: s.onError,\n});\nconst EdgeRenderer = ({ defaultMarkerColor, onlyRenderVisibleElements, elevateEdgesOnSelect, rfId, edgeTypes, noPanClassName, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeClick, onEdgeDoubleClick, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius, children, disableKeyboardA11y, }) => {\n    const { edgesFocusable, edgesUpdatable, elementsSelectable, width, height, connectionMode, nodeInternals, onError } = useStore(selector$4, shallow);\n    const edgeTree = useVisibleEdges(onlyRenderVisibleElements, nodeInternals, elevateEdgesOnSelect);\n    if (!width) {\n        return null;\n    }\n    return (React.createElement(React.Fragment, null,\n        edgeTree.map(({ level, edges, isMaxLevel }) => (React.createElement(\"svg\", { key: level, style: { zIndex: level }, width: width, height: height, className: \"react-flow__edges react-flow__container\" },\n            isMaxLevel && React.createElement(MarkerDefinitions$1, { defaultColor: defaultMarkerColor, rfId: rfId }),\n            React.createElement(\"g\", null, edges.map((edge) => {\n                const [sourceNodeRect, sourceHandleBounds, sourceIsValid] = getNodeData(nodeInternals.get(edge.source));\n                const [targetNodeRect, targetHandleBounds, targetIsValid] = getNodeData(nodeInternals.get(edge.target));\n                if (!sourceIsValid || !targetIsValid) {\n                    return null;\n                }\n                let edgeType = edge.type || 'default';\n                if (!edgeTypes[edgeType]) {\n                    onError?.('011', errorMessages['error011'](edgeType));\n                    edgeType = 'default';\n                }\n                const EdgeComponent = edgeTypes[edgeType] || edgeTypes.default;\n                // when connection type is loose we can define all handles as sources and connect source -> source\n                const targetNodeHandles = connectionMode === ConnectionMode.Strict\n                    ? targetHandleBounds.target\n                    : (targetHandleBounds.target ?? []).concat(targetHandleBounds.source ?? []);\n                const sourceHandle = getHandle(sourceHandleBounds.source, edge.sourceHandle);\n                const targetHandle = getHandle(targetNodeHandles, edge.targetHandle);\n                const sourcePosition = sourceHandle?.position || Position.Bottom;\n                const targetPosition = targetHandle?.position || Position.Top;\n                const isFocusable = !!(edge.focusable || (edgesFocusable && typeof edge.focusable === 'undefined'));\n                const edgeReconnectable = edge.reconnectable || edge.updatable;\n                const isReconnectable = typeof onReconnect !== 'undefined' &&\n                    (edgeReconnectable || (edgesUpdatable && typeof edgeReconnectable === 'undefined'));\n                if (!sourceHandle || !targetHandle) {\n                    onError?.('008', errorMessages['error008'](sourceHandle, edge));\n                    return null;\n                }\n                const { sourceX, sourceY, targetX, targetY } = getEdgePositions(sourceNodeRect, sourceHandle, sourcePosition, targetNodeRect, targetHandle, targetPosition);\n                return (React.createElement(EdgeComponent, { key: edge.id, id: edge.id, className: cc([edge.className, noPanClassName]), type: edgeType, data: edge.data, selected: !!edge.selected, animated: !!edge.animated, hidden: !!edge.hidden, label: edge.label, labelStyle: edge.labelStyle, labelShowBg: edge.labelShowBg, labelBgStyle: edge.labelBgStyle, labelBgPadding: edge.labelBgPadding, labelBgBorderRadius: edge.labelBgBorderRadius, style: edge.style, source: edge.source, target: edge.target, sourceHandleId: edge.sourceHandle, targetHandleId: edge.targetHandle, markerEnd: edge.markerEnd, markerStart: edge.markerStart, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, elementsSelectable: elementsSelectable, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, reconnectRadius: reconnectRadius, rfId: rfId, ariaLabel: edge.ariaLabel, isFocusable: isFocusable, isReconnectable: isReconnectable, pathOptions: 'pathOptions' in edge ? edge.pathOptions : undefined, interactionWidth: edge.interactionWidth, disableKeyboardA11y: disableKeyboardA11y }));\n            }))))),\n        children));\n};\nEdgeRenderer.displayName = 'EdgeRenderer';\nvar EdgeRenderer$1 = memo(EdgeRenderer);\n\nconst selector$3 = (s) => `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`;\nfunction Viewport({ children }) {\n    const transform = useStore(selector$3);\n    return (React.createElement(\"div\", { className: \"react-flow__viewport react-flow__container\", style: { transform } }, children));\n}\n\nfunction useOnInitHandler(onInit) {\n    const rfInstance = useReactFlow();\n    const isInitialized = useRef(false);\n    useEffect(() => {\n        if (!isInitialized.current && rfInstance.viewportInitialized && onInit) {\n            setTimeout(() => onInit(rfInstance), 1);\n            isInitialized.current = true;\n        }\n    }, [onInit, rfInstance.viewportInitialized]);\n}\n\nconst oppositePosition = {\n    [Position.Left]: Position.Right,\n    [Position.Right]: Position.Left,\n    [Position.Top]: Position.Bottom,\n    [Position.Bottom]: Position.Top,\n};\nconst ConnectionLine = ({ nodeId, handleType, style, type = ConnectionLineType.Bezier, CustomComponent, connectionStatus, }) => {\n    const { fromNode, handleId, toX, toY, connectionMode } = useStore(useCallback((s) => ({\n        fromNode: s.nodeInternals.get(nodeId),\n        handleId: s.connectionHandleId,\n        toX: (s.connectionPosition.x - s.transform[0]) / s.transform[2],\n        toY: (s.connectionPosition.y - s.transform[1]) / s.transform[2],\n        connectionMode: s.connectionMode,\n    }), [nodeId]), shallow);\n    const fromHandleBounds = fromNode?.[internalsSymbol]?.handleBounds;\n    let handleBounds = fromHandleBounds?.[handleType];\n    if (connectionMode === ConnectionMode.Loose) {\n        handleBounds = handleBounds ? handleBounds : fromHandleBounds?.[handleType === 'source' ? 'target' : 'source'];\n    }\n    if (!fromNode || !handleBounds) {\n        return null;\n    }\n    const fromHandle = handleId ? handleBounds.find((d) => d.id === handleId) : handleBounds[0];\n    const fromHandleX = fromHandle ? fromHandle.x + fromHandle.width / 2 : (fromNode.width ?? 0) / 2;\n    const fromHandleY = fromHandle ? fromHandle.y + fromHandle.height / 2 : fromNode.height ?? 0;\n    const fromX = (fromNode.positionAbsolute?.x ?? 0) + fromHandleX;\n    const fromY = (fromNode.positionAbsolute?.y ?? 0) + fromHandleY;\n    const fromPosition = fromHandle?.position;\n    const toPosition = fromPosition ? oppositePosition[fromPosition] : null;\n    if (!fromPosition || !toPosition) {\n        return null;\n    }\n    if (CustomComponent) {\n        return (React.createElement(CustomComponent, { connectionLineType: type, connectionLineStyle: style, fromNode: fromNode, fromHandle: fromHandle, fromX: fromX, fromY: fromY, toX: toX, toY: toY, fromPosition: fromPosition, toPosition: toPosition, connectionStatus: connectionStatus }));\n    }\n    let dAttr = '';\n    const pathParams = {\n        sourceX: fromX,\n        sourceY: fromY,\n        sourcePosition: fromPosition,\n        targetX: toX,\n        targetY: toY,\n        targetPosition: toPosition,\n    };\n    if (type === ConnectionLineType.Bezier) {\n        // we assume the destination position is opposite to the source position\n        [dAttr] = getBezierPath(pathParams);\n    }\n    else if (type === ConnectionLineType.Step) {\n        [dAttr] = getSmoothStepPath({\n            ...pathParams,\n            borderRadius: 0,\n        });\n    }\n    else if (type === ConnectionLineType.SmoothStep) {\n        [dAttr] = getSmoothStepPath(pathParams);\n    }\n    else if (type === ConnectionLineType.SimpleBezier) {\n        [dAttr] = getSimpleBezierPath(pathParams);\n    }\n    else {\n        dAttr = `M${fromX},${fromY} ${toX},${toY}`;\n    }\n    return React.createElement(\"path\", { d: dAttr, fill: \"none\", className: \"react-flow__connection-path\", style: style });\n};\nConnectionLine.displayName = 'ConnectionLine';\nconst selector$2 = (s) => ({\n    nodeId: s.connectionNodeId,\n    handleType: s.connectionHandleType,\n    nodesConnectable: s.nodesConnectable,\n    connectionStatus: s.connectionStatus,\n    width: s.width,\n    height: s.height,\n});\nfunction ConnectionLineWrapper({ containerStyle, style, type, component }) {\n    const { nodeId, handleType, nodesConnectable, width, height, connectionStatus } = useStore(selector$2, shallow);\n    const isValid = !!(nodeId && handleType && width && nodesConnectable);\n    if (!isValid) {\n        return null;\n    }\n    return (React.createElement(\"svg\", { style: containerStyle, width: width, height: height, className: \"react-flow__edges react-flow__connectionline react-flow__container\" },\n        React.createElement(\"g\", { className: cc(['react-flow__connection', connectionStatus]) },\n            React.createElement(ConnectionLine, { nodeId: nodeId, handleType: handleType, style: style, type: type, CustomComponent: component, connectionStatus: connectionStatus }))));\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodeOrEdgeTypes(nodeOrEdgeTypes, createTypes) {\n    const typesKeysRef = useRef(null);\n    const store = useStoreApi();\n    const typesParsed = useMemo(() => {\n        if (process.env.NODE_ENV === 'development') {\n            const typeKeys = Object.keys(nodeOrEdgeTypes);\n            if (shallow(typesKeysRef.current, typeKeys)) {\n                store.getState().onError?.('002', errorMessages['error002']());\n            }\n            typesKeysRef.current = typeKeys;\n        }\n        return createTypes(nodeOrEdgeTypes);\n    }, [nodeOrEdgeTypes]);\n    return typesParsed;\n}\n\nconst GraphView = ({ nodeTypes, edgeTypes, onMove, onMoveStart, onMoveEnd, onInit, onNodeClick, onEdgeClick, onNodeDoubleClick, onEdgeDoubleClick, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionLineType, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, selectionKeyCode, selectionOnDrag, selectionMode, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, deleteKeyCode, onlyRenderVisibleElements, elementsSelectable, selectNodesOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, defaultMarkerColor, zoomOnScroll, zoomOnPinch, panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius, noDragClassName, noWheelClassName, noPanClassName, elevateEdgesOnSelect, disableKeyboardA11y, nodeOrigin, nodeExtent, rfId, }) => {\n    const nodeTypesWrapped = useNodeOrEdgeTypes(nodeTypes, createNodeTypes);\n    const edgeTypesWrapped = useNodeOrEdgeTypes(edgeTypes, createEdgeTypes);\n    useOnInitHandler(onInit);\n    return (React.createElement(FlowRenderer$1, { onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, deleteKeyCode: deleteKeyCode, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, elementsSelectable: elementsSelectable, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, onSelectionContextMenu: onSelectionContextMenu, preventScrolling: preventScrolling, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y },\n        React.createElement(Viewport, null,\n            React.createElement(EdgeRenderer$1, { edgeTypes: edgeTypesWrapped, onEdgeClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onlyRenderVisibleElements: onlyRenderVisibleElements, onEdgeContextMenu: onEdgeContextMenu, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noPanClassName: noPanClassName, elevateEdgesOnSelect: !!elevateEdgesOnSelect, disableKeyboardA11y: disableKeyboardA11y, rfId: rfId },\n                React.createElement(ConnectionLineWrapper, { style: connectionLineStyle, type: connectionLineType, component: connectionLineComponent, containerStyle: connectionLineContainerStyle })),\n            React.createElement(\"div\", { className: \"react-flow__edgelabel-renderer\" }),\n            React.createElement(NodeRenderer$1, { nodeTypes: nodeTypesWrapped, onNodeClick: onNodeClick, onNodeDoubleClick: onNodeDoubleClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, selectNodesOnDrag: selectNodesOnDrag, onlyRenderVisibleElements: onlyRenderVisibleElements, noPanClassName: noPanClassName, noDragClassName: noDragClassName, disableKeyboardA11y: disableKeyboardA11y, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, rfId: rfId }))));\n};\nGraphView.displayName = 'GraphView';\nvar GraphView$1 = memo(GraphView);\n\nconst infiniteExtent = [\n    [Number.NEGATIVE_INFINITY, Number.NEGATIVE_INFINITY],\n    [Number.POSITIVE_INFINITY, Number.POSITIVE_INFINITY],\n];\nconst initialState = {\n    rfId: '1',\n    width: 0,\n    height: 0,\n    transform: [0, 0, 1],\n    nodeInternals: new Map(),\n    edges: [],\n    onNodesChange: null,\n    onEdgesChange: null,\n    hasDefaultNodes: false,\n    hasDefaultEdges: false,\n    d3Zoom: null,\n    d3Selection: null,\n    d3ZoomHandler: undefined,\n    minZoom: 0.5,\n    maxZoom: 2,\n    translateExtent: infiniteExtent,\n    nodeExtent: infiniteExtent,\n    nodesSelectionActive: false,\n    userSelectionActive: false,\n    userSelectionRect: null,\n    connectionNodeId: null,\n    connectionHandleId: null,\n    connectionHandleType: 'source',\n    connectionPosition: { x: 0, y: 0 },\n    connectionStatus: null,\n    connectionMode: ConnectionMode.Strict,\n    domNode: null,\n    paneDragging: false,\n    noPanClassName: 'nopan',\n    nodeOrigin: [0, 0],\n    nodeDragThreshold: 0,\n    snapGrid: [15, 15],\n    snapToGrid: false,\n    nodesDraggable: true,\n    nodesConnectable: true,\n    nodesFocusable: true,\n    edgesFocusable: true,\n    edgesUpdatable: true,\n    elementsSelectable: true,\n    elevateNodesOnSelect: true,\n    fitViewOnInit: false,\n    fitViewOnInitDone: false,\n    fitViewOnInitOptions: undefined,\n    onSelectionChange: [],\n    multiSelectionActive: false,\n    connectionStartHandle: null,\n    connectionEndHandle: null,\n    connectionClickStartHandle: null,\n    connectOnClick: true,\n    ariaLiveMessage: '',\n    autoPanOnConnect: true,\n    autoPanOnNodeDrag: true,\n    connectionRadius: 20,\n    onError: devWarn,\n    isValidConnection: undefined,\n};\n\nconst createRFStore = () => createWithEqualityFn((set, get) => ({\n    ...initialState,\n    setNodes: (nodes) => {\n        const { nodeInternals, nodeOrigin, elevateNodesOnSelect } = get();\n        set({ nodeInternals: createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect) });\n    },\n    getNodes: () => {\n        return Array.from(get().nodeInternals.values());\n    },\n    setEdges: (edges) => {\n        const { defaultEdgeOptions = {} } = get();\n        set({ edges: edges.map((e) => ({ ...defaultEdgeOptions, ...e })) });\n    },\n    setDefaultNodesAndEdges: (nodes, edges) => {\n        const hasDefaultNodes = typeof nodes !== 'undefined';\n        const hasDefaultEdges = typeof edges !== 'undefined';\n        const nodeInternals = hasDefaultNodes\n            ? createNodeInternals(nodes, new Map(), get().nodeOrigin, get().elevateNodesOnSelect)\n            : new Map();\n        const nextEdges = hasDefaultEdges ? edges : [];\n        set({ nodeInternals, edges: nextEdges, hasDefaultNodes, hasDefaultEdges });\n    },\n    updateNodeDimensions: (updates) => {\n        const { onNodesChange, nodeInternals, fitViewOnInit, fitViewOnInitDone, fitViewOnInitOptions, domNode, nodeOrigin, } = get();\n        const viewportNode = domNode?.querySelector('.react-flow__viewport');\n        if (!viewportNode) {\n            return;\n        }\n        const style = window.getComputedStyle(viewportNode);\n        const { m22: zoom } = new window.DOMMatrixReadOnly(style.transform);\n        const changes = updates.reduce((res, update) => {\n            const node = nodeInternals.get(update.id);\n            if (node?.hidden) {\n                nodeInternals.set(node.id, {\n                    ...node,\n                    [internalsSymbol]: {\n                        ...node[internalsSymbol],\n                        // we need to reset the handle bounds when the node is hidden\n                        // in order to force a new observation when the node is shown again\n                        handleBounds: undefined,\n                    },\n                });\n            }\n            else if (node) {\n                const dimensions = getDimensions(update.nodeElement);\n                const doUpdate = !!(dimensions.width &&\n                    dimensions.height &&\n                    (node.width !== dimensions.width || node.height !== dimensions.height || update.forceUpdate));\n                if (doUpdate) {\n                    nodeInternals.set(node.id, {\n                        ...node,\n                        [internalsSymbol]: {\n                            ...node[internalsSymbol],\n                            handleBounds: {\n                                source: getHandleBounds('.source', update.nodeElement, zoom, nodeOrigin),\n                                target: getHandleBounds('.target', update.nodeElement, zoom, nodeOrigin),\n                            },\n                        },\n                        ...dimensions,\n                    });\n                    res.push({\n                        id: node.id,\n                        type: 'dimensions',\n                        dimensions,\n                    });\n                }\n            }\n            return res;\n        }, []);\n        updateAbsoluteNodePositions(nodeInternals, nodeOrigin);\n        const nextFitViewOnInitDone = fitViewOnInitDone ||\n            (fitViewOnInit && !fitViewOnInitDone && fitView(get, { initial: true, ...fitViewOnInitOptions }));\n        set({ nodeInternals: new Map(nodeInternals), fitViewOnInitDone: nextFitViewOnInitDone });\n        if (changes?.length > 0) {\n            onNodesChange?.(changes);\n        }\n    },\n    updateNodePositions: (nodeDragItems, positionChanged = true, dragging = false) => {\n        const { triggerNodeChanges } = get();\n        const changes = nodeDragItems.map((node) => {\n            const change = {\n                id: node.id,\n                type: 'position',\n                dragging,\n            };\n            if (positionChanged) {\n                change.positionAbsolute = node.positionAbsolute;\n                change.position = node.position;\n            }\n            return change;\n        });\n        triggerNodeChanges(changes);\n    },\n    triggerNodeChanges: (changes) => {\n        const { onNodesChange, nodeInternals, hasDefaultNodes, nodeOrigin, getNodes, elevateNodesOnSelect } = get();\n        if (changes?.length) {\n            if (hasDefaultNodes) {\n                const nodes = applyNodeChanges(changes, getNodes());\n                const nextNodeInternals = createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect);\n                set({ nodeInternals: nextNodeInternals });\n            }\n            onNodesChange?.(changes);\n        }\n    },\n    addSelectedNodes: (selectedNodeIds) => {\n        const { multiSelectionActive, edges, getNodes } = get();\n        let changedNodes;\n        let changedEdges = null;\n        if (multiSelectionActive) {\n            changedNodes = selectedNodeIds.map((nodeId) => createSelectionChange(nodeId, true));\n        }\n        else {\n            changedNodes = getSelectionChanges(getNodes(), selectedNodeIds);\n            changedEdges = getSelectionChanges(edges, []);\n        }\n        updateNodesAndEdgesSelections({\n            changedNodes,\n            changedEdges,\n            get,\n            set,\n        });\n    },\n    addSelectedEdges: (selectedEdgeIds) => {\n        const { multiSelectionActive, edges, getNodes } = get();\n        let changedEdges;\n        let changedNodes = null;\n        if (multiSelectionActive) {\n            changedEdges = selectedEdgeIds.map((edgeId) => createSelectionChange(edgeId, true));\n        }\n        else {\n            changedEdges = getSelectionChanges(edges, selectedEdgeIds);\n            changedNodes = getSelectionChanges(getNodes(), []);\n        }\n        updateNodesAndEdgesSelections({\n            changedNodes,\n            changedEdges,\n            get,\n            set,\n        });\n    },\n    unselectNodesAndEdges: ({ nodes, edges } = {}) => {\n        const { edges: storeEdges, getNodes } = get();\n        const nodesToUnselect = nodes ? nodes : getNodes();\n        const edgesToUnselect = edges ? edges : storeEdges;\n        const changedNodes = nodesToUnselect.map((n) => {\n            n.selected = false;\n            return createSelectionChange(n.id, false);\n        });\n        const changedEdges = edgesToUnselect.map((edge) => createSelectionChange(edge.id, false));\n        updateNodesAndEdgesSelections({\n            changedNodes,\n            changedEdges,\n            get,\n            set,\n        });\n    },\n    setMinZoom: (minZoom) => {\n        const { d3Zoom, maxZoom } = get();\n        d3Zoom?.scaleExtent([minZoom, maxZoom]);\n        set({ minZoom });\n    },\n    setMaxZoom: (maxZoom) => {\n        const { d3Zoom, minZoom } = get();\n        d3Zoom?.scaleExtent([minZoom, maxZoom]);\n        set({ maxZoom });\n    },\n    setTranslateExtent: (translateExtent) => {\n        get().d3Zoom?.translateExtent(translateExtent);\n        set({ translateExtent });\n    },\n    resetSelectedElements: () => {\n        const { edges, getNodes } = get();\n        const nodes = getNodes();\n        const nodesToUnselect = nodes\n            .filter((e) => e.selected)\n            .map((n) => createSelectionChange(n.id, false));\n        const edgesToUnselect = edges\n            .filter((e) => e.selected)\n            .map((e) => createSelectionChange(e.id, false));\n        updateNodesAndEdgesSelections({\n            changedNodes: nodesToUnselect,\n            changedEdges: edgesToUnselect,\n            get,\n            set,\n        });\n    },\n    setNodeExtent: (nodeExtent) => {\n        const { nodeInternals } = get();\n        nodeInternals.forEach((node) => {\n            node.positionAbsolute = clampPosition(node.position, nodeExtent);\n        });\n        set({\n            nodeExtent,\n            nodeInternals: new Map(nodeInternals),\n        });\n    },\n    panBy: (delta) => {\n        const { transform, width, height, d3Zoom, d3Selection, translateExtent } = get();\n        if (!d3Zoom || !d3Selection || (!delta.x && !delta.y)) {\n            return false;\n        }\n        const nextTransform = zoomIdentity\n            .translate(transform[0] + delta.x, transform[1] + delta.y)\n            .scale(transform[2]);\n        const extent = [\n            [0, 0],\n            [width, height],\n        ];\n        const constrainedTransform = d3Zoom?.constrain()(nextTransform, extent, translateExtent);\n        d3Zoom.transform(d3Selection, constrainedTransform);\n        const transformChanged = transform[0] !== constrainedTransform.x ||\n            transform[1] !== constrainedTransform.y ||\n            transform[2] !== constrainedTransform.k;\n        return transformChanged;\n    },\n    cancelConnection: () => set({\n        connectionNodeId: initialState.connectionNodeId,\n        connectionHandleId: initialState.connectionHandleId,\n        connectionHandleType: initialState.connectionHandleType,\n        connectionStatus: initialState.connectionStatus,\n        connectionStartHandle: initialState.connectionStartHandle,\n        connectionEndHandle: initialState.connectionEndHandle,\n    }),\n    reset: () => set({ ...initialState }),\n}), Object.is);\n\nconst ReactFlowProvider = ({ children }) => {\n    const storeRef = useRef(null);\n    if (!storeRef.current) {\n        storeRef.current = createRFStore();\n    }\n    return React.createElement(Provider$1, { value: storeRef.current }, children);\n};\nReactFlowProvider.displayName = 'ReactFlowProvider';\n\nconst Wrapper = ({ children }) => {\n    const isWrapped = useContext(StoreContext);\n    if (isWrapped) {\n        // we need to wrap it with a fragment because it's not allowed for children to be a ReactNode\n        // https://github.com/DefinitelyTyped/DefinitelyTyped/issues/18051\n        return React.createElement(React.Fragment, null, children);\n    }\n    return React.createElement(ReactFlowProvider, null, children);\n};\nWrapper.displayName = 'ReactFlowWrapper';\n\nconst defaultNodeTypes = {\n    input: InputNode$1,\n    default: DefaultNode$1,\n    output: OutputNode$1,\n    group: GroupNode,\n};\nconst defaultEdgeTypes = {\n    default: BezierEdge,\n    straight: StraightEdge,\n    step: StepEdge,\n    smoothstep: SmoothStepEdge,\n    simplebezier: SimpleBezierEdge,\n};\nconst initNodeOrigin = [0, 0];\nconst initSnapGrid = [15, 15];\nconst initDefaultViewport = { x: 0, y: 0, zoom: 1 };\nconst wrapperStyle = {\n    width: '100%',\n    height: '100%',\n    overflow: 'hidden',\n    position: 'relative',\n    zIndex: 0,\n};\nconst ReactFlow = forwardRef(({ nodes, edges, defaultNodes, defaultEdges, className, nodeTypes = defaultNodeTypes, edgeTypes = defaultEdgeTypes, onNodeClick, onEdgeClick, onInit, onMove, onMoveStart, onMoveEnd, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onNodeDoubleClick, onNodeDragStart, onNodeDrag, onNodeDragStop, onNodesDelete, onEdgesDelete, onSelectionChange, onSelectionDragStart, onSelectionDrag, onSelectionDragStop, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionMode = ConnectionMode.Strict, connectionLineType = ConnectionLineType.Bezier, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, deleteKeyCode = 'Backspace', selectionKeyCode = 'Shift', selectionOnDrag = false, selectionMode = SelectionMode.Full, panActivationKeyCode = 'Space', multiSelectionKeyCode = isMacOs() ? 'Meta' : 'Control', zoomActivationKeyCode = isMacOs() ? 'Meta' : 'Control', snapToGrid = false, snapGrid = initSnapGrid, onlyRenderVisibleElements = false, selectNodesOnDrag = true, nodesDraggable, nodesConnectable, nodesFocusable, nodeOrigin = initNodeOrigin, edgesFocusable, edgesUpdatable, elementsSelectable, defaultViewport = initDefaultViewport, minZoom = 0.5, maxZoom = 2, translateExtent = infiniteExtent, preventScrolling = true, nodeExtent, defaultMarkerColor = '#b1b1b7', zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, children, onEdgeContextMenu, onEdgeDoubleClick, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeUpdate, onEdgeUpdateStart, onEdgeUpdateEnd, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius = 10, edgeUpdaterRadius = 10, onNodesChange, onEdgesChange, noDragClassName = 'nodrag', noWheelClassName = 'nowheel', noPanClassName = 'nopan', fitView = false, fitViewOptions, connectOnClick = true, attributionPosition, proOptions, defaultEdgeOptions, elevateNodesOnSelect = true, elevateEdgesOnSelect = false, disableKeyboardA11y = false, autoPanOnConnect = true, autoPanOnNodeDrag = true, connectionRadius = 20, isValidConnection, onError, style, id, nodeDragThreshold, ...rest }, ref) => {\n    const rfId = id || '1';\n    return (React.createElement(\"div\", { ...rest, style: { ...style, ...wrapperStyle }, ref: ref, className: cc(['react-flow', className]), \"data-testid\": \"rf__wrapper\", id: id },\n        React.createElement(Wrapper, null,\n            React.createElement(GraphView$1, { onInit: onInit, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, onNodeClick: onNodeClick, onEdgeClick: onEdgeClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, onNodeDoubleClick: onNodeDoubleClick, nodeTypes: nodeTypes, edgeTypes: edgeTypes, connectionLineType: connectionLineType, connectionLineStyle: connectionLineStyle, connectionLineComponent: connectionLineComponent, connectionLineContainerStyle: connectionLineContainerStyle, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, deleteKeyCode: deleteKeyCode, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, onlyRenderVisibleElements: onlyRenderVisibleElements, selectNodesOnDrag: selectNodesOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, preventScrolling: preventScrolling, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneScroll: onPaneScroll, onPaneContextMenu: onPaneContextMenu, onSelectionContextMenu: onSelectionContextMenu, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onEdgeContextMenu: onEdgeContextMenu, onEdgeDoubleClick: onEdgeDoubleClick, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, onReconnect: onReconnect ?? onEdgeUpdate, onReconnectStart: onReconnectStart ?? onEdgeUpdateStart, onReconnectEnd: onReconnectEnd ?? onEdgeUpdateEnd, reconnectRadius: reconnectRadius ?? edgeUpdaterRadius, defaultMarkerColor: defaultMarkerColor, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, elevateEdgesOnSelect: elevateEdgesOnSelect, rfId: rfId, disableKeyboardA11y: disableKeyboardA11y, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent }),\n            React.createElement(StoreUpdater, { nodes: nodes, edges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, onConnect: onConnect, onConnectStart: onConnectStart, onConnectEnd: onConnectEnd, onClickConnectStart: onClickConnectStart, onClickConnectEnd: onClickConnectEnd, nodesDraggable: nodesDraggable, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, edgesFocusable: edgesFocusable, edgesUpdatable: edgesUpdatable, elementsSelectable: elementsSelectable, elevateNodesOnSelect: elevateNodesOnSelect, minZoom: minZoom, maxZoom: maxZoom, nodeExtent: nodeExtent, onNodesChange: onNodesChange, onEdgesChange: onEdgesChange, snapToGrid: snapToGrid, snapGrid: snapGrid, connectionMode: connectionMode, translateExtent: translateExtent, connectOnClick: connectOnClick, defaultEdgeOptions: defaultEdgeOptions, fitView: fitView, fitViewOptions: fitViewOptions, onNodesDelete: onNodesDelete, onEdgesDelete: onEdgesDelete, onNodeDragStart: onNodeDragStart, onNodeDrag: onNodeDrag, onNodeDragStop: onNodeDragStop, onSelectionDrag: onSelectionDrag, onSelectionDragStart: onSelectionDragStart, onSelectionDragStop: onSelectionDragStop, noPanClassName: noPanClassName, nodeOrigin: nodeOrigin, rfId: rfId, autoPanOnConnect: autoPanOnConnect, autoPanOnNodeDrag: autoPanOnNodeDrag, onError: onError, connectionRadius: connectionRadius, isValidConnection: isValidConnection, nodeDragThreshold: nodeDragThreshold }),\n            React.createElement(Wrapper$1, { onSelectionChange: onSelectionChange }),\n            children,\n            React.createElement(Attribution, { proOptions: proOptions, position: attributionPosition }),\n            React.createElement(A11yDescriptions, { rfId: rfId, disableKeyboardA11y: disableKeyboardA11y }))));\n});\nReactFlow.displayName = 'ReactFlow';\n\nconst selector$1 = (s) => s.domNode?.querySelector('.react-flow__edgelabel-renderer');\nfunction EdgeLabelRenderer({ children }) {\n    const edgeLabelRenderer = useStore(selector$1);\n    if (!edgeLabelRenderer) {\n        return null;\n    }\n    return createPortal(children, edgeLabelRenderer);\n}\n\nfunction useUpdateNodeInternals() {\n    const store = useStoreApi();\n    return useCallback((id) => {\n        const { domNode, updateNodeDimensions } = store.getState();\n        const updateIds = Array.isArray(id) ? id : [id];\n        const updates = updateIds.reduce((res, updateId) => {\n            const nodeElement = domNode?.querySelector(`.react-flow__node[data-id=\"${updateId}\"]`);\n            if (nodeElement) {\n                res.push({ id: updateId, nodeElement, forceUpdate: true });\n            }\n            return res;\n        }, []);\n        requestAnimationFrame(() => updateNodeDimensions(updates));\n    }, []);\n}\n\nconst nodesSelector = (state) => state.getNodes();\nfunction useNodes() {\n    const nodes = useStore(nodesSelector, shallow);\n    return nodes;\n}\n\nconst edgesSelector = (state) => state.edges;\nfunction useEdges() {\n    const edges = useStore(edgesSelector, shallow);\n    return edges;\n}\n\nconst viewportSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n});\nfunction useViewport() {\n    const viewport = useStore(viewportSelector, shallow);\n    return viewport;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction createUseItemsState(applyChanges) {\n    return (initialItems) => {\n        const [items, setItems] = useState(initialItems);\n        const onItemsChange = useCallback((changes) => setItems((items) => applyChanges(changes, items)), []);\n        return [items, setItems, onItemsChange];\n    };\n}\nconst useNodesState = createUseItemsState(applyNodeChanges);\nconst useEdgesState = createUseItemsState(applyEdgeChanges);\n\nfunction useOnViewportChange({ onStart, onChange, onEnd }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        store.setState({ onViewportChangeStart: onStart });\n    }, [onStart]);\n    useEffect(() => {\n        store.setState({ onViewportChange: onChange });\n    }, [onChange]);\n    useEffect(() => {\n        store.setState({ onViewportChangeEnd: onEnd });\n    }, [onEnd]);\n}\n\nfunction useOnSelectionChange({ onChange }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const nextSelectionChangeHandlers = [...store.getState().onSelectionChange, onChange];\n        store.setState({ onSelectionChange: nextSelectionChangeHandlers });\n        return () => {\n            const nextHandlers = store.getState().onSelectionChange.filter((fn) => fn !== onChange);\n            store.setState({ onSelectionChange: nextHandlers });\n        };\n    }, [onChange]);\n}\n\nconst selector = (options) => (s) => {\n    if (s.nodeInternals.size === 0) {\n        return false;\n    }\n    return s\n        .getNodes()\n        .filter((n) => (options.includeHiddenNodes ? true : !n.hidden))\n        .every((n) => n[internalsSymbol]?.handleBounds !== undefined);\n};\nconst defaultOptions = {\n    includeHiddenNodes: false,\n};\nfunction useNodesInitialized(options = defaultOptions) {\n    const initialized = useStore(selector(options));\n    return initialized;\n}\n\nexport { BaseEdge, BezierEdge, ConnectionLineType, ConnectionMode, EdgeLabelRenderer, EdgeText$1 as EdgeText, Handle$1 as Handle, MarkerType, PanOnScrollMode, Panel, Position, ReactFlow, ReactFlowProvider, SelectionMode, SimpleBezierEdge, SmoothStepEdge, StepEdge, StraightEdge, addEdge, applyEdgeChanges, applyNodeChanges, boxToRect, clamp, getBezierPath, getBoundsOfRects, getConnectedEdges, getIncomers, getMarkerEnd, getNodePositionWithOrigin, getNodesBounds, getOutgoers, getRectOfNodes, getSimpleBezierPath, getSmoothStepPath, getStraightPath, getTransformForBounds, getViewportForBounds, handleParentExpand, internalsSymbol, isEdge, isNode, reconnectEdge, rectToBox, updateEdge, useEdges, useEdgesState, useGetPointerPosition, useKeyPress, useNodeId, useNodes, useNodesInitialized, useNodesState, useOnSelectionChange, useOnViewportChange, useReactFlow, useStore, useStoreApi, useUpdateNodeInternals, useViewport };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;;;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACnC,MAAM,aAAa,aAAa,QAAQ;AAExC,MAAM,gBAAgB;IAClB,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,UAAU,CAAC,WAAa,CAAC,WAAW,EAAE,SAAS,2CAA2C,CAAC;IAC3F,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,UAAU,CAAC,KAAO,CAAC,qBAAqB,EAAE,GAAG,gBAAgB,CAAC;IAC9D,UAAU,CAAC,OAAS,CAAC,aAAa,EAAE,KAAK,gBAAgB,CAAC;IAC1D,UAAU,CAAC,cAAc,OAAS,CAAC,yBAAyB,EAAE,CAAC,eAAe,WAAW,SAAS,aAAa,EAAE,CAAC,eAAe,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAC/L,UAAU,IAAM;IAChB,UAAU,CAAC,WAAa,CAAC,WAAW,EAAE,SAAS,2CAA2C,CAAC;IAC3F,UAAU,CAAC,KAAO,CAAC,cAAc,EAAE,GAAG,8HAA8H,CAAC;AACzK;AAEA,MAAM,sBAAsB,aAAa,CAAC,WAAW;AACrD,SAAS,SAAS,QAAQ,EAAE,UAAU;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACzB,IAAI,UAAU,MAAM;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,UAAU;AACnD;AACA,MAAM,cAAc;IAChB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IACzB,IAAI,UAAU,MAAM;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YAClB,UAAU,MAAM,QAAQ;YACxB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,SAAS,MAAM,OAAO;QAC1B,CAAC,GAAG;QAAC;KAAM;AACf;AAEA,MAAM,aAAa,CAAC,IAAO,EAAE,mBAAmB,GAAG,SAAS;AAC5D,SAAS,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM;IAC5D,MAAM,gBAAgB,SAAS;IAC/B,MAAM,kBAAkB,GAAG,UAAU,CAAC,KAAK,CAAC;IAC5C,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAqB;eAAc;SAAgB;QAAG,OAAO;YAAE,GAAG,KAAK;YAAE;QAAc;QAAG,GAAG,IAAI;IAAC,GAAG;AAC7J;AAEA,SAAS,YAAY,EAAE,UAAU,EAAE,WAAW,cAAc,EAAE;IAC1D,IAAI,YAAY,iBAAiB;QAC7B,OAAO;IACX;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,UAAU;QAAU,WAAW;QAA2B,gBAAgB;IAAyG,GACpN,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;QAAE,MAAM;QAAyB,QAAQ;QAAU,KAAK;QAAuB,cAAc;IAAyB,GAAG;AAC1J;AAEA,MAAM,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,cAAc,IAAI,EAAE,eAAe,CAAC,CAAC,EAAE,iBAAiB;IAAC;IAAG;CAAE,EAAE,sBAAsB,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM;IACrK,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;QAAG,OAAO;QAAG,QAAQ;IAAE;IACnF,MAAM,kBAAkB,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;QAAC;QAAgC;KAAU;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ,OAAO,EAAE;YACjB,MAAM,WAAW,QAAQ,OAAO,CAAC,OAAO;YACxC,gBAAgB;gBACZ,GAAG,SAAS,CAAC;gBACb,GAAG,SAAS,CAAC;gBACb,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;YAC3B;QACJ;IACJ,GAAG;QAAC;KAAM;IACV,IAAI,OAAO,UAAU,eAAe,CAAC,OAAO;QACxC,OAAO;IACX;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;QAAE,WAAW,CAAC,UAAU,EAAE,IAAI,aAAa,KAAK,GAAG,EAAE,CAAC,EAAE,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC,CAAC;QAAE,WAAW;QAAiB,YAAY,aAAa,KAAK,GAAG,YAAY;QAAU,GAAG,IAAI;IAAC,GAChN,eAAgB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,OAAO,aAAa,KAAK,GAAG,IAAI,cAAc,CAAC,EAAE;QAAE,GAAG,CAAC,cAAc,CAAC,EAAE;QAAE,GAAG,CAAC,cAAc,CAAC,EAAE;QAAE,QAAQ,aAAa,MAAM,GAAG,IAAI,cAAc,CAAC,EAAE;QAAE,WAAW;QAA2B,OAAO;QAAc,IAAI;QAAqB,IAAI;IAAoB,IAChT,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,WAAW;QAAyB,GAAG,aAAa,MAAM,GAAG;QAAG,IAAI;QAAS,KAAK;QAAS,OAAO;IAAW,GAAG,QAC9I;AACR;AACA,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEtB,MAAM,gBAAgB,CAAC,OAAS,CAAC;QAC7B,OAAO,KAAK,WAAW;QACvB,QAAQ,KAAK,YAAY;IAC7B,CAAC;AACD,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,GAAK,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM;AACtE,MAAM,gBAAgB,CAAC,WAAW;IAAE,GAAG;IAAG,GAAG;AAAE,CAAC,EAAE,SAAW,CAAC;QAC1D,GAAG,MAAM,SAAS,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;QAC/C,GAAG,MAAM,SAAS,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;IACnD,CAAC;AACD,gFAAgF;AAChF,oDAAoD;AACpD,MAAM,sBAAsB,CAAC,OAAO,KAAK;IACrC,IAAI,QAAQ,KAAK;QACb,OAAO,MAAM,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,MAAM;IACjD,OACK,IAAI,QAAQ,KAAK;QAClB,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,MAAM;IAClD;IACA,OAAO;AACX;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,MAAM,YAAY,oBAAoB,IAAI,CAAC,EAAE,IAAI,OAAO,KAAK,GAAG,MAAM;IACtE,MAAM,YAAY,oBAAoB,IAAI,CAAC,EAAE,IAAI,OAAO,MAAM,GAAG,MAAM;IACvE,OAAO;QAAC;QAAW;KAAU;AACjC;AACA,MAAM,oBAAoB,CAAC,UAAY,QAAQ,WAAW,QAAQ,QAAQ;AAC1E,MAAM,mBAAmB,CAAC,MAAM,OAAS,CAAC;QACtC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;QAC1B,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;QAC1B,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE;QAC7B,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE;IACjC,CAAC;AACD,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;QAC5C;QACA;QACA,IAAI,IAAI;QACR,IAAI,IAAI;IACZ,CAAC;AACD,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAK,CAAC;QACrC;QACA;QACA,OAAO,KAAK;QACZ,QAAQ,KAAK;IACjB,CAAC;AACD,MAAM,aAAa,CAAC,OAAS,CAAC;QAC1B,GAAI,KAAK,gBAAgB,IAAI;YAAE,GAAG;YAAG,GAAG;QAAE,CAAC;QAC3C,OAAO,KAAK,KAAK,IAAI;QACrB,QAAQ,KAAK,MAAM,IAAI;IAC3B,CAAC;AACD,MAAM,mBAAmB,CAAC,OAAO,QAAU,UAAU,iBAAiB,UAAU,QAAQ,UAAU;AAClG,MAAM,qBAAqB,CAAC,OAAO;IAC/B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,KAAK,EAAE,MAAM,CAAC,GAAG,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IAC/G,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,MAAM,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACjH,OAAO,KAAK,IAAI,CAAC,WAAW;AAChC;AACA,8DAA8D;AAC9D,MAAM,eAAe,CAAC,MAAQ,UAAU,IAAI,KAAK,KAAK,UAAU,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC;AAClH,+DAA+D,GAC/D,MAAM,YAAY,CAAC,IAAM,CAAC,MAAM,MAAM,SAAS;AAC/C,MAAM,kBAAkB,OAAO,GAAG,CAAC;AACnC,uDAAuD;AACvD,MAAM,uBAAuB;IAAC;IAAS;IAAK;CAAS;AACrD,MAAM,UAAU,CAAC,IAAI;IACjB,wCAA4C;QACxC,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,QAAQ,mCAAmC,EAAE,IAAI;IACnF;AACJ;AACA,MAAM,uBAAuB,CAAC,QAAU,iBAAiB;AACzD,SAAS,eAAe,KAAK;IACzB,MAAM,UAAU,qBAAqB,SAAS,MAAM,WAAW,GAAG;IAClE,8CAA8C;IAC9C,MAAM,SAAU,QAAQ,YAAY,MAAM,CAAC,EAAE,IAAI,MAAM,MAAM;IAC7D,MAAM,UAAU;QAAC;QAAS;QAAU;KAAW,CAAC,QAAQ,CAAC,QAAQ,aAAa,QAAQ,aAAa;IACnG,wFAAwF;IACxF,OAAO,WAAW,CAAC,CAAC,QAAQ,QAAQ;AACxC;AACA,MAAM,eAAe,CAAC,QAAU,aAAa;AAC7C,MAAM,mBAAmB,CAAC,OAAO;IAC7B,MAAM,mBAAmB,aAAa;IACtC,MAAM,OAAO,mBAAmB,MAAM,OAAO,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,CAAC;IACnE,MAAM,OAAO,mBAAmB,MAAM,OAAO,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,CAAC;IACnE,OAAO;QACH,GAAG,OAAO,CAAC,QAAQ,QAAQ,CAAC;QAC5B,GAAG,OAAO,CAAC,QAAQ,OAAO,CAAC;IAC/B;AACJ;AACA,MAAM,UAAU,IAAM,OAAO,cAAc,eAAe,WAAW,WAAW,QAAQ,UAAU;AAElG,MAAM,WAAW,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,mBAAmB,EAAE,EAAG;IACpL,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MACxC,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,IAAI;QAAI,OAAO;QAAO,GAAG;QAAM,MAAM;QAAQ,WAAW;QAAyB,WAAW;QAAW,aAAa;IAAY,IAC9J,oBAAqB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,GAAG;QAAM,MAAM;QAAQ,eAAe;QAAG,aAAa;QAAkB,WAAW;IAA+B,IACrK,SAAS,UAAU,WAAW,UAAU,UAAW,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QAAE,GAAG;QAAQ,GAAG;QAAQ,OAAO;QAAO,YAAY;QAAY,aAAa;QAAa,cAAc;QAAc,gBAAgB;QAAgB,qBAAqB;IAAoB,KAAM;AAC9R;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,eAAe,CAAC,YAAY;IAC9B,IAAI,OAAO,gBAAgB,eAAe,aAAa;QACnD,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACjC;IACA,OAAO,OAAO,eAAe,cAAc,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC,GAAG;AACnF;AACA,SAAS,kBAAkB,EAAE,EAAE,QAAQ,EAAE,OAAO;IAC5C,OAAO,YAAY,YACb,UACA,CAAC;QACC,MAAM,OAAO,WAAW,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QACnD,IAAI,MAAM;YACN,QAAQ,OAAO;gBAAE,GAAG,IAAI;YAAC;QAC7B;IACJ;AACR;AACA,mFAAmF;AACnF,SAAS,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAG;IAC1D,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW;IAC9C,MAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU;IAClE,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW;IAC9C,MAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU;IAClE,OAAO;QAAC;QAAS;QAAS;QAAS;KAAQ;AAC/C;AACA,SAAS,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAG;IAChI,gFAAgF;IAChF,8FAA8F;IAC9F,MAAM,UAAU,UAAU,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,UAAU;IAC9F,MAAM,UAAU,UAAU,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,UAAU;IAC9F,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU;IACnC,OAAO;QAAC;QAAS;QAAS;QAAS;KAAQ;AAC/C;AAEA,IAAI;AACJ,CAAC,SAAU,cAAc;IACrB,cAAc,CAAC,SAAS,GAAG;IAC3B,cAAc,CAAC,QAAQ,GAAG;AAC9B,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AACzC,IAAI;AACJ,CAAC,SAAU,eAAe;IACtB,eAAe,CAAC,OAAO,GAAG;IAC1B,eAAe,CAAC,WAAW,GAAG;IAC9B,eAAe,CAAC,aAAa,GAAG;AACpC,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAC3C,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,UAAU,GAAG;IAC3B,aAAa,CAAC,OAAO,GAAG;AAC5B,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AAEvC,IAAI;AACJ,CAAC,SAAU,kBAAkB;IACzB,kBAAkB,CAAC,SAAS,GAAG;IAC/B,kBAAkB,CAAC,WAAW,GAAG;IACjC,kBAAkB,CAAC,OAAO,GAAG;IAC7B,kBAAkB,CAAC,aAAa,GAAG;IACnC,kBAAkB,CAAC,eAAe,GAAG;AACzC,CAAC,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;AACjD,IAAI;AACJ,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,cAAc,GAAG;AAChC,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAEjC,IAAI;AACJ,CAAC,SAAU,QAAQ;IACf,QAAQ,CAAC,OAAO,GAAG;IACnB,QAAQ,CAAC,MAAM,GAAG;IAClB,QAAQ,CAAC,QAAQ,GAAG;IACpB,QAAQ,CAAC,SAAS,GAAG;AACzB,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;AAE7B,SAAS,WAAW,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACvC,IAAI,QAAQ,SAAS,IAAI,IAAI,QAAQ,SAAS,KAAK,EAAE;QACjD,OAAO;YAAC,MAAM,CAAC,KAAK,EAAE;YAAG;SAAG;IAChC;IACA,OAAO;QAAC;QAAI,MAAM,CAAC,KAAK,EAAE;KAAE;AAChC;AACA,SAAS,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,GAAG,EAAG;IACjI,MAAM,CAAC,gBAAgB,eAAe,GAAG,WAAW;QAChD,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IACA,MAAM,CAAC,gBAAgB,eAAe,GAAG,WAAW;QAChD,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACR;IACA,MAAM,CAAC,QAAQ,QAAQ,SAAS,QAAQ,GAAG,oBAAoB;QAC3D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,OAAO;QACH,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;QACvH;QACA;QACA;QACA;KACH;AACL;AACA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,iBAAiB,SAAS,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAG;IACvQ,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,oBAAoB;QAC/C;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAM,QAAQ;QAAQ,QAAQ;QAAQ,OAAO;QAAO,YAAY;QAAY,aAAa;QAAa,cAAc;QAAc,gBAAgB;QAAgB,qBAAqB;QAAqB,OAAO;QAAO,WAAW;QAAW,aAAa;QAAa,kBAAkB;IAAiB;AAC/V;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,mBAAmB;IACrB,CAAC,SAAS,IAAI,CAAC,EAAE;QAAE,GAAG,CAAC;QAAG,GAAG;IAAE;IAC/B,CAAC,SAAS,KAAK,CAAC,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC/B,CAAC,SAAS,GAAG,CAAC,EAAE;QAAE,GAAG;QAAG,GAAG,CAAC;IAAE;IAC9B,CAAC,SAAS,MAAM,CAAC,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;AACpC;AACA,MAAM,eAAe,CAAC,EAAE,MAAM,EAAE,iBAAiB,SAAS,MAAM,EAAE,MAAM,EAAG;IACvE,IAAI,mBAAmB,SAAS,IAAI,IAAI,mBAAmB,SAAS,KAAK,EAAE;QACvE,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG;YAAE,GAAG;YAAG,GAAG;QAAE,IAAI;YAAE,GAAG,CAAC;YAAG,GAAG;QAAE;IAChE;IACA,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG;QAAE,GAAG;QAAG,GAAG;IAAE,IAAI;QAAE,GAAG;QAAG,GAAG,CAAC;IAAE;AAChE;AACA,MAAM,WAAW,CAAC,GAAG,IAAM,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;AAClF,wEAAwE;AACxE,iIAAiI;AACjI,SAAS,UAAU,EAAE,MAAM,EAAE,iBAAiB,SAAS,MAAM,EAAE,MAAM,EAAE,iBAAiB,SAAS,GAAG,EAAE,MAAM,EAAE,MAAM,EAAG;IACnH,MAAM,YAAY,gBAAgB,CAAC,eAAe;IAClD,MAAM,YAAY,gBAAgB,CAAC,eAAe;IAClD,MAAM,eAAe;QAAE,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;QAAQ,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;IAAO;IAC9F,MAAM,eAAe;QAAE,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;QAAQ,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;IAAO;IAC9F,MAAM,MAAM,aAAa;QACrB,QAAQ;QACR;QACA,QAAQ;IACZ;IACA,MAAM,cAAc,IAAI,CAAC,KAAK,IAAI,MAAM;IACxC,MAAM,UAAU,GAAG,CAAC,YAAY;IAChC,IAAI,SAAS,EAAE;IACf,IAAI,SAAS;IACb,MAAM,kBAAkB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,MAAM,kBAAkB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,eAAe,GAAG,cAAc;QACnF,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;IACrB;IACA,0CAA0C;IAC1C,IAAI,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,KAAK,CAAC,GAAG;QACxD,UAAU,OAAO,CAAC,IAAI;QACtB,UAAU,OAAO,CAAC,IAAI;QACtB,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM,gBAAgB;YAClB;gBAAE,GAAG;gBAAS,GAAG,aAAa,CAAC;YAAC;YAChC;gBAAE,GAAG;gBAAS,GAAG,aAAa,CAAC;YAAC;SACnC;QACD,OAAO;QACP,OAAO;QACP,KAAK;QACL,MAAM,kBAAkB;YACpB;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG;YAAQ;YAChC;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG;YAAQ;SACnC;QACD,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS;YACpC,SAAS,gBAAgB,MAAM,gBAAgB;QACnD,OACK;YACD,SAAS,gBAAgB,MAAM,kBAAkB;QACrD;IACJ,OACK;QACD,2FAA2F;QAC3F,MAAM,eAAe;YAAC;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG,aAAa,CAAC;YAAC;SAAE;QAC/D,MAAM,eAAe;YAAC;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG,aAAa,CAAC;YAAC;SAAE;QAC/D,gDAAgD;QAChD,IAAI,gBAAgB,KAAK;YACrB,SAAS,UAAU,CAAC,KAAK,UAAU,eAAe;QACtD,OACK;YACD,SAAS,UAAU,CAAC,KAAK,UAAU,eAAe;QACtD;QACA,IAAI,mBAAmB,gBAAgB;YACnC,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY;YAC/D,gTAAgT;YAChT,IAAI,QAAQ,QAAQ;gBAChB,MAAM,YAAY,KAAK,GAAG,CAAC,SAAS,GAAG,SAAS;gBAChD,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS;oBACpC,eAAe,CAAC,YAAY,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI;gBAChG,OACK;oBACD,eAAe,CAAC,YAAY,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI;gBAChG;YACJ;QACJ;QACA,4FAA4F;QAC5F,IAAI,mBAAmB,gBAAgB;YACnC,MAAM,sBAAsB,gBAAgB,MAAM,MAAM;YACxD,MAAM,YAAY,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,oBAAoB;YAC3E,MAAM,qBAAqB,YAAY,CAAC,oBAAoB,GAAG,YAAY,CAAC,oBAAoB;YAChG,MAAM,qBAAqB,YAAY,CAAC,oBAAoB,GAAG,YAAY,CAAC,oBAAoB;YAChG,MAAM,mBAAmB,AAAC,SAAS,CAAC,YAAY,KAAK,KAAK,CAAC,AAAC,CAAC,aAAa,sBAAwB,aAAa,kBAAmB,KAC7H,SAAS,CAAC,YAAY,KAAK,KAAK,CAAC,AAAC,CAAC,aAAa,sBAAwB,aAAa,kBAAmB;YAC7G,IAAI,kBAAkB;gBAClB,SAAS,gBAAgB,MAAM,eAAe;YAClD;QACJ;QACA,MAAM,iBAAiB;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;QACtG,MAAM,iBAAiB;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;QACtG,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/G,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/G,gEAAgE;QAChE,IAAI,gBAAgB,cAAc;YAC9B,UAAU,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,IAAI;YAClD,UAAU,MAAM,CAAC,EAAE,CAAC,CAAC;QACzB,OACK;YACD,UAAU,MAAM,CAAC,EAAE,CAAC,CAAC;YACrB,UAAU,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,IAAI;QACtD;IACJ;IACA,MAAM,aAAa;QACf;QACA;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;WAC5E;QACH;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;QAC/E;KACH;IACD,OAAO;QAAC;QAAY;QAAS;QAAS;QAAgB;KAAe;AACzE;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI;IAC1B,MAAM,WAAW,KAAK,GAAG,CAAC,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG;IAClE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG;IACjB,UAAU;IACV,IAAI,AAAC,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC,IAAM,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC,EAAG;QACtD,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;IACvB;IACA,8BAA8B;IAC9B,IAAI,EAAE,CAAC,KAAK,GAAG;QACX,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;QAC9B,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,WAAW,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,WAAW,MAAM;IACjF;IACA,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;IAC9B,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;IAC9B,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,WAAW,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,WAAW,KAAK,CAAC,EAAE,GAAG;AACjF;AACA,SAAS,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,GAAG,EAAE,eAAe,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,EAAG;IAChL,MAAM,CAAC,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,GAAG,UAAU;QACzD,QAAQ;YAAE,GAAG;YAAS,GAAG;QAAQ;QACjC;QACA,QAAQ;YAAE,GAAG;YAAS,GAAG;QAAQ;QACjC;QACA,QAAQ;YAAE,GAAG;YAAS,GAAG;QAAQ;QACjC;IACJ;IACA,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,GAAG;QAChC,IAAI,UAAU;QACd,IAAI,IAAI,KAAK,IAAI,OAAO,MAAM,GAAG,GAAG;YAChC,UAAU,QAAQ,MAAM,CAAC,IAAI,EAAE,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE;QACvD,OACK;YACD,UAAU,GAAG,MAAM,IAAI,MAAM,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACnD;QACA,OAAO;QACP,OAAO;IACX,GAAG;IACH,OAAO;QAAC;QAAM;QAAQ;QAAQ;QAAS;KAAQ;AACnD;AACA,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,iBAAiB,SAAS,MAAM,EAAE,iBAAiB,SAAS,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAG;IAClR,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,kBAAkB;QAC7C;QACA;QACA;QACA;QACA;QACA;QACA,cAAc,aAAa;QAC3B,QAAQ,aAAa;IACzB;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAM,QAAQ;QAAQ,QAAQ;QAAQ,OAAO;QAAO,YAAY;QAAY,aAAa;QAAa,cAAc;QAAc,gBAAgB;QAAgB,qBAAqB;QAAqB,OAAO;QAAO,WAAW;QAAW,aAAa;QAAa,kBAAkB;IAAiB;AAC/V;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,QAAW,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB;QAAE,GAAG,KAAK;QAAE,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;gBAAE,cAAc;gBAAG,QAAQ,MAAM,WAAW,EAAE;YAAO,CAAC,GAAG;YAAC,MAAM,WAAW,EAAE;SAAO;IAAE;AACpM,SAAS,WAAW,GAAG;AAEvB,SAAS,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAG;IAC5D,MAAM,CAAC,QAAQ,QAAQ,SAAS,QAAQ,GAAG,cAAc;QACrD;QACA;QACA;QACA;IACJ;IACA,OAAO;QAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,SAAS;QAAE;QAAQ;QAAQ;QAAS;KAAQ;AAC/F;AACA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAG;IAClM,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,gBAAgB;QAAE;QAAS;QAAS;QAAS;IAAQ;IACpF,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAM,QAAQ;QAAQ,QAAQ;QAAQ,OAAO;QAAO,YAAY;QAAY,aAAa;QAAa,cAAc;QAAc,gBAAgB;QAAgB,qBAAqB;QAAqB,OAAO;QAAO,WAAW;QAAW,aAAa;QAAa,kBAAkB;IAAiB;AAC/V;AACA,aAAa,WAAW,GAAG;AAE3B,SAAS,uBAAuB,QAAQ,EAAE,SAAS;IAC/C,IAAI,YAAY,GAAG;QACf,OAAO,MAAM;IACjB;IACA,OAAO,YAAY,KAAK,KAAK,IAAI,CAAC,CAAC;AACvC;AACA,SAAS,wBAAwB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;IACvD,OAAQ;QACJ,KAAK,SAAS,IAAI;YACd,OAAO;gBAAC,KAAK,uBAAuB,KAAK,IAAI;gBAAI;aAAG;QACxD,KAAK,SAAS,KAAK;YACf,OAAO;gBAAC,KAAK,uBAAuB,KAAK,IAAI;gBAAI;aAAG;QACxD,KAAK,SAAS,GAAG;YACb,OAAO;gBAAC;gBAAI,KAAK,uBAAuB,KAAK,IAAI;aAAG;QACxD,KAAK,SAAS,MAAM;YAChB,OAAO;gBAAC;gBAAI,KAAK,uBAAuB,KAAK,IAAI;aAAG;IAC5D;AACJ;AACA,SAAS,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,GAAG,EAAE,YAAY,IAAI,EAAG;IAC7I,MAAM,CAAC,gBAAgB,eAAe,GAAG,wBAAwB;QAC7D,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;IACP;IACA,MAAM,CAAC,gBAAgB,eAAe,GAAG,wBAAwB;QAC7D,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;IACP;IACA,MAAM,CAAC,QAAQ,QAAQ,SAAS,QAAQ,GAAG,oBAAoB;QAC3D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,OAAO;QACH,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;QACvH;QACA;QACA;QACA;KACH;AACL;AACA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,iBAAiB,SAAS,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAG;IAC9Q,MAAM,CAAC,MAAM,QAAQ,OAAO,GAAG,cAAc;QACzC;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,aAAa;IAC5B;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAM,QAAQ;QAAQ,QAAQ;QAAQ,OAAO;QAAO,YAAY;QAAY,aAAa;QAAa,cAAc;QAAc,gBAAgB;QAAgB,qBAAqB;QAAqB,OAAO;QAAO,WAAW;QAAW,aAAa;QAAa,kBAAkB;IAAiB;AAC/V;AACA,WAAW,WAAW,GAAG;AAEzB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAE;AACpC,MAAM,WAAW,cAAc,QAAQ;AACvC,cAAc,QAAQ;AACtB,MAAM,YAAY;IACd,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC1B,OAAO;AACX;AAEA,MAAM,SAAS,CAAC,UAAY,QAAQ,WAAW,YAAY,WAAW,YAAY;AAClF,MAAM,SAAS,CAAC,UAAY,QAAQ,WAAW,CAAC,CAAC,YAAY,OAAO,KAAK,CAAC,CAAC,YAAY,OAAO;AAC9F,MAAM,cAAc,CAAC,MAAM,OAAO;IAC9B,IAAI,CAAC,OAAO,OAAO;QACf,OAAO,EAAE;IACb;IACA,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;IAChF,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,WAAW,QAAQ,CAAC,EAAE,EAAE;AACvD;AACA,MAAM,cAAc,CAAC,MAAM,OAAO;IAC9B,IAAI,CAAC,OAAO,OAAO;QACf,OAAO,EAAE;IACb;IACA,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,KAAK,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;IACjF,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,YAAY,QAAQ,CAAC,EAAE,EAAE;AACxD;AACA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,GAAK,CAAC,gBAAgB,EAAE,SAAS,gBAAgB,GAAG,CAAC,EAAE,SAAS,gBAAgB,IAAI;AACrJ,MAAM,cAAc,CAAC,QAAQ;IACzB,IAAI,OAAO,WAAW,aAAa;QAC/B,OAAO;IACX;IACA,IAAI,OAAO,WAAW,UAAU;QAC5B,OAAO;IACX;IACA,MAAM,WAAW,OAAO,GAAG,KAAK,EAAE,CAAC,GAAG;IACtC,OAAO,GAAG,WAAW,OAAO,IAAI,CAAC,QAC5B,IAAI,GACJ,GAAG,CAAC,CAAC,MAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,EACpC,IAAI,CAAC,MAAM;AACpB;AACA,MAAM,mBAAmB,CAAC,MAAM;IAC5B,OAAO,MAAM,IAAI,CAAC,CAAC,KAAO,GAAG,MAAM,KAAK,KAAK,MAAM,IAC/C,GAAG,MAAM,KAAK,KAAK,MAAM,IACzB,CAAC,GAAG,YAAY,KAAK,KAAK,YAAY,IAAK,CAAC,GAAG,YAAY,IAAI,CAAC,KAAK,YAAY,AAAC,KAClF,CAAC,GAAG,YAAY,KAAK,KAAK,YAAY,IAAK,CAAC,GAAG,YAAY,IAAI,CAAC,KAAK,YAAY,AAAC;AAC1F;AACA,MAAM,UAAU,CAAC,YAAY;IACzB,IAAI,CAAC,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM,EAAE;QAC1C,QAAQ,OAAO,aAAa,CAAC,WAAW;QACxC,OAAO;IACX;IACA,IAAI;IACJ,IAAI,OAAO,aAAa;QACpB,OAAO;YAAE,GAAG,UAAU;QAAC;IAC3B,OACK;QACD,OAAO;YACH,GAAG,UAAU;YACb,IAAI,UAAU;QAClB;IACJ;IACA,IAAI,iBAAiB,MAAM,QAAQ;QAC/B,OAAO;IACX;IACA,OAAO,MAAM,MAAM,CAAC;AACxB;AACA,MAAM,gBAAgB,CAAC,SAAS,eAAe,OAAO,UAAU;IAAE,iBAAiB;AAAK,CAAC;IACrF,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG,MAAM,GAAG;IACnC,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,MAAM,EAAE;QAChD,QAAQ,OAAO,aAAa,CAAC,WAAW;QACxC,OAAO;IACX;IACA,MAAM,YAAY,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IAC7C,IAAI,CAAC,WAAW;QACZ,QAAQ,OAAO,aAAa,CAAC,WAAW,CAAC;QACzC,OAAO;IACX;IACA,uEAAuE;IACvE,MAAM,OAAO;QACT,GAAG,IAAI;QACP,IAAI,QAAQ,eAAe,GAAG,UAAU,iBAAiB;QACzD,QAAQ,cAAc,MAAM;QAC5B,QAAQ,cAAc,MAAM;QAC5B,cAAc,cAAc,YAAY;QACxC,cAAc,cAAc,YAAY;IAC5C;IACA,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,WAAW,MAAM,CAAC;AAC1D;AACA;;;CAGC,GACD,MAAM,aAAa,CAAC,SAAS,eAAe,OAAO,UAAU;IAAE,iBAAiB;AAAK,CAAC;IAClF,QAAQ,IAAI,CAAC;IACb,OAAO,cAAc,SAAS,eAAe,OAAO;AACxD;AACA,MAAM,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,OAAO,EAAE,YAAY,CAAC,OAAO,MAAM;IAChF,MAAM,WAAW;QACb,GAAG,CAAC,IAAI,EAAE,IAAI;QACd,GAAG,CAAC,IAAI,EAAE,IAAI;IAClB;IACA,IAAI,YAAY;QACZ,OAAO;YACH,GAAG,QAAQ,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG;YACnC,GAAG,QAAQ,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG;QACvC;IACJ;IACA,OAAO;AACX;AACA,MAAM,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,OAAO;IACpD,OAAO;QACH,GAAG,IAAI,SAAS;QAChB,GAAG,IAAI,SAAS;IACpB;AACJ;AACA,MAAM,4BAA4B,CAAC,MAAM,aAAa;IAAC;IAAG;CAAE;IACxD,IAAI,CAAC,MAAM;QACP,OAAO;YACH,GAAG;YACH,GAAG;YACH,kBAAkB;gBACd,GAAG;gBACH,GAAG;YACP;QACJ;IACJ;IACA,MAAM,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC,EAAE;IACjD,MAAM,UAAU,CAAC,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,CAAC,EAAE;IAClD,MAAM,WAAW;QACb,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG;QACrB,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG;IACzB;IACA,OAAO;QACH,GAAG,QAAQ;QACX,kBAAkB,KAAK,gBAAgB,GACjC;YACE,GAAG,KAAK,gBAAgB,CAAC,CAAC,GAAG;YAC7B,GAAG,KAAK,gBAAgB,CAAC,CAAC,GAAG;QACjC,IACE;IACV;AACJ;AACA,MAAM,iBAAiB,CAAC,OAAO,aAAa;IAAC;IAAG;CAAE;IAC9C,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO;YAAE,GAAG;YAAG,GAAG;YAAG,OAAO;YAAG,QAAQ;QAAE;IAC7C;IACA,MAAM,MAAM,MAAM,MAAM,CAAC,CAAC,SAAS;QAC/B,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,0BAA0B,MAAM,YAAY,gBAAgB;QAC7E,OAAO,iBAAiB,SAAS,UAAU;YACvC;YACA;YACA,OAAO,KAAK,KAAK,IAAI;YACrB,QAAQ,KAAK,MAAM,IAAI;QAC3B;IACJ,GAAG;QAAE,GAAG;QAAU,GAAG;QAAU,IAAI,CAAC;QAAU,IAAI,CAAC;IAAS;IAC5D,OAAO,UAAU;AACrB;AACA,oCAAoC;AACpC,MAAM,iBAAiB,CAAC,OAAO,aAAa;IAAC;IAAG;CAAE;IAC9C,QAAQ,IAAI,CAAC;IACb,OAAO,eAAe,OAAO;AACjC;AACA,MAAM,iBAAiB,CAAC,eAAe,MAAM,CAAC,IAAI,IAAI,OAAO,GAAG;IAAC;IAAG;IAAG;CAAE,EAAE,YAAY,KAAK,EAC5F,iGAAiG;AACjG,4BAA4B,KAAK,EAAE,aAAa;IAAC;IAAG;CAAE;IAClD,MAAM,WAAW;QACb,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI;QACnB,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI;QACnB,OAAO,KAAK,KAAK,GAAG;QACpB,QAAQ,KAAK,MAAM,GAAG;IAC1B;IACA,MAAM,eAAe,EAAE;IACvB,cAAc,OAAO,CAAC,CAAC;QACnB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,IAAI,EAAE,SAAS,KAAK,EAAE,GAAG;QAC7D,IAAI,AAAC,6BAA6B,CAAC,cAAe,QAAQ;YACtD,OAAO;QACX;QACA,MAAM,EAAE,gBAAgB,EAAE,GAAG,0BAA0B,MAAM;QAC7D,MAAM,WAAW;YACb,GAAG,iBAAiB,CAAC;YACrB,GAAG,iBAAiB,CAAC;YACrB,OAAO,SAAS;YAChB,QAAQ,UAAU;QACtB;QACA,MAAM,kBAAkB,mBAAmB,UAAU;QACrD,MAAM,iBAAiB,OAAO,UAAU,eAAe,OAAO,WAAW,eAAe,UAAU,QAAQ,WAAW;QACrH,MAAM,mBAAmB,aAAa,kBAAkB;QACxD,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,MAAM,YAAY,kBAAkB,oBAAoB,mBAAmB;QAC3E,IAAI,aAAa,KAAK,QAAQ,EAAE;YAC5B,aAAa,IAAI,CAAC;QACtB;IACJ;IACA,OAAO;AACX;AACA,MAAM,oBAAoB,CAAC,OAAO;IAC9B,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;IAC3C,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,QAAQ,QAAQ,CAAC,KAAK,MAAM,KAAK,QAAQ,QAAQ,CAAC,KAAK,MAAM;AAC/F;AACA,0CAA0C;AAC1C,MAAM,wBAAwB,CAAC,QAAQ,OAAO,QAAQ,SAAS,SAAS,UAAU,GAAG;IACjF,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,qBAAqB,QAAQ,OAAO,QAAQ,SAAS,SAAS;IACrF,QAAQ,IAAI,CAAC;IACb,OAAO;QAAC;QAAG;QAAG;KAAK;AACvB;AACA,MAAM,uBAAuB,CAAC,QAAQ,OAAO,QAAQ,SAAS,SAAS,UAAU,GAAG;IAChF,MAAM,QAAQ,QAAQ,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,OAAO,CAAC;IACnD,MAAM,QAAQ,SAAS,CAAC,OAAO,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC;IACrD,MAAM,OAAO,KAAK,GAAG,CAAC,OAAO;IAC7B,MAAM,cAAc,MAAM,MAAM,SAAS;IACzC,MAAM,gBAAgB,OAAO,CAAC,GAAG,OAAO,KAAK,GAAG;IAChD,MAAM,gBAAgB,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG;IACjD,MAAM,IAAI,QAAQ,IAAI,gBAAgB;IACtC,MAAM,IAAI,SAAS,IAAI,gBAAgB;IACvC,OAAO;QAAE;QAAG;QAAG,MAAM;IAAY;AACrC;AACA,MAAM,kBAAkB,CAAC,WAAW,WAAW,CAAC;IAC5C,OAAO,UAAU,UAAU,GAAG,QAAQ,CAAC;AAC3C;AAEA,oEAAoE;AACpE,qEAAqE;AACrE,SAAS,WAAW,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,aAAa;IACvD,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,KAAK;QAC3C,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,eAAe;YAChD,IAAI,IAAI,CAAC;gBACL,IAAI,EAAE,EAAE,IAAI;gBACZ;gBACA,QAAQ,KAAK,EAAE;gBACf,GAAG,CAAC,KAAK,gBAAgB,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,KAAK,GAAG;gBACrD,GAAG,CAAC,KAAK,gBAAgB,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,MAAM,GAAG;YAC1D;QACJ;QACA,OAAO;IACX,GAAG,EAAE;AACT;AACA,SAAS,iBAAiB,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,EAAE,SAAS;IAC3E,mGAAmG;IACnG,wHAAwH;IACxH,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB;IAClC,MAAM,WAAW,IAAI,iBAAiB,CAAC,GAAG;IAC1C,MAAM,cAAc,SAAS,IAAI,CAAC,CAAC,KAAO,GAAG,SAAS,CAAC,QAAQ,CAAC;IAChE,IAAI,aAAa;QACb,MAAM,eAAe,YAAY,YAAY,CAAC;QAC9C,IAAI,cAAc;YACd,MAAM,aAAa,cAAc,WAAW;YAC5C,MAAM,WAAW,YAAY,YAAY,CAAC;YAC1C,MAAM,oBAAoB,UAAU;gBAAE,QAAQ;gBAAc,IAAI;gBAAU,MAAM;YAAW;YAC3F,IAAI,mBAAmB;gBACnB,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,gBAAgB,EAAE,IAAI,KAAK,cAAc,EAAE,EAAE,KAAK;gBAClG,OAAO;oBACH,QAAQ;wBACJ,IAAI;wBACJ,MAAM;wBACN,QAAQ;wBACR,GAAG,QAAQ,KAAK,IAAI,CAAC;wBACrB,GAAG,QAAQ,KAAK,IAAI,CAAC;oBACzB;oBACA;gBACJ;YACJ;QACJ;IACJ;IACA,qHAAqH;IACrH,IAAI,iBAAiB,EAAE;IACvB,IAAI,cAAc;IAClB,QAAQ,OAAO,CAAC,CAAC;QACb,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK;QAC3E,IAAI,YAAY,kBAAkB;YAC9B,MAAM,oBAAoB,UAAU;YACpC,IAAI,YAAY,aAAa;gBACzB,IAAI,WAAW,aAAa;oBACxB,iBAAiB;wBAAC;4BAAE;4BAAQ;wBAAkB;qBAAE;gBACpD,OACK,IAAI,aAAa,aAAa;oBAC/B,wEAAwE;oBACxE,eAAe,IAAI,CAAC;wBAChB;wBACA;oBACJ;gBACJ;gBACA,cAAc;YAClB;QACJ;IACJ;IACA,IAAI,CAAC,eAAe,MAAM,EAAE;QACxB,OAAO;YAAE,QAAQ;YAAM,mBAAmB;QAAgB;IAC9D;IACA,IAAI,eAAe,MAAM,KAAK,GAAG;QAC7B,OAAO,cAAc,CAAC,EAAE;IAC5B;IACA,MAAM,iBAAiB,eAAe,IAAI,CAAC,CAAC,EAAE,iBAAiB,EAAE,GAAK,kBAAkB,OAAO;IAC/F,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,GAAK,OAAO,IAAI,KAAK;IAC5E,uHAAuH;IACvH,OAAQ,eAAe,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAK,kBAAkB,OAAO,IAAI,KAAK,WAAY,iBAAiB,kBAAkB,OAAO,GAAG,SAAU,cAAc,CAAC,EAAE;AACzL;AACA,MAAM,iBAAiB;IAAE,QAAQ;IAAM,QAAQ;IAAM,cAAc;IAAM,cAAc;AAAK;AAC5F,MAAM,gBAAgB,IAAM,CAAC;QACzB,eAAe;QACf,SAAS;QACT,YAAY;QACZ,WAAW;IACf,CAAC;AACD,qFAAqF;AACrF,SAAS,cAAc,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG;IACrG,MAAM,WAAW,aAAa;IAC9B,MAAM,gBAAgB,IAAI,aAAa,CAAC,CAAC,6BAA6B,EAAE,QAAQ,OAAO,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,KAAK,EAAE,CAAC;IACxH,MAAM,SAAS;QACX,GAAG,eAAe;QAClB,eAAe;IACnB;IACA,IAAI,eAAe;QACf,MAAM,aAAa,cAAc,WAAW;QAC5C,MAAM,eAAe,cAAc,YAAY,CAAC;QAChD,MAAM,WAAW,cAAc,YAAY,CAAC;QAC5C,MAAM,cAAc,cAAc,SAAS,CAAC,QAAQ,CAAC;QACrD,MAAM,iBAAiB,cAAc,SAAS,CAAC,QAAQ,CAAC;QACxD,MAAM,aAAa;YACf,QAAQ,WAAW,eAAe;YAClC,cAAc,WAAW,WAAW;YACpC,QAAQ,WAAW,aAAa;YAChC,cAAc,WAAW,eAAe;QAC5C;QACA,OAAO,UAAU,GAAG;QACpB,MAAM,gBAAgB,eAAe;QACrC,iFAAiF;QACjF,MAAM,UAAU,iBACZ,CAAC,mBAAmB,eAAe,MAAM,GACnC,AAAC,YAAY,eAAe,YAAc,CAAC,YAAY,eAAe,WACtE,iBAAiB,cAAc,aAAa,YAAY;QAClE,IAAI,SAAS;YACT,OAAO,SAAS,GAAG;gBACf,QAAQ;gBACR;gBACA,MAAM;YACV;YACA,OAAO,OAAO,GAAG,kBAAkB;QACvC;IACJ;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;IAC5D,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK;QACtB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,gBAAgB;YAC9C,IAAI,gBAAgB,EAAE;YACtB,IAAI,gBAAgB,EAAE;YACtB,IAAI,cAAc;gBACd,gBAAgB,WAAW,MAAM,cAAc,UAAU,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,YAAY;gBAC9F,gBAAgB,WAAW,MAAM,cAAc,UAAU,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,YAAY;YAClG;YACA,IAAI,IAAI,IAAI,kBAAkB;QAClC;QACA,OAAO;IACX,GAAG,EAAE;AACT;AACA,SAAS,cAAc,eAAe,EAAE,aAAa;IACjD,IAAI,iBAAiB;QACjB,OAAO;IACX,OACK,IAAI,eAAe,UAAU,SAAS,WAAW;QAClD,OAAO;IACX,OACK,IAAI,eAAe,UAAU,SAAS,WAAW;QAClD,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,aAAa;IACpC,eAAe,UAAU,OAAO,SAAS,cAAc,4BAA4B;AACvF;AACA,SAAS,oBAAoB,wBAAwB,EAAE,aAAa;IAChE,IAAI,mBAAmB;IACvB,IAAI,eAAe;QACf,mBAAmB;IACvB,OACK,IAAI,4BAA4B,CAAC,eAAe;QACjD,mBAAmB;IACvB;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAG;IAChJ,qEAAqE;IACrE,MAAM,MAAM,kBAAkB,MAAM,MAAM;IAC1C,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAG,GAAG;IAC5H,IAAI,YAAY;IAChB,IAAI;IACJ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB;IAClC,MAAM,gBAAgB,KAAK,iBAAiB,GAAG;IAC/C,MAAM,aAAa,cAAc,iBAAiB;IAClD,MAAM,kBAAkB,SAAS;IACjC,IAAI,CAAC,mBAAmB,CAAC,YAAY;QACjC;IACJ;IACA,IAAI;IACJ,IAAI,qBAAqB,iBAAiB,OAAO;IACjD,IAAI,iBAAiB;IACrB,IAAI,aAAa;IACjB,IAAI,UAAU;IACd,IAAI,gBAAgB;IACpB,MAAM,eAAe,gBAAgB;QACjC,OAAO;QACP;QACA;QACA;IACJ;IACA,wGAAwG;IACxG,MAAM,UAAU;QACZ,IAAI,CAAC,kBAAkB;YACnB;QACJ;QACA,MAAM,CAAC,WAAW,UAAU,GAAG,YAAY,oBAAoB;QAC/D,MAAM;YAAE,GAAG;YAAW,GAAG;QAAU;QACnC,YAAY,sBAAsB;IACtC;IACA,SAAS;QACL;QACA,kBAAkB;QAClB,2FAA2F;QAC3F,kBAAkB;QAClB,oBAAoB;QACpB,sBAAsB;QACtB,uBAAuB;YACnB;YACA;YACA,MAAM;QACV;QACA,qBAAqB;IACzB;IACA,iBAAiB,OAAO;QAAE;QAAQ;QAAU;IAAW;IACvD,SAAS,cAAc,KAAK;QACxB,MAAM,EAAE,SAAS,EAAE,GAAG;QACtB,qBAAqB,iBAAiB,OAAO;QAC7C,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,iBAAiB,OAAO,KAAK,qBAAqB,oBAAoB,WAAW,OAAO;YAAC;YAAG;SAAE,GAAG,kBAAkB,cAAc,CAAC,SAAW,cAAc,QAAQ,gBAAgB,QAAQ,UAAU,WAAW,WAAW,UAAU,mBAAmB;QAC9R,gBAAgB;QAChB,IAAI,CAAC,gBAAgB;YACjB;YACA,iBAAiB;QACrB;QACA,gBAAgB,kBAAkB,aAAa;QAC/C,aAAa,kBAAkB,UAAU;QACzC,UAAU,kBAAkB,OAAO;QACnC,SAAS;YACL,oBAAoB,iBAAiB,UAC/B,qBAAqB;gBACnB,GAAG,cAAc,CAAC;gBAClB,GAAG,cAAc,CAAC;YACtB,GAAG,aACD;YACN,kBAAkB,oBAAoB,CAAC,CAAC,eAAe;YACvD,qBAAqB,kBAAkB,SAAS;QACpD;QACA,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,eAAe;YAC9C,OAAO,kBAAkB;QAC7B;QACA,IAAI,WAAW,MAAM,KAAK,WAAW,MAAM,IAAI,eAAe;YAC1D,kBAAkB;YAClB,mBAAmB;YACnB,oFAAoF;YACpF,cAAc,SAAS,CAAC,GAAG,CAAC,cAAc;YAC1C,cAAc,SAAS,CAAC,MAAM,CAAC,SAAS;YACxC,cAAc,SAAS,CAAC,MAAM,CAAC,4BAA4B;QAC/D;IACJ;IACA,SAAS,YAAY,KAAK;QACtB,IAAI,CAAC,iBAAiB,aAAa,KAAK,cAAc,SAAS;YAC3D,YAAY;QAChB;QACA,8DAA8D;QAC9D,mDAAmD;QACnD,WAAW,YAAY,GAAG;QAC1B,IAAI,iBAAiB;YACjB,iBAAiB;QACrB;QACA,kBAAkB;QAClB;QACA,qBAAqB;QACrB,iBAAiB;QACjB,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,IAAI,mBAAmB,CAAC,aAAa;QACrC,IAAI,mBAAmB,CAAC,WAAW;QACnC,IAAI,mBAAmB,CAAC,aAAa;QACrC,IAAI,mBAAmB,CAAC,YAAY;IACxC;IACA,IAAI,gBAAgB,CAAC,aAAa;IAClC,IAAI,gBAAgB,CAAC,WAAW;IAChC,IAAI,gBAAgB,CAAC,aAAa;IAClC,IAAI,gBAAgB,CAAC,YAAY;AACrC;AAEA,MAAM,cAAc,IAAM;AAC1B,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,uBAAuB,EAAE,qBAAqB;QAC9C,gBAAgB,EAAE,cAAc;QAChC,gBAAgB,EAAE,cAAc;IACpC,CAAC;AACD,MAAM,qBAAqB,CAAC,QAAQ,UAAU,OAAS,CAAC;QACpD,MAAM,EAAE,uBAAuB,WAAW,EAAE,qBAAqB,SAAS,EAAE,4BAA4B,WAAW,EAAG,GAAG;QACzH,OAAO;YACH,YAAY,AAAC,aAAa,WAAW,UAAU,aAAa,aAAa,YAAY,aAAa,SAAS,QACtG,WAAW,WAAW,UAAU,WAAW,aAAa,YAAY,WAAW,SAAS;YAC7F,iBAAiB,aAAa,WAAW,UAAU,aAAa,aAAa,YAAY,aAAa,SAAS;QACnH;IACJ;AACA,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,OAAO,QAAQ,EAAE,WAAW,SAAS,GAAG,EAAE,iBAAiB,EAAE,gBAAgB,IAAI,EAAE,qBAAqB,IAAI,EAAE,mBAAmB,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,EAAE;IAC1O,MAAM,WAAW,MAAM;IACvB,MAAM,WAAW,SAAS;IAC1B,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IACvE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,SAAS,mBAAmB,QAAQ,UAAU,OAAO,iLAAA,CAAA,UAAO;IACpG,IAAI,CAAC,QAAQ;QACT,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,aAAa,CAAC,WAAW;IAC/D;IACA,MAAM,oBAAoB,CAAC;QACvB,MAAM,EAAE,kBAAkB,EAAE,WAAW,eAAe,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ;QAC1F,MAAM,aAAa;YACf,GAAG,kBAAkB;YACrB,GAAG,MAAM;QACb;QACA,IAAI,iBAAiB;YACjB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ;YAC1C,SAAS,QAAQ,YAAY;QACjC;QACA,kBAAkB;QAClB,YAAY;IAChB;IACA,MAAM,gBAAgB,CAAC;QACnB,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,MAAM,mBAAmB,aAAa;QACtC,IAAI,sBAAsB,CAAC,AAAC,oBAAoB,MAAM,MAAM,KAAK,KAAM,CAAC,gBAAgB,GAAG;YACvF,kBAAkB;gBACd;gBACA;gBACA;gBACA,WAAW;gBACX;gBACA,UAAU,MAAM,QAAQ;gBACxB,UAAU,MAAM,QAAQ;gBACxB,mBAAmB,qBAAqB,MAAM,QAAQ,GAAG,iBAAiB,IAAI;YAClF;QACJ;QACA,IAAI,kBAAkB;YAClB,cAAc;QAClB,OACK;YACD,eAAe;QACnB;IACJ;IACA,MAAM,UAAU,CAAC;QACb,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,cAAc,EAAE,mBAAmB,sBAAsB,EAAG,GAAG,MAAM,QAAQ;QACzJ,IAAI,CAAC,UAAW,CAAC,8BAA8B,CAAC,oBAAqB;YACjE;QACJ;QACA,IAAI,CAAC,4BAA4B;YAC7B,sBAAsB,OAAO;gBAAE;gBAAQ;gBAAU,YAAY;YAAK;YAClE,MAAM,QAAQ,CAAC;gBAAE,4BAA4B;oBAAE;oBAAQ;oBAAM;gBAAS;YAAE;YACxE;QACJ;QACA,MAAM,MAAM,kBAAkB,MAAM,MAAM;QAC1C,MAAM,2BAA2B,qBAAqB,0BAA0B;QAChF,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,cAAc;YAC1C;YACA,IAAI;YACJ;QACJ,GAAG,gBAAgB,2BAA2B,MAAM,EAAE,2BAA2B,QAAQ,IAAI,MAAM,2BAA2B,IAAI,EAAE,0BAA0B;QAC9J,IAAI,SAAS;YACT,kBAAkB;QACtB;QACA,oBAAoB;QACpB,MAAM,QAAQ,CAAC;YAAE,4BAA4B;QAAK;IACtD;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,iBAAiB;QAAU,eAAe;QAAQ,kBAAkB;QAAU,WAAW,GAAG,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YACrK;YACA,CAAC,mBAAmB,EAAE,UAAU;YAChC;YACA;YACA;YACA;gBACI,QAAQ,CAAC;gBACT,QAAQ;gBACR,aAAa;gBACb,kBAAkB;gBAClB,gBAAgB;gBAChB,YAAY;gBACZ,qEAAqE;gBACrE,qBAAqB,iBAAiB,CAAC,AAAC,sBAAsB,CAAC,cAAgB,oBAAoB,UAAW;YAClH;SACH;QAAG,aAAa;QAAe,cAAc;QAAe,SAAS,iBAAiB,UAAU;QAAW,KAAK;QAAK,GAAG,IAAI;IAAC,GAAG;AACzI;AACA,OAAO,WAAW,GAAG;AACrB,IAAI,WAAW,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEpB,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,SAAS,GAAG,EAAE,iBAAiB,SAAS,MAAM,EAAG;IAC1G,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MACxC,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAU,UAAU;QAAgB,eAAe;IAAc,IACvG,MAAM,OACN,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAU,UAAU;QAAgB,eAAe;IAAc;AAC/G;AACA,YAAY,WAAW,GAAG;AAC1B,IAAI,gBAAgB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEzB,MAAM,YAAY,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,SAAS,MAAM,EAAE,GAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAClH,MAAM,OACN,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAU,UAAU;QAAgB,eAAe;IAAc;AAC3G,UAAU,WAAW,GAAG;AACxB,IAAI,cAAc,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEvB,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,iBAAiB,SAAS,GAAG,EAAE,GAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAChH,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,MAAM;QAAU,UAAU;QAAgB,eAAe;IAAc,IACvG,MAAM;AACV,WAAW,WAAW,GAAG;AACzB,IAAI,eAAe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAExB,MAAM,YAAY,IAAM;AACxB,UAAU,WAAW,GAAG;AAExB,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,eAAe,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ;QACpD,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;YAAC,CAAC;IACzE,CAAC;AACD,MAAM,WAAW,CAAC,MAAQ,IAAI,EAAE;AAChC,SAAS,SAAS,CAAC,EAAE,CAAC;IAClB,OAAQ,CAAA,GAAA,iLAAA,CAAA,UAAO,AAAD,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,cAC/D,CAAA,GAAA,iLAAA,CAAA,UAAO,AAAD,EAAE,EAAE,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC;AACnE;AACA,8EAA8E;AAC9E,2GAA2G;AAC3G,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,iBAAiB,EAAE;IACjD,MAAM,QAAQ;IACd,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,SAAS,YAAY;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,SAAS;YAAE,OAAO;YAAe,OAAO;QAAc;QAC5D,oBAAoB;QACpB,MAAM,QAAQ,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAO,GAAG;IAC1D,GAAG;QAAC;QAAe;QAAe;KAAkB;IACpD,OAAO;AACX;AACA,kBAAkB,WAAW,GAAG;AAChC,MAAM,iBAAiB,CAAC,IAAM,CAAC,CAAC,EAAE,iBAAiB;AACnD,SAAS,UAAU,EAAE,iBAAiB,EAAE;IACpC,MAAM,0BAA0B,SAAS;IACzC,IAAI,qBAAqB,yBAAyB;QAC9C,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mBAAmB;YAAE,mBAAmB;QAAkB;IACzF;IACA,OAAO;AACX;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,UAAU,EAAE,QAAQ;QACpB,UAAU,EAAE,QAAQ;QACpB,yBAAyB,EAAE,uBAAuB;QAClD,YAAY,EAAE,UAAU;QACxB,YAAY,EAAE,UAAU;QACxB,oBAAoB,EAAE,kBAAkB;QACxC,eAAe,EAAE,aAAa;QAC9B,OAAO,EAAE,KAAK;IAClB,CAAC;AACD,SAAS,gBAAgB,KAAK,EAAE,aAAa;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,OAAO,UAAU,aAAa;YAC9B,cAAc;QAClB;IACJ,GAAG;QAAC;KAAM;AACd;AACA,2EAA2E;AAC3E,SAAS,sBAAsB,GAAG,EAAE,KAAK,EAAE,QAAQ;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,OAAO,UAAU,aAAa;YAC9B,SAAS;gBAAE,CAAC,IAAI,EAAE;YAAM;QAC5B;IACJ,GAAG;QAAC;KAAM;AACd;AACA,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,kBAAkB,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAG;IAC3sB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,uBAAuB,EAAE,UAAU,EAAE,UAAU,EAAE,kBAAkB,EAAE,aAAa,EAAE,KAAK,EAAG,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IACvJ,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,oBAAoB,cAAc,IAAI,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;gBAAE,GAAG,kBAAkB;YAAC,CAAC;QACnF,wBAAwB,cAAc;QACtC,OAAO;YACH;QACJ;IACJ,GAAG,EAAE;IACL,sBAAsB,sBAAsB,oBAAoB,MAAM,QAAQ;IAC9E,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,aAAa,WAAW,MAAM,QAAQ;IAC5D,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,gBAAgB,cAAc,MAAM,QAAQ;IAClE,sBAAsB,uBAAuB,qBAAqB,MAAM,QAAQ;IAChF,sBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;IAC5E,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,oBAAoB,kBAAkB,MAAM,QAAQ;IAC1E,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,sBAAsB,oBAAoB,MAAM,QAAQ;IAC9E,sBAAsB,wBAAwB,sBAAsB,MAAM,QAAQ;IAClF,sBAAsB,cAAc,YAAY,MAAM,QAAQ;IAC9D,sBAAsB,YAAY,UAAU,MAAM,QAAQ;IAC1D,sBAAsB,iBAAiB,eAAe,MAAM,QAAQ;IACpE,sBAAsB,iBAAiB,eAAe,MAAM,QAAQ;IACpE,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,iBAAiB,SAAS,MAAM,QAAQ;IAC9D,sBAAsB,wBAAwB,gBAAgB,MAAM,QAAQ;IAC5E,sBAAsB,iBAAiB,eAAe,MAAM,QAAQ;IACpE,sBAAsB,iBAAiB,eAAe,MAAM,QAAQ;IACpE,sBAAsB,cAAc,YAAY,MAAM,QAAQ;IAC9D,sBAAsB,mBAAmB,iBAAiB,MAAM,QAAQ;IACxE,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,mBAAmB,iBAAiB,MAAM,QAAQ;IACxE,sBAAsB,wBAAwB,sBAAsB,MAAM,QAAQ;IAClF,sBAAsB,uBAAuB,qBAAqB,MAAM,QAAQ;IAChF,sBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;IACtE,sBAAsB,cAAc,YAAY,MAAM,QAAQ;IAC9D,sBAAsB,QAAQ,MAAM,MAAM,QAAQ;IAClD,sBAAsB,oBAAoB,kBAAkB,MAAM,QAAQ;IAC1E,sBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;IAC5E,sBAAsB,WAAW,SAAS,MAAM,QAAQ;IACxD,sBAAsB,oBAAoB,kBAAkB,MAAM,QAAQ;IAC1E,sBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;IAC5E,sBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;IAC5E,gBAAgB,OAAO;IACvB,gBAAgB,OAAO;IACvB,gBAAgB,SAAS;IACzB,gBAAgB,SAAS;IACzB,gBAAgB,iBAAiB;IACjC,gBAAgB,YAAY;IAC5B,OAAO;AACX;AAEA,MAAM,QAAQ;IAAE,SAAS;AAAO;AAChC,MAAM,gBAAgB;IAClB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,QAAQ,CAAC;IACT,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,UAAU;AACd;AACA,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,MAAM,oBAAoB;AAC1B,MAAM,aAAa,CAAC,IAAM,EAAE,eAAe;AAC3C,SAAS,gBAAgB,EAAE,IAAI,EAAE;IAC7B,MAAM,kBAAkB,SAAS;IACjC,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,IAAI,GAAG,kBAAkB,CAAC,EAAE,MAAM;QAAE,aAAa;QAAa,eAAe;QAAQ,OAAO;IAAc,GAAG;AACtJ;AACA,SAAS,iBAAiB,EAAE,IAAI,EAAE,mBAAmB,EAAE;IACnD,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MACxC,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,IAAI,GAAG,mBAAmB,CAAC,EAAE,MAAM;QAAE,OAAO;IAAM,GAC3E,0CACA,CAAC,uBAAuB,4DACxB,oDACA,MACJ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,IAAI,GAAG,mBAAmB,CAAC,EAAE,MAAM;QAAE,OAAO;IAAM,GAAG,wGAClF,CAAC,uBAAuB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB;QAAE,MAAM;IAAK;AAClF;AAEA,sEAAsE;AACtE,0EAA0E;AAC1E,2EAA2E;AAC3E,+DAA+D;AAC/D,IAAI,cAAc,CAAC,UAAU,IAAI,EAAE,UAAU;IAAE,4BAA4B;AAAK,CAAC;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,wEAAwE;IACxE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,wEAAwE;IACxE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI,IAAI,EAAE;IACrC,6EAA6E;IAC7E,sEAAsE;IACtE,iGAAiG;IACjG,8GAA8G;IAC9G,iHAAiH;IACjH,+CAA+C;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,YAAY,MAAM;YAClB,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW,UAAU;gBAAC;aAAQ;YAC/D,MAAM,OAAO,WAAW,MAAM,CAAC,CAAC,KAAO,OAAO,OAAO,UAAU,GAAG,CAAC,CAAC,KAAO,GAAG,KAAK,CAAC;YACpF,MAAM,WAAW,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,IAAI,MAAM,IAAI,OAAO,EAAE;YACnE,OAAO;gBAAC;gBAAM;aAAS;QAC3B;QACA,OAAO;YAAC,EAAE;YAAE,EAAE;SAAC;IACnB,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,MAAM,OAAO,aAAa,cAAc,WAAW;QACzD,MAAM,SAAS,SAAS,UAAU;QAClC,IAAI,YAAY,MAAM;YAClB,MAAM,cAAc,CAAC;gBACjB,gBAAgB,OAAO,GAAG,MAAM,OAAO,IAAI,MAAM,OAAO,IAAI,MAAM,QAAQ;gBAC1E,MAAM,gBAAgB,CAAC,CAAC,gBAAgB,OAAO,IAAK,gBAAgB,OAAO,IAAI,CAAC,QAAQ,0BAA0B,AAAC,KAC/G,eAAe;gBACnB,IAAI,eAAe;oBACf,OAAO;gBACX;gBACA,MAAM,YAAY,aAAa,MAAM,IAAI,EAAE;gBAC3C,YAAY,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU;gBACxC,IAAI,cAAc,UAAU,YAAY,OAAO,EAAE,QAAQ;oBACrD,MAAM,cAAc;oBACpB,cAAc;gBAClB;YACJ;YACA,MAAM,YAAY,CAAC;gBACf,MAAM,gBAAgB,CAAC,CAAC,gBAAgB,OAAO,IAAK,gBAAgB,OAAO,IAAI,CAAC,QAAQ,0BAA0B,AAAC,KAC/G,eAAe;gBACnB,IAAI,eAAe;oBACf,OAAO;gBACX;gBACA,MAAM,YAAY,aAAa,MAAM,IAAI,EAAE;gBAC3C,IAAI,cAAc,UAAU,YAAY,OAAO,EAAE,OAAO;oBACpD,cAAc;oBACd,YAAY,OAAO,CAAC,KAAK;gBAC7B,OACK;oBACD,YAAY,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU;gBAC/C;gBACA,0MAA0M;gBAC1M,IAAI,MAAM,GAAG,KAAK,QAAQ;oBACtB,YAAY,OAAO,CAAC,KAAK;gBAC7B;gBACA,gBAAgB,OAAO,GAAG;YAC9B;YACA,MAAM,eAAe;gBACjB,YAAY,OAAO,CAAC,KAAK;gBACzB,cAAc;YAClB;YACA,QAAQ,iBAAiB,WAAW;YACpC,QAAQ,iBAAiB,SAAS;YAClC,OAAO,gBAAgB,CAAC,QAAQ;YAChC,OAAO;gBACH,QAAQ,oBAAoB,WAAW;gBACvC,QAAQ,oBAAoB,SAAS;gBACrC,OAAO,mBAAmB,CAAC,QAAQ;YACvC;QACJ;IACJ,GAAG;QAAC;QAAS;KAAc;IAC3B,OAAO;AACX;AACA,QAAQ;AACR,SAAS,cAAc,QAAQ,EAAE,WAAW,EAAE,IAAI;IAC9C,OAAQ,QACJ,4DAA4D;IAC5D,sEAAsE;IACtE,+EAA+E;KAC9E,MAAM,CAAC,CAAC,OAAS,QAAQ,KAAK,MAAM,KAAK,YAAY,IAAI,CAC1D,kEAAkE;IAClE,mDAAmD;KAClD,IAAI,CAAC,CAAC,OAAS,KAAK,KAAK,CAAC,CAAC,IAAM,YAAY,GAAG,CAAC;AAC1D;AACA,SAAS,aAAa,SAAS,EAAE,WAAW;IACxC,OAAO,YAAY,QAAQ,CAAC,aAAa,SAAS;AACtD;AAEA,SAAS,qBAAqB,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,UAAU;IACjE,MAAM,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ;IACjD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,MAAM,aAAa,cAAc,GAAG,CAAC;IACrC,MAAM,qBAAqB,0BAA0B,YAAY;IACjE,OAAO,qBAAqB,YAAY,eAAe;QACnD,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC;QACzC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC;QACzC,GAAG,CAAC,UAAU,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,gBAAgB,EAAE,KAAK,IAAI,OAAO,CAAC,IAAI;IACnH,GAAG;AACP;AACA,SAAS,4BAA4B,aAAa,EAAE,UAAU,EAAE,WAAW;IACvE,cAAc,OAAO,CAAC,CAAC;QACnB,MAAM,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ;QACjD,IAAI,YAAY,CAAC,cAAc,GAAG,CAAC,WAAW;YAC1C,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS,UAAU,CAAC;QACvD;QACA,IAAI,YAAY,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE;YACpC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,qBAAqB,MAAM,eAAe;gBAC1D,GAAG,KAAK,QAAQ;gBAChB,GAAG,IAAI,CAAC,gBAAgB,EAAE,KAAK;YACnC,GAAG;YACH,KAAK,gBAAgB,GAAG;gBACpB;gBACA;YACJ;YACA,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG;YAC1B,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC,EAAE;gBACxB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;YACrC;QACJ;IACJ;AACJ;AACA,SAAS,oBAAoB,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,oBAAoB;IAC/E,MAAM,oBAAoB,IAAI;IAC9B,MAAM,cAAc,CAAC;IACrB,MAAM,gBAAgB,uBAAuB,OAAO;IACpD,MAAM,OAAO,CAAC,CAAC;QACX,MAAM,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,QAAQ,GAAG,gBAAgB,CAAC;QACzF,MAAM,gBAAgB,cAAc,GAAG,CAAC,KAAK,EAAE;QAC/C,MAAM,YAAY;YACd,GAAG,IAAI;YACP,kBAAkB;gBACd,GAAG,KAAK,QAAQ,CAAC,CAAC;gBAClB,GAAG,KAAK,QAAQ,CAAC,CAAC;YACtB;QACJ;QACA,MAAM,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ;QACjD,IAAI,UAAU;YACV,WAAW,CAAC,SAAS,GAAG;QAC5B;QACA,MAAM,oBAAoB,eAAe,QAAQ,eAAe,SAAS,KAAK,IAAI;QAClF,OAAO,cAAc,CAAC,WAAW,iBAAiB;YAC9C,YAAY;YACZ,OAAO;gBACH,cAAc,oBAAoB,YAAY,eAAe,CAAC,gBAAgB,EAAE;gBAChF;YACJ;QACJ;QACA,kBAAkB,GAAG,CAAC,KAAK,EAAE,EAAE;IACnC;IACA,4BAA4B,mBAAmB,YAAY;IAC3D,OAAO;AACX;AACA,SAAS,QAAQ,GAAG,EAAE,UAAU,CAAC,CAAC;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,EAAE,UAAU,EAAG,GAAG;IAC1H,MAAM,mBAAmB,QAAQ,OAAO,IAAI,CAAC,qBAAqB;IAClE,MAAM,gBAAgB,UAAU;IAChC,IAAI,iBAAiB,CAAC,oBAAoB,CAAC,QAAQ,OAAO,GAAG;QACzD,MAAM,QAAQ,WAAW,MAAM,CAAC,CAAC;YAC7B,MAAM,YAAY,QAAQ,kBAAkB,GAAG,EAAE,KAAK,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM;YAC9E,IAAI,QAAQ,KAAK,EAAE,QAAQ;gBACvB,OAAO,aAAa,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,aAAe,WAAW,EAAE,KAAK,EAAE,EAAE;YACjF;YACA,OAAO;QACX;QACA,MAAM,mBAAmB,MAAM,KAAK,CAAC,CAAC,IAAM,EAAE,KAAK,IAAI,EAAE,MAAM;QAC/D,IAAI,MAAM,MAAM,GAAG,KAAK,kBAAkB;YACtC,MAAM,SAAS,eAAe,OAAO;YACrC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,qBAAqB,QAAQ,OAAO,QAAQ,QAAQ,OAAO,IAAI,SAAS,QAAQ,OAAO,IAAI,SAAS,QAAQ,OAAO,IAAI;YAC9I,MAAM,gBAAgB,0LAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC;YACzD,IAAI,OAAO,QAAQ,QAAQ,KAAK,YAAY,QAAQ,QAAQ,GAAG,GAAG;gBAC9D,OAAO,SAAS,CAAC,gBAAgB,aAAa,QAAQ,QAAQ,GAAG;YACrE,OACK;gBACD,OAAO,SAAS,CAAC,aAAa;YAClC;YACA,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,SAAS,oCAAoC,WAAW,EAAE,aAAa;IACnE,YAAY,OAAO,CAAC,CAAC;QACjB,MAAM,OAAO,cAAc,GAAG,CAAC,OAAO,EAAE;QACxC,IAAI,MAAM;YACN,cAAc,GAAG,CAAC,KAAK,EAAE,EAAE;gBACvB,GAAG,IAAI;gBACP,CAAC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;gBACxC,UAAU,OAAO,QAAQ;YAC7B;QACJ;IACJ;IACA,OAAO,IAAI,IAAI;AACnB;AACA,SAAS,oCAAoC,WAAW,EAAE,KAAK;IAC3D,OAAO,MAAM,GAAG,CAAC,CAAC;QACd,MAAM,SAAS,YAAY,IAAI,CAAC,CAAC,SAAW,OAAO,EAAE,KAAK,EAAE,EAAE;QAC9D,IAAI,QAAQ;YACR,EAAE,QAAQ,GAAG,OAAO,QAAQ;QAChC;QACA,OAAO;IACX;AACJ;AACA,SAAS,8BAA8B,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,EAAE;IAC3E,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG;IACjG,IAAI,cAAc,QAAQ;QACtB,IAAI,iBAAiB;YACjB,IAAI;gBAAE,eAAe,oCAAoC,cAAc;YAAe;QAC1F;QACA,gBAAgB;IACpB;IACA,IAAI,cAAc,QAAQ;QACtB,IAAI,iBAAiB;YACjB,IAAI;gBAAE,OAAO,oCAAoC,cAAc;YAAO;QAC1E;QACA,gBAAgB;IACpB;AACJ;AAEA,gEAAgE;AAChE,MAAM,OAAO,KAAQ;AACrB,MAAM,wBAAwB;IAC1B,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS,IAAM;IACf,aAAa;IACb,aAAa,IAAM,CAAC;YAAE,GAAG;YAAG,GAAG;YAAG,MAAM;QAAE,CAAC;IAC3C,SAAS,IAAM;IACf,WAAW;IACX,WAAW;IACX,SAAS,CAAC,WAAa;IACvB,sBAAsB,CAAC,WAAa;IACpC,sBAAsB,CAAC,WAAa;IACpC,qBAAqB;AACzB;AACA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,WAAW;IAC9B,CAAC;AACD,MAAM,oBAAoB;IACtB,MAAM,QAAQ;IACd,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAC5D,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACpC,IAAI,eAAe,QAAQ;YACvB,OAAO;gBACH,QAAQ,CAAC,UAAY,OAAO,OAAO,CAAC,gBAAgB,aAAa,SAAS,WAAW;gBACrF,SAAS,CAAC,UAAY,OAAO,OAAO,CAAC,gBAAgB,aAAa,SAAS,WAAW,IAAI;gBAC1F,QAAQ,CAAC,WAAW,UAAY,OAAO,OAAO,CAAC,gBAAgB,aAAa,SAAS,WAAW;gBAChG,SAAS,IAAM,MAAM,QAAQ,GAAG,SAAS,CAAC,EAAE;gBAC5C,aAAa,CAAC,WAAW;oBACrB,MAAM,CAAC,GAAG,GAAG,KAAK,GAAG,MAAM,QAAQ,GAAG,SAAS;oBAC/C,MAAM,gBAAgB,0LAAA,CAAA,eAAY,CAC7B,SAAS,CAAC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAC3C,KAAK,CAAC,UAAU,IAAI,IAAI;oBAC7B,OAAO,SAAS,CAAC,gBAAgB,aAAa,SAAS,WAAW;gBACtE;gBACA,aAAa;oBACT,MAAM,CAAC,GAAG,GAAG,KAAK,GAAG,MAAM,QAAQ,GAAG,SAAS;oBAC/C,OAAO;wBAAE;wBAAG;wBAAG;oBAAK;gBACxB;gBACA,SAAS,CAAC,UAAY,QAAQ,MAAM,QAAQ,EAAE;gBAC9C,WAAW,CAAC,GAAG,GAAG;oBACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;oBACjD,MAAM,WAAW,OAAO,SAAS,SAAS,cAAc,QAAQ,IAAI,GAAG;oBACvE,MAAM,UAAU,QAAQ,IAAI,IAAI;oBAChC,MAAM,UAAU,SAAS,IAAI,IAAI;oBACjC,MAAM,YAAY,0LAAA,CAAA,eAAY,CAAC,SAAS,CAAC,SAAS,SAAS,KAAK,CAAC;oBACjE,OAAO,SAAS,CAAC,gBAAgB,aAAa,SAAS,WAAW;gBACtE;gBACA,WAAW,CAAC,QAAQ;oBAChB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;oBAC1D,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,qBAAqB,QAAQ,OAAO,QAAQ,SAAS,SAAS,SAAS,WAAW;oBACzG,MAAM,YAAY,0LAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC;oBACrD,OAAO,SAAS,CAAC,gBAAgB,aAAa,SAAS,WAAW;gBACtE;gBACA,0CAA0C;gBAC1C,SAAS,CAAC;oBACN,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ;oBAC1D,QAAQ,IAAI,CAAC;oBACb,OAAO,qBAAqB,UAAU,WAAW,YAAY;gBACjE;gBACA,sBAAsB,CAAC;oBACnB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;oBACnE,IAAI,CAAC,SAAS;wBACV,OAAO;oBACX;oBACA,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,qBAAqB;oBAC1D,MAAM,mBAAmB;wBACrB,GAAG,SAAS,CAAC,GAAG;wBAChB,GAAG,SAAS,CAAC,GAAG;oBACpB;oBACA,OAAO,qBAAqB,kBAAkB,WAAW,YAAY;gBACzE;gBACA,sBAAsB,CAAC;oBACnB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;oBAC7C,IAAI,CAAC,SAAS;wBACV,OAAO;oBACX;oBACA,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,qBAAqB;oBAC1D,MAAM,mBAAmB,qBAAqB,UAAU;oBACxD,OAAO;wBACH,GAAG,iBAAiB,CAAC,GAAG;wBACxB,GAAG,iBAAiB,CAAC,GAAG;oBAC5B;gBACJ;gBACA,qBAAqB;YACzB;QACJ;QACA,OAAO;IACX,GAAG;QAAC;QAAQ;KAAY;IACxB,OAAO;AACX;AAEA,+DAA+D,GAC/D,SAAS;IACL,MAAM,iBAAiB;IACvB,MAAM,QAAQ;IACd,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,OAAO,MACF,QAAQ,GACR,QAAQ,GACR,GAAG,CAAC,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;YAAC,CAAC;IAC7B,GAAG,EAAE;IACL,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzB,OAAO,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC;IAC9C,GAAG,EAAE;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,MAAM,EAAE,QAAQ,EAAE,EAAE,GAAG,MAAM,QAAQ;QACrC,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;gBAAE,GAAG,CAAC;YAAC,CAAC;IACrC,GAAG,EAAE;IACL,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzB,MAAM,EAAE,QAAQ,EAAE,EAAE,GAAG,MAAM,QAAQ;QACrC,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACtC,GAAG,EAAE;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;QAC7E,MAAM,QAAQ;QACd,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,iBAAiB;YACjB,SAAS;QACb,OACK,IAAI,eAAe;YACpB,MAAM,UAAU,UAAU,MAAM,KAAK,IAC/B,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;oBAAE,MAAM;oBAAU,IAAI,KAAK,EAAE;gBAAC,CAAC,KACpD,UAAU,GAAG,CAAC,CAAC,OAAS,CAAC;oBAAE,MAAM;oBAAM,MAAM;gBAAQ,CAAC;YAC5D,cAAc;QAClB;IACJ,GAAG,EAAE;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;QAC/E,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,iBAAiB;YACjB,SAAS;QACb,OACK,IAAI,eAAe;YACpB,MAAM,UAAU,UAAU,MAAM,KAAK,IAC/B,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;oBAAE,MAAM;oBAAU,IAAI,KAAK,EAAE;gBAAC,CAAC,KACpD,UAAU,GAAG,CAAC,CAAC,OAAS,CAAC;oBAAE,MAAM;oBAAM,MAAM;gBAAQ,CAAC;YAC5D,cAAc;QAClB;IACJ,GAAG,EAAE;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,QAAQ,MAAM,OAAO,CAAC,WAAW,UAAU;YAAC;SAAQ;QAC1D,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;QAC7E,IAAI,iBAAiB;YACjB,MAAM,eAAe;YACrB,MAAM,YAAY;mBAAI;mBAAiB;aAAM;YAC7C,SAAS;QACb,OACK,IAAI,eAAe;YACpB,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;oBAAE,MAAM;oBAAM,MAAM;gBAAM,CAAC;YAChE,cAAc;QAClB;IACJ,GAAG,EAAE;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,YAAY,MAAM,OAAO,CAAC,WAAW,UAAU;YAAC;SAAQ;QAC9D,MAAM,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;QAC/E,IAAI,iBAAiB;YACjB,SAAS;mBAAI;mBAAU;aAAU;QACrC,OACK,IAAI,eAAe;YACpB,MAAM,UAAU,UAAU,GAAG,CAAC,CAAC,OAAS,CAAC;oBAAE,MAAM;oBAAM,MAAM;gBAAM,CAAC;YACpE,cAAc;QAClB;IACJ,GAAG,EAAE;IACL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ;QAC1D,MAAM,CAAC,GAAG,GAAG,KAAK,GAAG;QACrB,OAAO;YACH,OAAO,WAAW,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,GAAG,CAAC;gBAAC,CAAC;YACtC,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,GAAG,CAAC;gBAAC,CAAC;YACjC,UAAU;gBACN;gBACA;gBACA;YACJ;QACJ;IACJ,GAAG,EAAE;IACL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,EAAE,OAAO,YAAY,EAAE,OAAO,YAAY,EAAE;QAC5E,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAG,GAAG,MAAM,QAAQ;QACxJ,MAAM,UAAU,CAAC,gBAAgB,EAAE,EAAE,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;QAC1D,MAAM,UAAU,CAAC,gBAAgB,EAAE,EAAE,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;QAC1D,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,KAAK;YAC1C,MAAM,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ;YACjD,MAAM,YAAY,CAAC,QAAQ,QAAQ,CAAC,KAAK,EAAE,KAAK,YAAY,IAAI,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACrF,MAAM,YAAY,OAAO,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG;YACzE,IAAI,aAAa,CAAC,QAAQ,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,GAAG;gBACvD,IAAI,IAAI,CAAC;YACb;YACA,OAAO;QACX,GAAG,EAAE;QACL,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,IAAO,OAAO,EAAE,SAAS,KAAK,YAAY,EAAE,SAAS,GAAG;QAC7F,MAAM,kBAAkB,eAAe,MAAM,CAAC,CAAC,IAAM,QAAQ,QAAQ,CAAC,EAAE,EAAE;QAC1E,IAAI,iBAAiB,iBAAiB;YAClC,MAAM,iBAAiB,kBAAkB,eAAe;YACxD,MAAM,gBAAgB;mBAAI;mBAAoB;aAAe;YAC7D,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,KAAK;gBAC/C,IAAI,CAAC,IAAI,QAAQ,CAAC,KAAK,EAAE,GAAG;oBACxB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACpB;gBACA,OAAO;YACX,GAAG,EAAE;YACL,IAAI,mBAAmB,iBAAiB;gBACpC,IAAI,iBAAiB;oBACjB,MAAM,QAAQ,CAAC;wBACX,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,CAAC,gBAAgB,QAAQ,CAAC,EAAE,EAAE;oBAC7D;gBACJ;gBACA,IAAI,iBAAiB;oBACjB,cAAc,OAAO,CAAC,CAAC;wBACnB,cAAc,MAAM,CAAC,KAAK,EAAE;oBAChC;oBACA,MAAM,QAAQ,CAAC;wBACX,eAAe,IAAI,IAAI;oBAC3B;gBACJ;YACJ;YACA,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC5B,gBAAgB;gBAChB,IAAI,eAAe;oBACf,cAAc,gBAAgB,GAAG,CAAC,CAAC,KAAO,CAAC;4BACvC;4BACA,MAAM;wBACV,CAAC;gBACL;YACJ;YACA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC1B,gBAAgB;gBAChB,IAAI,eAAe;oBACf,MAAM,cAAc,cAAc,GAAG,CAAC,CAAC,IAAM,CAAC;4BAAE,IAAI,EAAE,EAAE;4BAAE,MAAM;wBAAS,CAAC;oBAC1E,cAAc;gBAClB;YACJ;QACJ;IACJ,GAAG,EAAE;IACL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,MAAM,SAAS,aAAa;QAC5B,MAAM,OAAO,SAAS,OAAO,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE;QAC7E,IAAI,CAAC,UAAU,CAAC,MAAM;YAClB,OAAO;gBAAC;gBAAM;gBAAM;aAAO;QAC/B;QACA,MAAM,WAAW,SAAS,aAAa,WAAW;QAClD,OAAO;YAAC;YAAU;YAAM;SAAO;IACnC,GAAG,EAAE;IACL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAY,YAAY,IAAI,EAAE;QACpE,MAAM,CAAC,UAAU,MAAM,OAAO,GAAG,YAAY;QAC7C,IAAI,CAAC,UAAU;YACX,OAAO,EAAE;QACb;QACA,OAAO,CAAC,SAAS,MAAM,QAAQ,GAAG,QAAQ,EAAE,EAAE,MAAM,CAAC,CAAC;YAClD,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,KAAK,EAAE,IAAI,CAAC,EAAE,gBAAgB,GAAG;gBACtD,OAAO;YACX;YACA,MAAM,eAAe,WAAW;YAChC,MAAM,kBAAkB,mBAAmB,cAAc;YACzD,MAAM,mBAAmB,aAAa,kBAAkB;YACxD,OAAO,oBAAoB,mBAAmB,SAAS,KAAK,GAAG,SAAS,MAAM;QAClF;IACJ,GAAG,EAAE;IACL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAY,MAAM,YAAY,IAAI;QACtE,MAAM,CAAC,SAAS,GAAG,YAAY;QAC/B,IAAI,CAAC,UAAU;YACX,OAAO;QACX;QACA,MAAM,kBAAkB,mBAAmB,UAAU;QACrD,MAAM,mBAAmB,aAAa,kBAAkB;QACxD,OAAO,oBAAoB,mBAAmB,SAAS,KAAK,GAAG,SAAS,MAAM;IAClF,GAAG,EAAE;IACL,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACX,OAAO;YACH,GAAG,cAAc;YACjB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;AACL;AAEA,MAAM,mBAAmB;IAAE,4BAA4B;AAAM;AAC7D,IAAI,sBAAsB,CAAC,EAAE,aAAa,EAAE,qBAAqB,EAAE;IAC/D,MAAM,QAAQ;IACd,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,MAAM,mBAAmB,YAAY,eAAe;IACpD,MAAM,2BAA2B,YAAY;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,kBAAkB;YAClB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ;YAC1C,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;YAC/D,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ;YAC1D,eAAe;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC5D,MAAM,QAAQ,CAAC;gBAAE,sBAAsB;YAAM;QACjD;IACJ,GAAG;QAAC;KAAiB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,sBAAsB;QAAyB;IACpE,GAAG;QAAC;KAAyB;AACjC;AAEA,SAAS,iBAAiB,YAAY;IAClC,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI;QACJ,MAAM,mBAAmB;YACrB,IAAI,CAAC,aAAa,OAAO,EAAE;gBACvB;YACJ;YACA,MAAM,OAAO,cAAc,aAAa,OAAO;YAC/C,IAAI,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;gBACvC,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,aAAa,CAAC,WAAW;YAC/D;YACA,MAAM,QAAQ,CAAC;gBAAE,OAAO,KAAK,KAAK,IAAI;gBAAK,QAAQ,KAAK,MAAM,IAAI;YAAI;QAC1E;QACA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,IAAI,aAAa,OAAO,EAAE;YACtB,iBAAiB,IAAI,eAAe,IAAM;YAC1C,eAAe,OAAO,CAAC,aAAa,OAAO;QAC/C;QACA,OAAO;YACH,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,kBAAkB,aAAa,OAAO,EAAE;gBACxC,eAAe,SAAS,CAAC,aAAa,OAAO;YACjD;QACJ;IACJ,GAAG,EAAE;AACT;AAEA,MAAM,iBAAiB;IACnB,UAAU;IACV,OAAO;IACP,QAAQ;IACR,KAAK;IACL,MAAM;AACV;AAEA,oDAAoD,GACpD,MAAM,cAAc,CAAC,cAAc,iBAAmB,aAAa,CAAC,KAAK,eAAe,CAAC,IAAI,aAAa,CAAC,KAAK,eAAe,CAAC,IAAI,aAAa,IAAI,KAAK,eAAe,CAAC;AAC1K,MAAM,uBAAuB,CAAC,iBAAmB,CAAC;QAC9C,GAAG,eAAe,CAAC;QACnB,GAAG,eAAe,CAAC;QACnB,MAAM,eAAe,CAAC;IAC1B,CAAC;AACD,MAAM,qBAAqB,CAAC,OAAO,YAAc,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW;AACrF,MAAM,kBAAkB,CAAC,WAAW,aAAe,eAAe,KAAK,MAAM,OAAO,CAAC,cAAc,UAAU,QAAQ,CAAC;AACtH,MAAM,aAAa,CAAC;IAChB,MAAM,SAAS,MAAM,OAAO,IAAI,YAAY,KAAK;IACjD,OAAO,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,KAAK,IAAI,OAAO,MAAM,SAAS,GAAG,IAAI,KAAK,IAAI;AAC1F;AACA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,WAAW;QAC1B,eAAe,EAAE,aAAa;QAC9B,qBAAqB,EAAE,mBAAmB;IAC9C,CAAC;AACD,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,iBAAiB,EAAE,eAAe,IAAI,EAAE,cAAc,IAAI,EAAE,cAAc,KAAK,EAAE,mBAAmB,GAAG,EAAE,kBAAkB,gBAAgB,IAAI,EAAE,oBAAoB,IAAI,EAAE,kBAAkB,EAAE,YAAY,IAAI,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,qBAAqB,EAAE,mBAAmB,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,cAAc,EAAG;IAClZ,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IACrB,MAAM,QAAQ;IACd,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC1C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;QAAG,MAAM;IAAE;IACnD,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAChG,MAAM,2BAA2B,YAAY;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC9B,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,SAAS,OAAO,EAAE;YAClB,MAAM,OAAO,SAAS,OAAO,CAAC,qBAAqB;YACnD,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,OAAI,AAAD,IAAI,WAAW,CAAC;gBAAC;gBAAS;aAAQ,EAAE,eAAe,CAAC;YAC9E,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE,SAAS,OAAO,EAAE,IAAI,CAAC;YAChD,MAAM,mBAAmB,0LAAA,CAAA,eAAY,CAChC,SAAS,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,EAC9C,KAAK,CAAC,MAAM,gBAAgB,IAAI,EAAE,SAAS;YAChD,MAAM,SAAS;gBACX;oBAAC;oBAAG;iBAAE;gBACN;oBAAC,KAAK,KAAK;oBAAE,KAAK,MAAM;iBAAC;aAC5B;YACD,MAAM,uBAAuB,eAAe,SAAS,GAAG,kBAAkB,QAAQ;YAClF,eAAe,SAAS,CAAC,WAAW;YACpC,eAAe,UAAU,CAAC;YAC1B,MAAM,QAAQ,CAAC;gBACX,QAAQ;gBACR,aAAa;gBACb,eAAe,UAAU,EAAE,CAAC;gBAC5B,qGAAqG;gBACrG,WAAW;oBAAC,qBAAqB,CAAC;oBAAE,qBAAqB,CAAC;oBAAE,qBAAqB,CAAC;iBAAC;gBACnF,SAAS,SAAS,OAAO,CAAC,OAAO,CAAC;YACtC;QACJ;IACJ,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,eAAe,QAAQ;YACvB,IAAI,eAAe,CAAC,4BAA4B,CAAC,qBAAqB;gBAClE,YAAY,EAAE,CAAC,cAAc,CAAC;oBAC1B,IAAI,mBAAmB,OAAO,mBAAmB;wBAC7C,OAAO;oBACX;oBACA,MAAM,cAAc;oBACpB,MAAM,wBAAwB;oBAC9B,MAAM,cAAc,YAAY,QAAQ,CAAC,UAAU,CAAC,IAAI;oBACxD,iEAAiE;oBACjE,IAAI,MAAM,OAAO,IAAI,aAAa;wBAC9B,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAO,AAAD,EAAE;wBACtB,MAAM,aAAa,WAAW;wBAC9B,MAAM,OAAO,cAAc,KAAK,GAAG,CAAC,GAAG;wBACvC,aAAa;wBACb,OAAO,OAAO,CAAC,aAAa,MAAM,OAAO;wBACzC;oBACJ;oBACA,mCAAmC;oBACnC,oDAAoD;oBACpD,MAAM,iBAAiB,MAAM,SAAS,KAAK,IAAI,KAAK;oBACpD,IAAI,SAAS,oBAAoB,gBAAgB,QAAQ,GAAG,IAAI,MAAM,MAAM,GAAG;oBAC/E,IAAI,SAAS,oBAAoB,gBAAgB,UAAU,GAAG,IAAI,MAAM,MAAM,GAAG;oBACjF,iEAAiE;oBACjE,IAAI,CAAC,aAAa,MAAM,QAAQ,IAAI,oBAAoB,gBAAgB,QAAQ,EAAE;wBAC9E,SAAS,MAAM,MAAM,GAAG;wBACxB,SAAS;oBACb;oBACA,OAAO,WAAW,CAAC,aAAa,CAAC,CAAC,SAAS,WAAW,IAAI,kBAAkB,CAAC,CAAC,SAAS,WAAW,IAAI,kBACtG,aAAa;oBACb;wBAAE,UAAU;oBAAK;oBACjB,MAAM,eAAe,qBAAqB,YAAY,QAAQ,CAAC;oBAC/D,MAAM,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,MAAM,QAAQ;oBACvF,aAAa,iBAAiB,OAAO;oBACrC,iEAAiE;oBACjE,2DAA2D;oBAC3D,yFAAyF;oBACzF,IAAI,CAAC,eAAe,OAAO,EAAE;wBACzB,eAAe,OAAO,GAAG;wBACzB,cAAc,OAAO;wBACrB,wBAAwB;oBAC5B;oBACA,IAAI,eAAe,OAAO,EAAE;wBACxB,SAAS,OAAO;wBAChB,mBAAmB;wBACnB,iBAAiB,OAAO,GAAG,WAAW;4BAClC,YAAY,OAAO;4BACnB,sBAAsB;4BACtB,eAAe,OAAO,GAAG;wBAC7B,GAAG;oBACP;gBACJ,GAAG;oBAAE,SAAS;gBAAM;YACxB,OACK,IAAI,OAAO,kBAAkB,aAAa;gBAC3C,YAAY,EAAE,CAAC,cAAc,SAAU,KAAK,EAAE,CAAC;oBAC3C,iFAAiF;oBACjF,MAAM,eAAe,CAAC,oBAAoB,MAAM,IAAI,KAAK,WAAW,CAAC,MAAM,OAAO;oBAClF,IAAI,gBAAgB,mBAAmB,OAAO,mBAAmB;wBAC7D,OAAO;oBACX;oBACA,MAAM,cAAc;oBACpB,cAAc,IAAI,CAAC,IAAI,EAAE,OAAO;gBACpC,GAAG;oBAAE,SAAS;gBAAM;YACxB;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ;YACR,OAAO,EAAE,CAAC,SAAS,CAAC;gBAChB,IAAI,CAAC,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,QAAQ,EAAE;oBAClD,OAAO;gBACX;gBACA,yEAAyE;gBACzE,YAAY,OAAO,GAAG,MAAM,WAAW,EAAE;gBACzC,MAAM,EAAE,qBAAqB,EAAE,GAAG,MAAM,QAAQ;gBAChD,MAAM,gBAAgB,qBAAqB,MAAM,SAAS;gBAC1D,mBAAmB,OAAO,GAAG;gBAC7B,cAAc,OAAO,GAAG;gBACxB,IAAI,MAAM,WAAW,EAAE,SAAS,aAAa;oBACzC,MAAM,QAAQ,CAAC;wBAAE,cAAc;oBAAK;gBACxC;gBACA,wBAAwB;gBACxB,cAAc,MAAM,WAAW,EAAE;YACrC;QACJ;IACJ,GAAG;QAAC;QAAQ;KAAY;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ;YACR,IAAI,uBAAuB,CAAC,mBAAmB,OAAO,EAAE;gBACpD,OAAO,EAAE,CAAC,QAAQ;YACtB,OACK,IAAI,CAAC,qBAAqB;gBAC3B,OAAO,EAAE,CAAC,QAAQ,CAAC;oBACf,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,QAAQ;oBAC3C,MAAM,QAAQ,CAAC;wBAAE,WAAW;4BAAC,MAAM,SAAS,CAAC,CAAC;4BAAE,MAAM,SAAS,CAAC,CAAC;4BAAE,MAAM,SAAS,CAAC,CAAC;yBAAC;oBAAC;oBACtF,2BAA2B,OAAO,GAAG,CAAC,CAAC,CAAC,qBAAqB,gBAAgB,WAAW,YAAY,OAAO,IAAI,EAAE;oBACjH,IAAI,CAAC,UAAU,gBAAgB,KAAK,CAAC,MAAM,WAAW,EAAE,UAAU;wBAC9D,MAAM,gBAAgB,qBAAqB,MAAM,SAAS;wBAC1D,mBAAmB;wBACnB,SAAS,MAAM,WAAW,EAAE;oBAChC;gBACJ;YACJ;QACJ;IACJ,GAAG;QAAC;QAAqB;QAAQ;QAAQ;QAAW;KAAkB;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ;YACR,OAAO,EAAE,CAAC,OAAO,CAAC;gBACd,IAAI,CAAC,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,QAAQ,EAAE;oBAClD,OAAO;gBACX;gBACA,MAAM,EAAE,mBAAmB,EAAE,GAAG,MAAM,QAAQ;gBAC9C,mBAAmB,OAAO,GAAG;gBAC7B,MAAM,QAAQ,CAAC;oBAAE,cAAc;gBAAM;gBACrC,IAAI,qBACA,gBAAgB,WAAW,YAAY,OAAO,IAAI,MAClD,CAAC,2BAA2B,OAAO,EAAE;oBACrC,kBAAkB,MAAM,WAAW;gBACvC;gBACA,2BAA2B,OAAO,GAAG;gBACrC,IAAI,CAAC,aAAa,mBAAmB,KAAK,YAAY,cAAc,OAAO,EAAE,MAAM,SAAS,GAAG;oBAC3F,MAAM,gBAAgB,qBAAqB,MAAM,SAAS;oBAC1D,cAAc,OAAO,GAAG;oBACxB,aAAa,QAAQ,OAAO;oBAC5B,QAAQ,OAAO,GAAG,WAAW;wBACzB,sBAAsB;wBACtB,YAAY,MAAM,WAAW,EAAE;oBACnC,GAAG,cAAc,MAAM;gBAC3B;YACJ;QACJ;IACJ,GAAG;QAAC;QAAQ;QAAa;QAAW;QAAW;KAAkB;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,QAAQ;YACR,OAAO,MAAM,CAAC,CAAC;gBACX,MAAM,aAAa,4BAA4B;gBAC/C,MAAM,YAAY,eAAe,MAAM,OAAO;gBAC9C,IAAI,CAAC,cAAc,QAAS,MAAM,OAAO,CAAC,cAAc,UAAU,QAAQ,CAAC,EAAG,KAC1E,MAAM,MAAM,KAAK,KACjB,MAAM,IAAI,KAAK,eACf,CAAC,mBAAmB,OAAO,uBAAuB,mBAAmB,OAAO,mBAAmB,GAAG;oBAClG,OAAO;gBACX;gBACA,+DAA+D;gBAC/D,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,qBAAqB,CAAC,aAAa;oBACjF,OAAO;gBACX;gBACA,uDAAuD;gBACvD,IAAI,qBAAqB;oBACrB,OAAO;gBACX;gBACA,yEAAyE;gBACzE,IAAI,CAAC,qBAAqB,MAAM,IAAI,KAAK,YAAY;oBACjD,OAAO;gBACX;gBACA,wFAAwF;gBACxF,IAAI,mBAAmB,OAAO,qBAAqB,MAAM,IAAI,KAAK,SAAS;oBACvE,OAAO;gBACX;gBACA,sFAAsF;gBACtF,IAAI,mBAAmB,OAAO,mBAC1B,CAAC,MAAM,IAAI,KAAK,WAAY,eAAe,MAAM,IAAI,KAAK,WAAW,CAAC,wBAAyB,GAAG;oBAClG,OAAO;gBACX;gBACA,IAAI,CAAC,eAAe,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;oBACzD,OAAO;gBACX;gBACA,wEAAwE;gBACxE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,MAAM,IAAI,KAAK,SAAS;oBACrE,OAAO;gBACX;gBACA,mFAAmF;gBACnF,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,YAAY,GAAG;oBAC3E,OAAO;gBACX;gBACA,mDAAmD;gBACnD,IAAI,MAAM,OAAO,CAAC,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK,aAAa;oBAC7F,OAAO;gBACX;gBACA,kEAAkE;gBAClE,MAAM,gBAAgB,AAAC,MAAM,OAAO,CAAC,cAAc,UAAU,QAAQ,CAAC,MAAM,MAAM,KAAM,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;gBACzH,6BAA6B;gBAC7B,OAAO,CAAC,CAAC,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,OAAO,KAAK;YACzD;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW;QAAwB,KAAK;QAAU,OAAO;IAAe,GAAG;AACpH;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,qBAAqB,EAAE,mBAAmB;QAC1C,mBAAmB,EAAE,iBAAiB;IAC1C,CAAC;AACD,SAAS;IACL,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAC/E,MAAM,WAAW,uBAAuB;IACxC,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW;QAA+C,OAAO;YAC9F,OAAO,kBAAkB,KAAK;YAC9B,QAAQ,kBAAkB,MAAM;YAChC,WAAW,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC,GAAG,CAAC;QAC9E;IAAE;AACV;AAEA,SAAS,mBAAmB,GAAG,EAAE,UAAU;IACvC,MAAM,WAAW,WAAW,UAAU,IAAI,WAAW,QAAQ;IAC7D,MAAM,SAAS,IAAI,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACxC,IAAI,QAAQ;QACR,MAAM,cAAc,WAAW,QAAQ,CAAC,CAAC,GAAG,WAAW,KAAK,GAAG,OAAO,KAAK;QAC3E,MAAM,eAAe,WAAW,QAAQ,CAAC,CAAC,GAAG,WAAW,MAAM,GAAG,OAAO,MAAM;QAC9E,IAAI,cAAc,KAAK,eAAe,KAAK,WAAW,QAAQ,CAAC,CAAC,GAAG,KAAK,WAAW,QAAQ,CAAC,CAAC,GAAG,GAAG;YAC/F,OAAO,KAAK,GAAG;gBAAE,GAAG,OAAO,KAAK;YAAC,KAAK,CAAC;YACvC,OAAO,KAAK,CAAC,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK,IAAI,OAAO,KAAK;YACvD,OAAO,KAAK,CAAC,MAAM,GAAG,OAAO,KAAK,CAAC,MAAM,IAAI,OAAO,MAAM;YAC1D,IAAI,cAAc,GAAG;gBACjB,OAAO,KAAK,CAAC,KAAK,IAAI;YAC1B;YACA,IAAI,eAAe,GAAG;gBAClB,OAAO,KAAK,CAAC,MAAM,IAAI;YAC3B;YACA,IAAI,WAAW,QAAQ,CAAC,CAAC,GAAG,GAAG;gBAC3B,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,QAAQ,CAAC,CAAC;gBAC5C,OAAO,QAAQ,CAAC,CAAC,GAAG,OAAO,QAAQ,CAAC,CAAC,GAAG;gBACxC,OAAO,KAAK,CAAC,KAAK,IAAI;gBACtB,WAAW,QAAQ,CAAC,CAAC,GAAG;YAC5B;YACA,IAAI,WAAW,QAAQ,CAAC,CAAC,GAAG,GAAG;gBAC3B,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,QAAQ,CAAC,CAAC;gBAC5C,OAAO,QAAQ,CAAC,CAAC,GAAG,OAAO,QAAQ,CAAC,CAAC,GAAG;gBACxC,OAAO,KAAK,CAAC,MAAM,IAAI;gBACvB,WAAW,QAAQ,CAAC,CAAC,GAAG;YAC5B;YACA,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK;YACjC,OAAO,MAAM,GAAG,OAAO,KAAK,CAAC,MAAM;QACvC;IACJ;AACJ;AACA,SAAS,aAAa,OAAO,EAAE,QAAQ;IACnC,+GAA+G;IAC/G,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,UAAU;QACzC,OAAO,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IACtE;IACA,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IAC9E,OAAO,SAAS,MAAM,CAAC,CAAC,KAAK;QACzB,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE;QAC7D,IAAI,eAAe,MAAM,KAAK,GAAG;YAC7B,IAAI,IAAI,CAAC;YACT,OAAO;QACX;QACA,MAAM,aAAa;YAAE,GAAG,IAAI;QAAC;QAC7B,KAAK,MAAM,iBAAiB,eAAgB;YACxC,IAAI,eAAe;gBACf,OAAQ,cAAc,IAAI;oBACtB,KAAK;wBAAU;4BACX,WAAW,QAAQ,GAAG,cAAc,QAAQ;4BAC5C;wBACJ;oBACA,KAAK;wBAAY;4BACb,IAAI,OAAO,cAAc,QAAQ,KAAK,aAAa;gCAC/C,WAAW,QAAQ,GAAG,cAAc,QAAQ;4BAChD;4BACA,IAAI,OAAO,cAAc,gBAAgB,KAAK,aAAa;gCACvD,WAAW,gBAAgB,GAAG,cAAc,gBAAgB;4BAChE;4BACA,IAAI,OAAO,cAAc,QAAQ,KAAK,aAAa;gCAC/C,WAAW,QAAQ,GAAG,cAAc,QAAQ;4BAChD;4BACA,IAAI,WAAW,YAAY,EAAE;gCACzB,mBAAmB,KAAK;4BAC5B;4BACA;wBACJ;oBACA,KAAK;wBAAc;4BACf,IAAI,OAAO,cAAc,UAAU,KAAK,aAAa;gCACjD,WAAW,KAAK,GAAG,cAAc,UAAU,CAAC,KAAK;gCACjD,WAAW,MAAM,GAAG,cAAc,UAAU,CAAC,MAAM;4BACvD;4BACA,IAAI,OAAO,cAAc,WAAW,KAAK,aAAa;gCAClD,WAAW,KAAK,GAAG;oCAAE,GAAI,WAAW,KAAK,IAAI,CAAC,CAAC;oCAAG,GAAG,cAAc,UAAU;gCAAC;4BAClF;4BACA,IAAI,OAAO,cAAc,QAAQ,KAAK,WAAW;gCAC7C,WAAW,QAAQ,GAAG,cAAc,QAAQ;4BAChD;4BACA,IAAI,WAAW,YAAY,EAAE;gCACzB,mBAAmB,KAAK;4BAC5B;4BACA;wBACJ;oBACA,KAAK;wBAAU;4BACX,OAAO;wBACX;gBACJ;YACJ;QACJ;QACA,IAAI,IAAI,CAAC;QACT,OAAO;IACX,GAAG;AACP;AACA,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,OAAO,aAAa,SAAS;AACjC;AACA,SAAS,iBAAiB,OAAO,EAAE,KAAK;IACpC,OAAO,aAAa,SAAS;AACjC;AACA,MAAM,wBAAwB,CAAC,IAAI,WAAa,CAAC;QAC7C;QACA,MAAM;QACN;IACJ,CAAC;AACD,SAAS,oBAAoB,KAAK,EAAE,WAAW;IAC3C,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK;QACtB,MAAM,iBAAiB,YAAY,QAAQ,CAAC,KAAK,EAAE;QACnD,IAAI,CAAC,KAAK,QAAQ,IAAI,gBAAgB;YAClC,KAAK,QAAQ,GAAG;YAChB,IAAI,IAAI,CAAC,sBAAsB,KAAK,EAAE,EAAE;QAC5C,OACK,IAAI,KAAK,QAAQ,IAAI,CAAC,gBAAgB;YACvC,KAAK,QAAQ,GAAG;YAChB,IAAI,IAAI,CAAC,sBAAsB,KAAK,EAAE,EAAE;QAC5C;QACA,OAAO;IACX,GAAG,EAAE;AACT;AAEA;;CAEC,GACD,MAAM,cAAc,CAAC,SAAS;IAC1B,OAAO,CAAC;QACJ,IAAI,MAAM,MAAM,KAAK,aAAa,OAAO,EAAE;YACvC;QACJ;QACA,UAAU;IACd;AACJ;AACA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,qBAAqB,EAAE,mBAAmB;QAC1C,oBAAoB,EAAE,kBAAkB;QACxC,UAAU,EAAE,YAAY;IAC5B,CAAC;AACD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE,CAAC,EAAE,WAAW,EAAE,gBAAgB,cAAc,IAAI,EAAE,SAAS,EAAE,gBAAgB,EAAE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,QAAQ,EAAG;IAC7N,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,QAAQ;IACd,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtC,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACtC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC7B,MAAM,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAC1F,MAAM,qBAAqB;QACvB,MAAM,QAAQ,CAAC;YAAE,qBAAqB;YAAO,mBAAmB;QAAK;QACrE,uBAAuB,OAAO,GAAG;QACjC,uBAAuB,OAAO,GAAG;IACrC;IACA,MAAM,UAAU,CAAC;QACb,cAAc;QACd,MAAM,QAAQ,GAAG,qBAAqB;QACtC,MAAM,QAAQ,CAAC;YAAE,sBAAsB;QAAM;IACjD;IACA,MAAM,gBAAgB,CAAC;QACnB,IAAI,MAAM,OAAO,CAAC,cAAc,WAAW,SAAS,IAAI;YACpD,MAAM,cAAc;YACpB;QACJ;QACA,oBAAoB;IACxB;IACA,MAAM,UAAU,eAAe,CAAC,QAAU,aAAa,SAAS;IAChE,MAAM,cAAc,CAAC;QACjB,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;QACzD,gBAAgB,OAAO,GAAG,SAAS;QACnC,IAAI,CAAC,sBACD,CAAC,eACD,MAAM,MAAM,KAAK,KACjB,MAAM,MAAM,KAAK,UAAU,OAAO,IAClC,CAAC,gBAAgB,OAAO,EAAE;YAC1B;QACJ;QACA,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB,OAAO,gBAAgB,OAAO;QAChE;QACA,MAAM,QAAQ,CAAC;YACX,mBAAmB;gBACf,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,QAAQ;gBACR;gBACA;YACJ;QACJ;QACA,mBAAmB;IACvB;IACA,MAAM,cAAc,CAAC;QACjB,MAAM,EAAE,iBAAiB,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ;QACjI,IAAI,CAAC,eAAe,CAAC,gBAAgB,OAAO,IAAI,CAAC,mBAAmB;YAChE;QACJ;QACA,MAAM,QAAQ,CAAC;YAAE,qBAAqB;YAAM,sBAAsB;QAAM;QACxE,MAAM,WAAW,iBAAiB,OAAO,gBAAgB,OAAO;QAChE,MAAM,SAAS,kBAAkB,MAAM,IAAI;QAC3C,MAAM,SAAS,kBAAkB,MAAM,IAAI;QAC3C,MAAM,qBAAqB;YACvB,GAAG,iBAAiB;YACpB,GAAG,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,GAAG;YACtC,GAAG,SAAS,CAAC,GAAG,SAAS,SAAS,CAAC,GAAG;YACtC,OAAO,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG;YAC7B,QAAQ,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG;QAClC;QACA,MAAM,QAAQ;QACd,MAAM,gBAAgB,eAAe,eAAe,oBAAoB,WAAW,kBAAkB,cAAc,OAAO,EAAE,MAAM;QAClI,MAAM,kBAAkB,kBAAkB,eAAe,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;QAC/E,MAAM,kBAAkB,cAAc,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;QACrD,IAAI,uBAAuB,OAAO,KAAK,gBAAgB,MAAM,EAAE;YAC3D,uBAAuB,OAAO,GAAG,gBAAgB,MAAM;YACvD,MAAM,UAAU,oBAAoB,OAAO;YAC3C,IAAI,QAAQ,MAAM,EAAE;gBAChB,gBAAgB;YACpB;QACJ;QACA,IAAI,uBAAuB,OAAO,KAAK,gBAAgB,MAAM,EAAE;YAC3D,uBAAuB,OAAO,GAAG,gBAAgB,MAAM;YACvD,MAAM,UAAU,oBAAoB,OAAO;YAC3C,IAAI,QAAQ,MAAM,EAAE;gBAChB,gBAAgB;YACpB;QACJ;QACA,MAAM,QAAQ,CAAC;YACX,mBAAmB;QACvB;IACJ;IACA,MAAM,YAAY,CAAC;QACf,IAAI,MAAM,MAAM,KAAK,GAAG;YACpB;QACJ;QACA,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;QAC5C,oEAAoE;QACpE,mCAAmC;QACnC,IAAI,CAAC,uBAAuB,qBAAqB,MAAM,MAAM,KAAK,UAAU,OAAO,EAAE;YACjF,UAAU;QACd;QACA,MAAM,QAAQ,CAAC;YAAE,sBAAsB,uBAAuB,OAAO,GAAG;QAAE;QAC1E;QACA,iBAAiB;IACrB;IACA,MAAM,eAAe,CAAC;QAClB,IAAI,qBAAqB;YACrB,MAAM,QAAQ,CAAC;gBAAE,sBAAsB,uBAAuB,OAAO,GAAG;YAAE;YAC1E,iBAAiB;QACrB;QACA;IACJ;IACA,MAAM,qBAAqB,sBAAsB,CAAC,eAAe,mBAAmB;IACpF,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAoB;gBAAE;gBAAU,WAAW;YAAY;SAAE;QAAG,SAAS,qBAAqB,YAAY,YAAY,SAAS;QAAY,eAAe,YAAY,eAAe;QAAY,SAAS,YAAY,SAAS;QAAY,cAAc,qBAAqB,YAAY;QAAkB,aAAa,qBAAqB,cAAc;QAAW,aAAa,qBAAqB,cAAc;QAAiB,WAAW,qBAAqB,YAAY;QAAW,cAAc,qBAAqB,eAAe;QAAkB,KAAK;QAAW,OAAO;IAAe,GACrnB,UACA,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;AAC3C;AACA,KAAK,WAAW,GAAG;AAEnB,SAAS,iBAAiB,IAAI,EAAE,aAAa;IACzC,MAAM,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ;IACjD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,MAAM,aAAa,cAAc,GAAG,CAAC;IACrC,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,IAAI,WAAW,QAAQ,EAAE;QACrB,OAAO;IACX;IACA,OAAO,iBAAiB,YAAY;AACxC;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC1C,IAAI,UAAU;IACd,GAAG;QACC,IAAI,SAAS,QAAQ,WACjB,OAAO;QACX,IAAI,YAAY,QAAQ,OAAO,EAC3B,OAAO;QACX,UAAU,QAAQ,aAAa;IACnC,QAAS,QAAS;IAClB,OAAO;AACX;AACA,2EAA2E;AAC3E,SAAS,aAAa,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM;IACjE,OAAO,MAAM,IAAI,CAAC,cAAc,MAAM,IACjC,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,QAAQ,IAAI,EAAE,EAAE,KAAK,MAAM,KAC7C,CAAC,CAAC,EAAE,UAAU,IAAI,EAAE,QAAQ,IAAI,CAAC,iBAAiB,GAAG,cAAc,KACnE,CAAC,EAAE,SAAS,IAAK,kBAAkB,OAAO,EAAE,SAAS,KAAK,WAAY,GACrE,GAAG,CAAC,CAAC,IAAM,CAAC;YACb,IAAI,EAAE,EAAE;YACR,UAAU,EAAE,QAAQ,IAAI;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACrC,kBAAkB,EAAE,gBAAgB,IAAI;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACrD,UAAU;gBACN,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,gBAAgB,EAAE,KAAK,CAAC;gBAC3C,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,gBAAgB,EAAE,KAAK,CAAC;YAC/C;YACA,OAAO;gBACH,GAAG;gBACH,GAAG;YACP;YACA,QAAQ,EAAE,MAAM;YAChB,YAAY,EAAE,UAAU,IAAI,EAAE,QAAQ;YACtC,UAAU,EAAE,UAAU,IAAI,EAAE,QAAQ;YACpC,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,YAAY;QAChC,CAAC;AACL;AACA,SAAS,gBAAgB,IAAI,EAAE,MAAM;IACjC,IAAI,CAAC,UAAU,WAAW,UAAU;QAChC,OAAO;IACX;IACA,OAAO;QAAC,MAAM,CAAC,EAAE;QAAE;YAAC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,KAAK,IAAI,CAAC;YAAG,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,MAAM,IAAI,CAAC;SAAE;KAAC;AAC7F;AACA,SAAS,iBAAiB,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa;IAAC;IAAG;CAAE,EAAE,OAAO;IACjG,MAAM,oBAAoB,gBAAgB,MAAM,KAAK,MAAM,IAAI;IAC/D,IAAI,gBAAgB;IACpB,MAAM,WAAW,KAAK,UAAU,IAAI,KAAK,QAAQ;IACjD,IAAI,KAAK,MAAM,KAAK,YAAY,CAAC,KAAK,YAAY,EAAE;QAChD,IAAI,YAAY,KAAK,KAAK,IAAI,KAAK,MAAM,EAAE;YACvC,MAAM,SAAS,cAAc,GAAG,CAAC;YACjC,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,0BAA0B,QAAQ,YAAY,gBAAgB;YACjG,gBACI,UAAU,UAAU,YAAY,UAAU,YAAY,UAAU,OAAO,KAAK,KAAK,UAAU,OAAO,MAAM,IAClG;gBACE;oBAAC,UAAU,KAAK,KAAK,GAAG,UAAU,CAAC,EAAE;oBAAE,UAAU,KAAK,MAAM,GAAG,UAAU,CAAC,EAAE;iBAAC;gBAC7E;oBACI,UAAU,OAAO,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,UAAU,CAAC,EAAE;oBAChE,UAAU,OAAO,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,UAAU,CAAC,EAAE;iBACtE;aACJ,GACC;QACd,OACK;YACD,UAAU,OAAO,aAAa,CAAC,WAAW;YAC1C,gBAAgB;QACpB;IACJ,OACK,IAAI,KAAK,MAAM,IAAI,YAAY,KAAK,MAAM,KAAK,UAAU;QAC1D,MAAM,SAAS,cAAc,GAAG,CAAC;QACjC,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,0BAA0B,QAAQ,YAAY,gBAAgB;QACjG,gBAAgB;YACZ;gBAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;gBAAS,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;aAAQ;YAC1D;gBAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;gBAAS,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;aAAQ;SAC7D;IACL;IACA,IAAI,iBAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAClC,IAAI,UAAU;QACV,MAAM,aAAa,cAAc,GAAG,CAAC;QACrC,iBAAiB,0BAA0B,YAAY,YAAY,gBAAgB;IACvF;IACA,MAAM,mBAAmB,iBAAiB,kBAAkB,WACtD,cAAc,cAAc,iBAC5B;IACN,OAAO;QACH,UAAU;YACN,GAAG,iBAAiB,CAAC,GAAG,eAAe,CAAC;YACxC,GAAG,iBAAiB,CAAC,GAAG,eAAe,CAAC;QAC5C;QACA;IACJ;AACJ;AACA,sBAAsB;AACtB,sFAAsF;AACtF,oDAAoD;AACpD,SAAS,sBAAsB,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAG;IAChE,MAAM,oBAAoB,UAAU,GAAG,CAAC,CAAC;QACrC,MAAM,OAAO,cAAc,GAAG,CAAC,EAAE,EAAE;QACnC,OAAO;YACH,GAAG,IAAI;YACP,UAAU,EAAE,QAAQ;YACpB,kBAAkB,EAAE,gBAAgB;QACxC;IACJ;IACA,OAAO;QAAC,SAAS,kBAAkB,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,UAAU,iBAAiB,CAAC,EAAE;QAAE;KAAkB;AAC9G;AAEA,MAAM,kBAAkB,CAAC,UAAU,aAAa,MAAM;IAClD,MAAM,UAAU,YAAY,gBAAgB,CAAC;IAC7C,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE;QAC7B,OAAO;IACX;IACA,MAAM,eAAe,MAAM,IAAI,CAAC;IAChC,MAAM,aAAa,YAAY,qBAAqB;IACpD,MAAM,aAAa;QACf,GAAG,WAAW,KAAK,GAAG,UAAU,CAAC,EAAE;QACnC,GAAG,WAAW,MAAM,GAAG,UAAU,CAAC,EAAE;IACxC;IACA,OAAO,aAAa,GAAG,CAAC,CAAC;QACrB,MAAM,eAAe,OAAO,qBAAqB;QACjD,OAAO;YACH,IAAI,OAAO,YAAY,CAAC;YACxB,UAAU,OAAO,YAAY,CAAC;YAC9B,GAAG,CAAC,aAAa,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,CAAC,IAAI;YAC1D,GAAG,CAAC,aAAa,GAAG,GAAG,WAAW,GAAG,GAAG,WAAW,CAAC,IAAI;YACxD,GAAG,cAAc,OAAO;QAC5B;IACJ;AACJ;AACA,SAAS,gBAAgB,EAAE,EAAE,QAAQ,EAAE,OAAO;IAC1C,OAAO,YAAY,YACb,UACA,CAAC;QACC,MAAM,OAAO,WAAW,aAAa,CAAC,GAAG,CAAC;QAC1C,IAAI,MAAM;YACN,QAAQ,OAAO;gBAAE,GAAG,IAAI;YAAC;QAC7B;IACJ;AACR;AACA,4BAA4B;AAC5B,+EAA+E;AAC/E,KAAK;AACL,mFAAmF;AACnF,SAAS,gBAAgB,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,KAAK,EAAE,OAAO,EAAG;IAC9D,MAAM,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ;IAChH,MAAM,OAAO,cAAc,GAAG,CAAC;IAC/B,IAAI,CAAC,MAAM;QACP,UAAU,OAAO,aAAa,CAAC,WAAW,CAAC;QAC3C;IACJ;IACA,MAAM,QAAQ,CAAC;QAAE,sBAAsB;IAAM;IAC7C,IAAI,CAAC,KAAK,QAAQ,EAAE;QAChB,iBAAiB;YAAC;SAAG;IACzB,OACK,IAAI,YAAa,KAAK,QAAQ,IAAI,sBAAuB;QAC1D,sBAAsB;YAAE,OAAO;gBAAC;aAAK;YAAE,OAAO,EAAE;QAAC;QACjD,sBAAsB,IAAM,SAAS,SAAS;IAClD;AACJ;AAEA,SAAS;IACL,MAAM,QAAQ;IACd,qEAAqE;IACrE,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,EAAE,WAAW,EAAE;QACnD,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;QAC1D,MAAM,IAAI,YAAY,OAAO,GAAG,YAAY,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,YAAY,OAAO;QACpF,MAAM,IAAI,YAAY,OAAO,GAAG,YAAY,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,YAAY,OAAO;QACpF,MAAM,aAAa;YACf,GAAG,CAAC,IAAI,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;YACpC,GAAG,CAAC,IAAI,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;QACxC;QACA,mFAAmF;QACnF,OAAO;YACH,UAAU,aAAa,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,EAAE,IAAI,WAAW,CAAC;YAC1F,UAAU,aAAa,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,EAAE,IAAI,WAAW,CAAC;YAC1F,GAAG,UAAU;QACjB;IACJ,GAAG,EAAE;IACL,OAAO;AACX;AAEA,SAAS,sBAAsB,aAAa;IACxC,OAAO,CAAC,OAAO,GAAG,QAAU,gBAAgB,OAAO;AACvD;AACA,SAAS,QAAQ,EAAE,OAAO,EAAE,WAAW,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAG;IACrH,MAAM,QAAQ;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAM,GAAG;IAAK;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC1C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC9B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,qBAAqB;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,SAAS,SAAS;YAClB,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,OAAO;YACxC,MAAM,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACzB,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAG,GAAG,MAAM,QAAQ;gBAClJ,QAAQ,OAAO,GAAG;oBAAE;oBAAG;gBAAE;gBACzB,IAAI,YAAY;gBAChB,IAAI,WAAW;oBAAE,GAAG;oBAAG,GAAG;oBAAG,IAAI;oBAAG,IAAI;gBAAE;gBAC1C,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,KAAK,YAAY;oBAC5C,MAAM,OAAO,eAAe,UAAU,OAAO,EAAE;oBAC/C,WAAW,UAAU;gBACzB;gBACA,UAAU,OAAO,GAAG,UAAU,OAAO,CAAC,GAAG,CAAC,CAAC;oBACvC,MAAM,eAAe;wBAAE,GAAG,IAAI,EAAE,QAAQ,CAAC,CAAC;wBAAE,GAAG,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAAC;oBAChE,IAAI,YAAY;wBACZ,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE;wBACtE,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE;oBAC1E;oBACA,sHAAsH;oBACtH,2FAA2F;oBAC3F,MAAM,qBAAqB;wBACvB;4BAAC,UAAU,CAAC,EAAE,CAAC,EAAE;4BAAE,UAAU,CAAC,EAAE,CAAC,EAAE;yBAAC;wBACpC;4BAAC,UAAU,CAAC,EAAE,CAAC,EAAE;4BAAE,UAAU,CAAC,EAAE,CAAC,EAAE;yBAAC;qBACvC;oBACD,IAAI,UAAU,OAAO,CAAC,MAAM,GAAG,KAAK,cAAc,CAAC,EAAE,MAAM,EAAE;wBACzD,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;wBAC/E,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,SAAS,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;wBACjG,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;wBAC/E,kBAAkB,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,SAAS,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;oBACtG;oBACA,MAAM,aAAa,iBAAiB,GAAG,cAAc,eAAe,oBAAoB,YAAY;oBACpG,+EAA+E;oBAC/E,YAAY,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,WAAW,QAAQ,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,WAAW,QAAQ,CAAC,CAAC;oBACzG,EAAE,QAAQ,GAAG,WAAW,QAAQ;oBAChC,EAAE,gBAAgB,GAAG,WAAW,gBAAgB;oBAChD,OAAO;gBACX;gBACA,IAAI,CAAC,WAAW;oBACZ;gBACJ;gBACA,oBAAoB,UAAU,OAAO,EAAE,MAAM;gBAC7C,YAAY;gBACZ,MAAM,SAAS,SAAS,aAAa,sBAAsB;gBAC3D,IAAI,UAAU,UAAU,OAAO,EAAE;oBAC7B,MAAM,CAAC,aAAa,MAAM,GAAG,sBAAsB;wBAC/C;wBACA,WAAW,UAAU,OAAO;wBAC5B;oBACJ;oBACA,OAAO,UAAU,OAAO,EAAE,aAAa;gBAC3C;YACJ;YACA,MAAM,UAAU;gBACZ,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC1B;gBACJ;gBACA,MAAM,CAAC,WAAW,UAAU,GAAG,YAAY,cAAc,OAAO,EAAE,gBAAgB,OAAO;gBACzF,IAAI,cAAc,KAAK,cAAc,GAAG;oBACpC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;oBAC3C,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,SAAS,CAAC,EAAE;oBACvE,QAAQ,OAAO,CAAC,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,SAAS,CAAC,EAAE;oBACvE,IAAI,MAAM;wBAAE,GAAG;wBAAW,GAAG;oBAAU,IAAI;wBACvC,YAAY,QAAQ,OAAO;oBAC/B;gBACJ;gBACA,UAAU,OAAO,GAAG,sBAAsB;YAC9C;YACA,MAAM,YAAY,CAAC;gBACf,MAAM,EAAE,aAAa,EAAE,oBAAoB,EAAE,cAAc,EAAE,qBAAqB,EAAE,eAAe,EAAE,oBAAoB,EAAG,GAAG,MAAM,QAAQ;gBAC7I,YAAY,OAAO,GAAG;gBACtB,MAAM,UAAU,SAAS,kBAAkB,sBAAsB;gBACjE,IAAI,CAAC,CAAC,qBAAqB,CAAC,YAAY,KAAK,CAAC,wBAAwB,QAAQ;oBAC1E,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,UAAU;wBACtC,+DAA+D;wBAC/D;oBACJ;gBACJ;gBACA,IAAI,UAAU,gBAAgB,mBAAmB;oBAC7C,gBAAgB;wBACZ,IAAI;wBACJ;wBACA,SAAS;oBACb;gBACJ;gBACA,MAAM,aAAa,mBAAmB;gBACtC,QAAQ,OAAO,GAAG;gBAClB,UAAU,OAAO,GAAG,aAAa,eAAe,gBAAgB,YAAY;gBAC5E,IAAI,WAAW,UAAU,OAAO,EAAE;oBAC9B,MAAM,CAAC,aAAa,MAAM,GAAG,sBAAsB;wBAC/C;wBACA,WAAW,UAAU,OAAO;wBAC5B;oBACJ;oBACA,QAAQ,MAAM,WAAW,EAAE,aAAa;gBAC5C;YACJ;YACA,IAAI,UAAU;gBACV,UAAU,EAAE,CAAC,SAAS;YAC1B,OACK;gBACD,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,OAAI,AAAD,IAClB,EAAE,CAAC,SAAS,CAAC;oBACd,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;oBACrD,IAAI,sBAAsB,GAAG;wBACzB,UAAU;oBACd;oBACA,UAAU,OAAO,GAAG;oBACpB,MAAM,aAAa,mBAAmB;oBACtC,QAAQ,OAAO,GAAG;oBAClB,gBAAgB,OAAO,GAAG,SAAS,2BAA2B;oBAC9D,cAAc,OAAO,GAAG,iBAAiB,MAAM,WAAW,EAAE,gBAAgB,OAAO;gBACvF,GACK,EAAE,CAAC,QAAQ,CAAC;oBACb,MAAM,aAAa,mBAAmB;oBACtC,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;oBAC/D,IAAI,MAAM,WAAW,CAAC,IAAI,KAAK,eAAe,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;wBAChF,UAAU,OAAO,GAAG;oBACxB;oBACA,IAAI,UAAU,OAAO,EAAE;wBACnB;oBACJ;oBACA,IAAI,CAAC,eAAe,OAAO,IAAI,YAAY,OAAO,IAAI,mBAAmB;wBACrE,eAAe,OAAO,GAAG;wBACzB;oBACJ;oBACA,IAAI,CAAC,YAAY,OAAO,EAAE;wBACtB,MAAM,IAAI,WAAW,QAAQ,GAAG,CAAC,SAAS,SAAS,KAAK,CAAC;wBACzD,MAAM,IAAI,WAAW,QAAQ,GAAG,CAAC,SAAS,SAAS,KAAK,CAAC;wBACzD,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;wBACvC,IAAI,WAAW,mBAAmB;4BAC9B,UAAU;wBACd;oBACJ;oBACA,+BAA+B;oBAC/B,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC,KAAK,WAAW,QAAQ,IAAI,QAAQ,OAAO,CAAC,CAAC,KAAK,WAAW,QAAQ,KACvF,UAAU,OAAO,IACjB,YAAY,OAAO,EAAE;wBACrB,UAAU,OAAO,GAAG,MAAM,WAAW;wBACrC,cAAc,OAAO,GAAG,iBAAiB,MAAM,WAAW,EAAE,gBAAgB,OAAO;wBACnF,YAAY;oBAChB;gBACJ,GACK,EAAE,CAAC,OAAO,CAAC;oBACZ,IAAI,CAAC,YAAY,OAAO,IAAI,UAAU,OAAO,EAAE;wBAC3C;oBACJ;oBACA,YAAY;oBACZ,eAAe,OAAO,GAAG;oBACzB,YAAY,OAAO,GAAG;oBACtB,qBAAqB,UAAU,OAAO;oBACtC,IAAI,UAAU,OAAO,EAAE;wBACnB,MAAM,EAAE,mBAAmB,EAAE,aAAa,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG,MAAM,QAAQ;wBAClG,MAAM,SAAS,SAAS,iBAAiB,sBAAsB;wBAC/D,oBAAoB,UAAU,OAAO,EAAE,OAAO;wBAC9C,IAAI,QAAQ;4BACR,MAAM,CAAC,aAAa,MAAM,GAAG,sBAAsB;gCAC/C;gCACA,WAAW,UAAU,OAAO;gCAC5B;4BACJ;4BACA,OAAO,MAAM,WAAW,EAAE,aAAa;wBAC3C;oBACJ;gBACJ,GACK,MAAM,CAAC,CAAC;oBACT,MAAM,SAAS,MAAM,MAAM;oBAC3B,MAAM,cAAc,CAAC,MAAM,MAAM,IAC7B,CAAC,CAAC,mBAAmB,CAAC,YAAY,QAAQ,CAAC,CAAC,EAAE,iBAAiB,EAAE,QAAQ,KACzE,CAAC,CAAC,kBAAkB,YAAY,QAAQ,gBAAgB,QAAQ;oBACpE,OAAO;gBACX;gBACA,UAAU,IAAI,CAAC;gBACf,OAAO;oBACH,UAAU,EAAE,CAAC,SAAS;gBAC1B;YACJ;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;AACX;AAEA,SAAS;IACL,MAAM,QAAQ;IACd,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,mBAAmB,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,QAAQ;QAClI,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,IAAI,CAAC,EAAE,SAAS,IAAK,kBAAkB,OAAO,EAAE,SAAS,KAAK,WAAY;QACnI,6EAA6E;QAC7E,yDAAyD;QACzD,MAAM,QAAQ,aAAa,QAAQ,CAAC,EAAE,GAAG;QACzC,MAAM,QAAQ,aAAa,QAAQ,CAAC,EAAE,GAAG;QACzC,MAAM,SAAS,OAAO,cAAc,GAAG,IAAI;QAC3C,MAAM,gBAAgB,OAAO,CAAC,GAAG,QAAQ;QACzC,MAAM,gBAAgB,OAAO,CAAC,GAAG,QAAQ;QACzC,MAAM,cAAc,cAAc,GAAG,CAAC,CAAC;YACnC,IAAI,EAAE,gBAAgB,EAAE;gBACpB,MAAM,eAAe;oBAAE,GAAG,EAAE,gBAAgB,CAAC,CAAC,GAAG;oBAAe,GAAG,EAAE,gBAAgB,CAAC,CAAC,GAAG;gBAAc;gBACxG,IAAI,YAAY;oBACZ,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE;oBACtE,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,EAAE;gBAC1E;gBACA,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,GAAG,iBAAiB,GAAG,cAAc,eAAe,YAAY,WAAW;gBAC/G,EAAE,QAAQ,GAAG;gBACb,EAAE,gBAAgB,GAAG;YACzB;YACA,OAAO;QACX;QACA,oBAAoB,aAAa,MAAM;IAC3C,GAAG,EAAE;IACL,OAAO;AACX;AAEA,MAAM,gBAAgB;IAClB,SAAS;QAAE,GAAG;QAAG,GAAG,CAAC;IAAE;IACvB,WAAW;QAAE,GAAG;QAAG,GAAG;IAAE;IACxB,WAAW;QAAE,GAAG,CAAC;QAAG,GAAG;IAAE;IACzB,YAAY;QAAE,GAAG;QAAG,GAAG;IAAE;AAC7B;AACA,IAAI,WAAW,CAAC;IACZ,MAAM,cAAc,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAE,WAAW,EAAE,mBAAmB,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAG;QACzb,MAAM,QAAQ;QACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAC3B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAClC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAClC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACxB,MAAM,mBAAmB,gBAAgB,eAAe,WAAW,gBAAgB,eAAe;QAClG,MAAM,kBAAkB;QACxB,MAAM,sBAAsB,gBAAgB,IAAI,MAAM,QAAQ,EAAE;QAChE,MAAM,qBAAqB,gBAAgB,IAAI,MAAM,QAAQ,EAAE;QAC/D,MAAM,sBAAsB,gBAAgB,IAAI,MAAM,QAAQ,EAAE;QAChE,MAAM,uBAAuB,gBAAgB,IAAI,MAAM,QAAQ,EAAE;QACjE,MAAM,uBAAuB,gBAAgB,IAAI,MAAM,QAAQ,EAAE;QACjE,MAAM,sBAAsB,CAAC;YACzB,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;YAC5C,IAAI,gBAAgB,CAAC,CAAC,qBAAqB,CAAC,eAAe,oBAAoB,CAAC,GAAG;gBAC/E,mFAAmF;gBACnF,gBAAgB;oBACZ;oBACA;oBACA;gBACJ;YACJ;YACA,IAAI,SAAS;gBACT,MAAM,OAAO,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC;gBAChD,IAAI,MAAM;oBACN,QAAQ,OAAO;wBAAE,GAAG,IAAI;oBAAC;gBAC7B;YACJ;QACJ;QACA,MAAM,YAAY,CAAC;YACf,IAAI,eAAe,QAAQ;gBACvB;YACJ;YACA,IAAI,qBAAqB;gBACrB;YACJ;YACA,IAAI,qBAAqB,QAAQ,CAAC,MAAM,GAAG,KAAK,cAAc;gBAC1D,MAAM,WAAW,MAAM,GAAG,KAAK;gBAC/B,gBAAgB;oBACZ;oBACA;oBACA;oBACA;gBACJ;YACJ,OACK,IAAI,eAAe,YAAY,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;gBAChG,MAAM,QAAQ,CAAC;oBACX,iBAAiB,CAAC,oBAAoB,EAAE,MAAM,GAAG,CAC5C,OAAO,CAAC,SAAS,IACjB,WAAW,GAAG,mBAAmB,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,MAAM;gBAClE;gBACA,gBAAgB;oBACZ,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC7B,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC7B,gBAAgB,MAAM,QAAQ;gBAClC;YACJ;QACJ;QACA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACN,OAAO;gBACH,IAAI,YAAY,OAAO,EAAE;oBACrB,gBAAgB,UAAU,YAAY,OAAO;oBAC7C,YAAY,OAAO,GAAG;gBAC1B;YACJ;QACJ,GAAG,EAAE;QACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACN,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ;gBAC5B,MAAM,WAAW,QAAQ,OAAO;gBAChC,IAAI,CAAC,eAAe,CAAC,mBAAmB,YAAY,OAAO,KAAK,UAAU;oBACtE,6FAA6F;oBAC7F,6DAA6D;oBAC7D,IAAI,YAAY,OAAO,EAAE;wBACrB,gBAAgB,UAAU,YAAY,OAAO;oBACjD;oBACA,gBAAgB,QAAQ;oBACxB,YAAY,OAAO,GAAG;gBAC1B;YACJ;QACJ,GAAG;YAAC;YAAQ;YAAa;SAAgB;QACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACN,kGAAkG;YAClG,MAAM,cAAc,SAAS,OAAO,KAAK;YACzC,MAAM,mBAAmB,mBAAmB,OAAO,KAAK;YACxD,MAAM,mBAAmB,mBAAmB,OAAO,KAAK;YACxD,IAAI,QAAQ,OAAO,IAAI,CAAC,eAAe,oBAAoB,gBAAgB,GAAG;gBAC1E,IAAI,aAAa;oBACb,SAAS,OAAO,GAAG;gBACvB;gBACA,IAAI,kBAAkB;oBAClB,mBAAmB,OAAO,GAAG;gBACjC;gBACA,IAAI,kBAAkB;oBAClB,mBAAmB,OAAO,GAAG;gBACjC;gBACA,MAAM,QAAQ,GAAG,oBAAoB,CAAC;oBAAC;wBAAE;wBAAI,aAAa,QAAQ,OAAO;wBAAE,aAAa;oBAAK;iBAAE;YACnG;QACJ,GAAG;YAAC;YAAI;YAAM;YAAgB;SAAe;QAC7C,MAAM,WAAW,QAAQ;YACrB;YACA,UAAU,UAAU,CAAC;YACrB;YACA,gBAAgB;YAChB,QAAQ;YACR;YACA;QACJ;QACA,IAAI,QAAQ;YACR,OAAO;QACX;QACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;gBAC3C;gBACA,CAAC,iBAAiB,EAAE,MAAM;gBAC1B;oBACI,0DAA0D;oBAC1D,CAAC,eAAe,EAAE;gBACtB;gBACA;gBACA;oBACI;oBACA,YAAY;oBACZ,QAAQ;oBACR;gBACJ;aACH;YAAG,KAAK;YAAS,OAAO;gBACrB;gBACA,WAAW,CAAC,UAAU,EAAE,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC;gBACvD,eAAe,mBAAmB,QAAQ;gBAC1C,YAAY,cAAc,YAAY;gBACtC,GAAG,KAAK;YACZ;YAAG,WAAW;YAAI,eAAe,CAAC,SAAS,EAAE,IAAI;YAAE,cAAc;YAAqB,aAAa;YAAoB,cAAc;YAAqB,eAAe;YAAsB,SAAS;YAAqB,eAAe;YAAsB,WAAW,cAAc,YAAY;YAAW,UAAU,cAAc,IAAI;YAAW,MAAM,cAAc,WAAW;YAAW,oBAAoB,sBAAsB,YAAY,GAAG,mBAAmB,CAAC,EAAE,MAAM;YAAE,cAAc;QAAU,GAClf,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;YAAE,OAAO;QAAG,GACtC,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;YAAE,IAAI;YAAI,MAAM;YAAM,MAAM;YAAM,MAAM;YAAM,MAAM;YAAM,UAAU;YAAU,eAAe;YAAe,gBAAgB;YAAgB,gBAAgB;YAAgB,UAAU;YAAU,YAAY;YAAY,QAAQ;QAAO;IACtR;IACA,YAAY,WAAW,GAAG;IAC1B,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAChB;AAEA;;;CAGC,GACD,MAAM,aAAa,CAAC;IAChB,MAAM,gBAAgB,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ;IAC3D,OAAO;QACH,GAAG,eAAe,eAAe,EAAE,UAAU,CAAC;QAC9C,iBAAiB,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9F,qBAAqB,EAAE,mBAAmB;IAC9C;AACJ;AACA,SAAS,eAAe,EAAE,sBAAsB,EAAE,cAAc,EAAE,mBAAmB,EAAE;IACnF,MAAM,QAAQ;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,eAAe,EAAE,mBAAmB,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAC7G,MAAM,kBAAkB;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,qBAAqB;YACtB,QAAQ,OAAO,EAAE,MAAM;gBACnB,eAAe;YACnB;QACJ;IACJ,GAAG;QAAC;KAAoB;IACxB,QAAQ;QACJ;IACJ;IACA,IAAI,uBAAuB,CAAC,SAAS,CAAC,QAAQ;QAC1C,OAAO;IACX;IACA,MAAM,gBAAgB,yBAChB,CAAC;QACC,MAAM,gBAAgB,MACjB,QAAQ,GACR,QAAQ,GACR,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ;QAC7B,uBAAuB,OAAO;IAClC,IACE;IACN,MAAM,YAAY,CAAC;QACf,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,MAAM,GAAG,GAAG;YAChE,gBAAgB;gBACZ,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7B,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7B,gBAAgB,MAAM,QAAQ;YAClC;QACJ;IACJ;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA8B;YAAyB;SAAe;QAAG,OAAO;YAC5H,WAAW;QACf;IAAE,GACF,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,KAAK;QAAS,WAAW;QAAmC,eAAe;QAAe,UAAU,sBAAsB,YAAY,CAAC;QAAG,WAAW,sBAAsB,YAAY;QAAW,OAAO;YAC9N;YACA;YACA;YACA;QACJ;IAAE;AACd;AACA,IAAI,mBAAmB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE5B,MAAM,aAAa,CAAC,IAAM,EAAE,oBAAoB;AAChD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,YAAY,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,WAAW,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,mBAAmB,EAAG;IACrnB,MAAM,uBAAuB,SAAS;IACtC,MAAM,sBAAsB,YAAY;IACxC,MAAM,0BAA0B,YAAY;IAC5C,MAAM,YAAY,2BAA2B;IAC7C,MAAM,cAAc,2BAA2B;IAC/C,MAAM,cAAc,uBAAwB,mBAAmB,cAAc;IAC7E,oBAAoB;QAAE;QAAe;IAAsB;IAC3D,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,QAAQ;QAAQ,aAAa;QAAa,WAAW;QAAW,mBAAmB;QAAmB,oBAAoB;QAAoB,cAAc;QAAc,aAAa;QAAa,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,mBAAmB;QAAmB,WAAW,CAAC,uBAAuB;QAAW,iBAAiB;QAAiB,iBAAiB;QAAiB,SAAS;QAAS,SAAS;QAAS,uBAAuB;QAAuB,kBAAkB;QAAkB,kBAAkB;QAAkB,gBAAgB;IAAe,GACzpB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;QAAE,kBAAkB;QAAkB,gBAAgB;QAAgB,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,mBAAmB;QAAmB,cAAc;QAAc,WAAW;QAAW,aAAa,CAAC,CAAC;QAAa,eAAe;IAAc,GACjX,UACA,wBAAyB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kBAAkB;QAAE,wBAAwB;QAAwB,gBAAgB;QAAgB,qBAAqB;IAAoB;AACtM;AACA,aAAa,WAAW,GAAG;AAC3B,IAAI,iBAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B,SAAS,gBAAgB,iBAAiB;IACtC,MAAM,QAAQ,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAM,oBACpC,eAAe,EAAE,aAAa,EAAE;YAAE,GAAG;YAAG,GAAG;YAAG,OAAO,EAAE,KAAK;YAAE,QAAQ,EAAE,MAAM;QAAC,GAAG,EAAE,SAAS,EAAE,QAC/F,EAAE,QAAQ,IAAI;QAAC;KAAkB;IACvC,OAAO;AACX;AAEA,SAAS,gBAAgB,SAAS;IAC9B,MAAM,gBAAgB;QAClB,OAAO,SAAU,UAAU,KAAK,IAAI;QACpC,SAAS,SAAU,UAAU,OAAO,IAAI;QACxC,QAAQ,SAAU,UAAU,MAAM,IAAI;QACtC,OAAO,SAAU,UAAU,KAAK,IAAI;IACxC;IACA,MAAM,eAAe,CAAC;IACtB,MAAM,eAAe,OAAO,IAAI,CAAC,WAC5B,MAAM,CAAC,CAAC,IAAM,CAAC;YAAC;YAAS;YAAW;YAAU;SAAQ,CAAC,QAAQ,CAAC,IAChE,MAAM,CAAC,CAAC,KAAK;QACd,GAAG,CAAC,IAAI,GAAG,SAAU,SAAS,CAAC,IAAI,IAAI;QACvC,OAAO;IACX,GAAG;IACH,OAAO;QACH,GAAG,aAAa;QAChB,GAAG,YAAY;IACnB;AACJ;AACA,MAAM,wBAAwB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAG;IAC3D,IAAI,CAAC,SAAS,CAAC,QAAQ;QACnB,OAAO;YAAE;YAAG;QAAE;IAClB;IACA,IAAI,MAAM,CAAC,EAAE,GAAG,KAAK,MAAM,CAAC,EAAE,GAAG,KAAK,MAAM,CAAC,EAAE,GAAG,KAAK,MAAM,CAAC,EAAE,GAAG,GAAG;QAClE,OAAO;YAAE;YAAG;QAAE;IAClB;IACA,OAAO;QACH,GAAG,IAAI,QAAQ,MAAM,CAAC,EAAE;QACxB,GAAG,IAAI,SAAS,MAAM,CAAC,EAAE;IAC7B;AACJ;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,gBAAgB,EAAE,cAAc;QAChC,kBAAkB,EAAE,gBAAgB;QACpC,gBAAgB,EAAE,cAAc;QAChC,oBAAoB,EAAE,kBAAkB;QACxC,sBAAsB,EAAE,oBAAoB;QAC5C,SAAS,EAAE,OAAO;IACtB,CAAC;AACD,MAAM,eAAe,CAAC;IAClB,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,OAAO,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAC5I,MAAM,QAAQ,gBAAgB,MAAM,yBAAyB;IAC7D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,OAAO,mBAAmB,aAAa;YACvC,OAAO;QACX;QACA,MAAM,WAAW,IAAI,eAAe,CAAC;YACjC,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAC,QAAU,CAAC;oBACpC,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC;oBAC9B,aAAa,MAAM,MAAM;oBACzB,aAAa;gBACjB,CAAC;YACD,qBAAqB;QACzB;QACA,kBAAkB,OAAO,GAAG;QAC5B,OAAO;IACX,GAAG,EAAE;IACL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,OAAO;YACH,mBAAmB,SAAS;QAChC;IACJ,GAAG,EAAE;IACL,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW;QAAqB,OAAO;IAAe,GAAG,MAAM,GAAG,CAAC,CAAC;QACrG,IAAI,WAAW,KAAK,IAAI,IAAI;QAC5B,IAAI,CAAC,MAAM,SAAS,CAAC,SAAS,EAAE;YAC5B,UAAU,OAAO,aAAa,CAAC,WAAW,CAAC;YAC3C,WAAW;QACf;QACA,MAAM,gBAAiB,MAAM,SAAS,CAAC,SAAS,IAAI,MAAM,SAAS,CAAC,OAAO;QAC3E,MAAM,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY;QAClG,MAAM,eAAe,CAAC,CAAC,CAAC,KAAK,UAAU,IAAK,sBAAsB,OAAO,KAAK,UAAU,KAAK,WAAY;QACzG,MAAM,gBAAgB,CAAC,CAAC,CAAC,KAAK,WAAW,IAAK,oBAAoB,OAAO,KAAK,WAAW,KAAK,WAAY;QAC1G,MAAM,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY;QAClG,MAAM,kBAAkB,MAAM,UAAU,GAClC,cAAc,KAAK,gBAAgB,EAAE,MAAM,UAAU,IACrD,KAAK,gBAAgB;QAC3B,MAAM,OAAO,iBAAiB,KAAK;QACnC,MAAM,OAAO,iBAAiB,KAAK;QACnC,MAAM,YAAY,sBAAsB;YACpC,GAAG;YACH,GAAG;YACH,OAAO,KAAK,KAAK,IAAI;YACrB,QAAQ,KAAK,MAAM,IAAI;YACvB,QAAQ,MAAM,UAAU;QAC5B;QACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;YAAE,KAAK,KAAK,EAAE;YAAE,IAAI,KAAK,EAAE;YAAE,WAAW,KAAK,SAAS;YAAE,OAAO,KAAK,KAAK;YAAE,MAAM;YAAU,MAAM,KAAK,IAAI;YAAE,gBAAgB,KAAK,cAAc,IAAI,SAAS,MAAM;YAAE,gBAAgB,KAAK,cAAc,IAAI,SAAS,GAAG;YAAE,QAAQ,KAAK,MAAM;YAAE,MAAM;YAAM,MAAM;YAAM,YAAY,UAAU,CAAC;YAAE,YAAY,UAAU,CAAC;YAAE,mBAAmB,MAAM,iBAAiB;YAAE,SAAS,MAAM,WAAW;YAAE,cAAc,MAAM,gBAAgB;YAAE,aAAa,MAAM,eAAe;YAAE,cAAc,MAAM,gBAAgB;YAAE,eAAe,MAAM,iBAAiB;YAAE,eAAe,MAAM,iBAAiB;YAAE,UAAU,CAAC,CAAC,KAAK,QAAQ;YAAE,aAAa;YAAa,cAAc;YAAc,eAAe;YAAe,aAAa;YAAa,gBAAgB;YAAgB,YAAY,KAAK,UAAU;YAAE,QAAQ,IAAI,CAAC,gBAAgB,EAAE,KAAK;YAAG,UAAU,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAAU,iBAAiB,MAAM,eAAe;YAAE,gBAAgB,MAAM,cAAc;YAAE,aAAa,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,KAAK,MAAM;YAAE,MAAM,MAAM,IAAI;YAAE,qBAAqB,MAAM,mBAAmB;YAAE,WAAW,KAAK,SAAS;YAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAAa;IACvpC;AACJ;AACA,aAAa,WAAW,GAAG;AAC3B,IAAI,iBAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B,MAAM,SAAS,CAAC,GAAG,OAAO;IACtB,IAAI,aAAa,SAAS,IAAI,EAC1B,OAAO,IAAI;IACf,IAAI,aAAa,SAAS,KAAK,EAC3B,OAAO,IAAI;IACf,OAAO;AACX;AACA,MAAM,SAAS,CAAC,GAAG,OAAO;IACtB,IAAI,aAAa,SAAS,GAAG,EACzB,OAAO,IAAI;IACf,IAAI,aAAa,SAAS,MAAM,EAC5B,OAAO,IAAI;IACf,OAAO;AACX;AACA,MAAM,uBAAuB;AAC7B,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAG,GAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,aAAa;QAAa,cAAc;QAAc,YAAY;QAAY,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAsB,GAAG,qBAAqB,CAAC,EAAE,MAAM;SAAC;QAAG,IAAI,OAAO,SAAS,QAAQ;QAAW,IAAI,OAAO,SAAS,QAAQ;QAAW,GAAG;QAAQ,QAAQ;QAAe,MAAM;IAAc;AAE/a,MAAM,wBAAwB,IAAM;AACpC,IAAI,WAAW,CAAC;IACZ,MAAM,cAAc,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,mBAAmB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,mBAAmB,EAAG;QAC5jB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM,QAAQ;QACd,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC,MAAM,EAAE,YAAY,aAAa,MAAM,EAAE,CAAC,EAAE;YAAC;YAAa;SAAK;QACrG,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC,MAAM,EAAE,YAAY,WAAW,MAAM,EAAE,CAAC,EAAE;YAAC;YAAW;SAAK;QAC/F,IAAI,QAAQ;YACR,OAAO;QACX;QACA,MAAM,cAAc,CAAC;YACjB,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,GAAG,MAAM,QAAQ;YAC/F,MAAM,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACxC,IAAI,CAAC,MAAM;gBACP;YACJ;YACA,IAAI,oBAAoB;gBACpB,MAAM,QAAQ,CAAC;oBAAE,sBAAsB;gBAAM;gBAC7C,IAAI,KAAK,QAAQ,IAAI,sBAAsB;oBACvC,sBAAsB;wBAAE,OAAO,EAAE;wBAAE,OAAO;4BAAC;yBAAK;oBAAC;oBACjD,QAAQ,OAAO,EAAE;gBACrB,OACK;oBACD,iBAAiB;wBAAC;qBAAG;gBACzB;YACJ;YACA,IAAI,SAAS;gBACT,QAAQ,OAAO;YACnB;QACJ;QACA,MAAM,2BAA2B,kBAAkB,IAAI,MAAM,QAAQ,EAAE;QACvE,MAAM,oBAAoB,kBAAkB,IAAI,MAAM,QAAQ,EAAE;QAChE,MAAM,mBAAmB,kBAAkB,IAAI,MAAM,QAAQ,EAAE;QAC/D,MAAM,kBAAkB,kBAAkB,IAAI,MAAM,QAAQ,EAAE;QAC9D,MAAM,mBAAmB,kBAAkB,IAAI,MAAM,QAAQ,EAAE;QAC/D,MAAM,oBAAoB,CAAC,OAAO;YAC9B,yDAAyD;YACzD,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB;YACJ;YACA,MAAM,EAAE,KAAK,EAAE,mBAAmB,sBAAsB,EAAE,GAAG,MAAM,QAAQ;YAC3E,MAAM,SAAS,iBAAiB,SAAS;YACzC,MAAM,WAAW,CAAC,iBAAiB,iBAAiB,cAAc,KAAK;YACvE,MAAM,aAAa,iBAAiB,WAAW;YAC/C,MAAM,oBAAoB,0BAA0B;YACpD,MAAM,WAAW;YACjB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YACxC,YAAY;YACZ,mBAAmB,OAAO,MAAM;YAChC,MAAM,kBAAkB,CAAC;gBACrB,YAAY;gBACZ,iBAAiB,KAAK,MAAM;YAChC;YACA,MAAM,gBAAgB,CAAC,aAAe,cAAc,MAAM;YAC1D,kBAAkB;gBACd;gBACA;gBACA;gBACA,WAAW;gBACX;gBACA,UAAU,MAAM,QAAQ;gBACxB,UAAU,MAAM,QAAQ;gBACxB;gBACA,iBAAiB;gBACjB,gBAAgB;YACpB;QACJ;QACA,MAAM,+BAA+B,CAAC,QAAU,kBAAkB,OAAO;QACzE,MAAM,+BAA+B,CAAC,QAAU,kBAAkB,OAAO;QACzE,MAAM,0BAA0B,IAAM,eAAe;QACrD,MAAM,wBAAwB,IAAM,eAAe;QACnD,MAAM,WAAW,CAAC,sBAAsB,CAAC;QACzC,MAAM,YAAY,CAAC;YACf,IAAI,CAAC,uBAAuB,qBAAqB,QAAQ,CAAC,MAAM,GAAG,KAAK,oBAAoB;gBACxF,MAAM,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;gBACzE,MAAM,WAAW,MAAM,GAAG,KAAK;gBAC/B,IAAI,UAAU;oBACV,QAAQ,OAAO,EAAE;oBACjB,sBAAsB;wBAAE,OAAO;4BAAC,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;yBAAI;oBAAC;gBACpE,OACK;oBACD,iBAAiB;wBAAC;qBAAG;gBACzB;YACJ;QACJ;QACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;YAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;gBACzC;gBACA,CAAC,iBAAiB,EAAE,MAAM;gBAC1B;gBACA;oBAAE;oBAAU;oBAAU;oBAAU,UAAU;gBAAY;aACzD;YAAG,SAAS;YAAa,eAAe;YAA0B,eAAe;YAAmB,cAAc;YAAkB,aAAa;YAAiB,cAAc;YAAkB,WAAW,cAAc,YAAY;YAAW,UAAU,cAAc,IAAI;YAAW,MAAM,cAAc,WAAW;YAAO,eAAe,CAAC,SAAS,EAAE,IAAI;YAAE,cAAc,cAAc,OAAO,YAAY,YAAY,YAAY,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE,QAAQ;YAAE,oBAAoB,cAAc,GAAG,mBAAmB,CAAC,EAAE,MAAM,GAAG;YAAW,KAAK;QAAQ,GACxiB,CAAC,YAAa,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;YAAE,IAAI;YAAI,QAAQ;YAAQ,QAAQ;YAAQ,UAAU;YAAU,UAAU;YAAU,OAAO;YAAO,YAAY;YAAY,aAAa;YAAa,cAAc;YAAc,gBAAgB;YAAgB,qBAAqB;YAAqB,MAAM;YAAM,OAAO;YAAO,SAAS;YAAS,SAAS;YAAS,SAAS;YAAS,SAAS;YAAS,gBAAgB;YAAgB,gBAAgB;YAAgB,gBAAgB;YAAgB,gBAAgB;YAAgB,aAAa;YAAgB,WAAW;YAAc,aAAa;YAAa,kBAAkB;QAAiB,IAC9nB,mBAAoB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MACpD,CAAC,oBAAoB,YAAY,oBAAoB,IAAI,KAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;YAAE,UAAU;YAAgB,SAAS;YAAS,SAAS;YAAS,QAAQ;YAAiB,aAAa;YAA8B,cAAc;YAAyB,YAAY;YAAuB,MAAM;QAAS,IAC5T,CAAC,oBAAoB,YAAY,oBAAoB,IAAI,KAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;YAAE,UAAU;YAAgB,SAAS;YAAS,SAAS;YAAS,QAAQ;YAAiB,aAAa;YAA8B,cAAc;YAAyB,YAAY;YAAuB,MAAM;QAAS;IACxU;IACA,YAAY,WAAW,GAAG;IAC1B,OAAO,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAChB;AAEA,SAAS,gBAAgB,SAAS;IAC9B,MAAM,gBAAgB;QAClB,SAAS,SAAU,UAAU,OAAO,IAAI;QACxC,UAAU,SAAU,UAAU,MAAM,IAAI;QACxC,MAAM,SAAU,UAAU,IAAI,IAAI;QAClC,YAAY,SAAU,UAAU,IAAI,IAAI;QACxC,cAAc,SAAU,UAAU,YAAY,IAAI;IACtD;IACA,MAAM,eAAe,CAAC;IACtB,MAAM,eAAe,OAAO,IAAI,CAAC,WAC5B,MAAM,CAAC,CAAC,IAAM,CAAC;YAAC;YAAW;SAAS,CAAC,QAAQ,CAAC,IAC9C,MAAM,CAAC,CAAC,KAAK;QACd,GAAG,CAAC,IAAI,GAAG,SAAU,SAAS,CAAC,IAAI,IAAI;QACvC,OAAO;IACX,GAAG;IACH,OAAO;QACH,GAAG,aAAa;QAChB,GAAG,YAAY;IACnB;AACJ;AACA,SAAS,kBAAkB,QAAQ,EAAE,QAAQ,EAAE,SAAS,IAAI;IACxD,MAAM,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,SAAS,CAAC;IACvC,MAAM,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,SAAS,CAAC;IACvC,MAAM,QAAQ,QAAQ,SAAS,SAAS,KAAK;IAC7C,MAAM,SAAS,QAAQ,UAAU,SAAS,MAAM;IAChD,OAAQ;QACJ,KAAK,SAAS,GAAG;YACb,OAAO;gBACH,GAAG,IAAI,QAAQ;gBACf;YACJ;QACJ,KAAK,SAAS,KAAK;YACf,OAAO;gBACH,GAAG,IAAI;gBACP,GAAG,IAAI,SAAS;YACpB;QACJ,KAAK,SAAS,MAAM;YAChB,OAAO;gBACH,GAAG,IAAI,QAAQ;gBACf,GAAG,IAAI;YACX;QACJ,KAAK,SAAS,IAAI;YACd,OAAO;gBACH;gBACA,GAAG,IAAI,SAAS;YACpB;IACR;AACJ;AACA,SAAS,UAAU,MAAM,EAAE,QAAQ;IAC/B,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IACA,IAAI,OAAO,MAAM,KAAK,KAAK,CAAC,UAAU;QAClC,OAAO,MAAM,CAAC,EAAE;IACpB,OACK,IAAI,UAAU;QACf,OAAO,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,aAAa;IACpD;IACA,OAAO;AACX;AACA,MAAM,mBAAmB,CAAC,gBAAgB,cAAc,gBAAgB,gBAAgB,cAAc;IAClG,MAAM,kBAAkB,kBAAkB,gBAAgB,gBAAgB;IAC1E,MAAM,kBAAkB,kBAAkB,gBAAgB,gBAAgB;IAC1E,OAAO;QACH,SAAS,gBAAgB,CAAC;QAC1B,SAAS,gBAAgB,CAAC;QAC1B,SAAS,gBAAgB,CAAC;QAC1B,SAAS,gBAAgB,CAAC;IAC9B;AACJ;AACA,SAAS,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAG;IAC5H,MAAM,UAAU;QACZ,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;QACpC,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;QACpC,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG,aAAa,UAAU,CAAC,GAAG;QACtD,IAAI,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG,cAAc,UAAU,CAAC,GAAG;IAC3D;IACA,IAAI,QAAQ,CAAC,KAAK,QAAQ,EAAE,EAAE;QAC1B,QAAQ,EAAE,IAAI;IAClB;IACA,IAAI,QAAQ,CAAC,KAAK,QAAQ,EAAE,EAAE;QAC1B,QAAQ,EAAE,IAAI;IAClB;IACA,MAAM,UAAU,UAAU;QACtB,GAAG,CAAC,IAAI,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;QACpC,GAAG,CAAC,IAAI,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;QACpC,OAAO,QAAQ,SAAS,CAAC,EAAE;QAC3B,QAAQ,SAAS,SAAS,CAAC,EAAE;IACjC;IACA,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAC7F,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IAC7F,MAAM,kBAAkB,KAAK,IAAI,CAAC,WAAW;IAC7C,OAAO,kBAAkB;AAC7B;AACA,SAAS,YAAY,IAAI;IACrB,MAAM,eAAe,MAAM,CAAC,gBAAgB,EAAE,gBAAgB;IAC9D,MAAM,UAAU,gBACZ,MAAM,SACN,MAAM,UACN,OAAO,MAAM,kBAAkB,MAAM,eACrC,OAAO,MAAM,kBAAkB,MAAM;IACzC,OAAO;QACH;YACI,GAAG,MAAM,kBAAkB,KAAK;YAChC,GAAG,MAAM,kBAAkB,KAAK;YAChC,OAAO,MAAM,SAAS;YACtB,QAAQ,MAAM,UAAU;QAC5B;QACA;QACA,CAAC,CAAC;KACL;AACL;AAEA,MAAM,kBAAkB;IAAC;QAAE,OAAO;QAAG,YAAY;QAAM,OAAO,EAAE;IAAC;CAAE;AACnE,SAAS,mBAAmB,KAAK,EAAE,aAAa,EAAE,uBAAuB,KAAK;IAC1E,IAAI,WAAW,CAAC;IAChB,MAAM,cAAc,MAAM,MAAM,CAAC,CAAC,MAAM;QACpC,MAAM,YAAY,UAAU,KAAK,MAAM;QACvC,IAAI,IAAI,YAAY,KAAK,MAAM,GAAG;QAClC,IAAI,sBAAsB;YACtB,MAAM,aAAa,cAAc,GAAG,CAAC,KAAK,MAAM;YAChD,MAAM,aAAa,cAAc,GAAG,CAAC,KAAK,MAAM;YAChD,MAAM,8BAA8B,KAAK,QAAQ,IAAI,YAAY,YAAY,YAAY;YACzF,MAAM,iBAAiB,KAAK,GAAG,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAK,GAAG,YAAY,CAAC,gBAAgB,EAAE,KAAK,GAAG;YAC9G,IAAI,CAAC,YAAY,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,8BAA8B,iBAAiB,CAAC;QACzF;QACA,IAAI,IAAI,CAAC,EAAE,EAAE;YACT,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACjB,OACK;YACD,IAAI,CAAC,EAAE,GAAG;gBAAC;aAAK;QACpB;QACA,WAAW,IAAI,WAAW,IAAI;QAC9B,OAAO;IACX,GAAG,CAAC;IACJ,MAAM,WAAW,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1D,MAAM,QAAQ,CAAC;QACf,OAAO;YACH;YACA;YACA,YAAY,UAAU;QAC1B;IACJ;IACA,IAAI,SAAS,MAAM,KAAK,GAAG;QACvB,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,iBAAiB,EAAE,aAAa,EAAE,oBAAoB;IAC3E,MAAM,QAAQ,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,CAAC,mBAAmB;YACpB,OAAO,EAAE,KAAK;QAClB;QACA,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YACnB,MAAM,aAAa,cAAc,GAAG,CAAC,EAAE,MAAM;YAC7C,MAAM,aAAa,cAAc,GAAG,CAAC,EAAE,MAAM;YAC7C,OAAQ,YAAY,SAChB,YAAY,UACZ,YAAY,SACZ,YAAY,UACZ,cAAc;gBACV,WAAW,WAAW,gBAAgB,IAAI;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBACvD,WAAW,WAAW,gBAAgB,IAAI;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBACvD,aAAa,WAAW,KAAK;gBAC7B,cAAc,WAAW,MAAM;gBAC/B,aAAa,WAAW,KAAK;gBAC7B,cAAc,WAAW,MAAM;gBAC/B,OAAO,EAAE,KAAK;gBACd,QAAQ,EAAE,MAAM;gBAChB,WAAW,EAAE,SAAS;YAC1B;QACR;IACJ,GAAG;QAAC;QAAmB;KAAc;IACrC,OAAO,mBAAmB,OAAO,eAAe;AACpD;AAEA,MAAM,cAAc,CAAC,EAAE,QAAQ,MAAM,EAAE,cAAc,CAAC,EAAE;IACpD,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QAAE,OAAO;YACzC,QAAQ;YACR;QACJ;QAAG,eAAe;QAAS,gBAAgB;QAAS,MAAM;QAAQ,QAAQ;IAAiB;AACnG;AACA,MAAM,oBAAoB,CAAC,EAAE,QAAQ,MAAM,EAAE,cAAc,CAAC,EAAE;IAC1D,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QAAE,OAAO;YACzC,QAAQ;YACR,MAAM;YACN;QACJ;QAAG,eAAe;QAAS,gBAAgB;QAAS,QAAQ;IAAuB;AAC3F;AACA,MAAM,gBAAgB;IAClB,CAAC,WAAW,KAAK,CAAC,EAAE;IACpB,CAAC,WAAW,WAAW,CAAC,EAAE;AAC9B;AACA,SAAS,gBAAgB,IAAI;IACzB,MAAM,QAAQ;IACd,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACnB,MAAM,eAAe,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;QACzE,IAAI,CAAC,cAAc;YACf,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,aAAa,CAAC,WAAW,CAAC;YAC5D,OAAO;QACX;QACA,OAAO,aAAa,CAAC,KAAK;IAC9B,GAAG;QAAC;KAAK;IACT,OAAO;AACX;AAEA,MAAM,SAAS,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,IAAI,EAAE,SAAS,IAAI,EAAE,cAAc,aAAa,EAAE,WAAW,EAAE,SAAS,oBAAoB,EAAG;IACtI,MAAM,UAAS,gBAAgB;IAC/B,IAAI,CAAC,SAAQ;QACT,OAAO;IACX;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAAE,WAAW;QAAyB,IAAI;QAAI,aAAa,GAAG,OAAO;QAAE,cAAc,GAAG,QAAQ;QAAE,SAAS;QAAiB,aAAa;QAAa,QAAQ;QAAQ,MAAM;QAAK,MAAM;IAAI,GAC7N,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAQ;QAAE,OAAO;QAAO,aAAa;IAAY;AAC7E;AACA,MAAM,iBAAiB,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,GAAK,CAAC;QAChD,MAAM,MAAM,EAAE;QACd,OAAO,EAAE,KAAK,CACT,MAAM,CAAC,CAAC,SAAS;YAClB;gBAAC,KAAK,WAAW;gBAAE,KAAK,SAAS;aAAC,CAAC,OAAO,CAAC,CAAC;gBACxC,IAAI,UAAU,OAAO,WAAW,UAAU;oBACtC,MAAM,WAAW,YAAY,QAAQ;oBACrC,IAAI,CAAC,IAAI,QAAQ,CAAC,WAAW;wBACzB,QAAQ,IAAI,CAAC;4BAAE,IAAI;4BAAU,OAAO,OAAO,KAAK,IAAI;4BAAc,GAAG,MAAM;wBAAC;wBAC5E,IAAI,IAAI,CAAC;oBACb;gBACJ;YACJ;YACA,OAAO;QACX,GAAG,EAAE,EACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE;IAC/C;AACA,4GAA4G;AAC5G,uHAAuH;AACvH,0DAA0D;AAC1D,MAAM,oBAAoB,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;IAC7C,MAAM,UAAU,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,eAAe;QAAE;QAAc;IAAK,IAAI;QAAC;QAAc;KAAK,GACjG,yFAAyF;IACzF,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IACvE,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ,MAAM,QAAQ,GAAG,CAAC,CAAC,SAAY,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;YAAE,IAAI,OAAO,EAAE;YAAE,KAAK,OAAO,EAAE;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO,OAAO,KAAK;YAAE,OAAO,OAAO,KAAK;YAAE,QAAQ,OAAO,MAAM;YAAE,aAAa,OAAO,WAAW;YAAE,aAAa,OAAO,WAAW;YAAE,QAAQ,OAAO,MAAM;QAAC;AAChT;AACA,kBAAkB,WAAW,GAAG;AAChC,IAAI,sBAAsB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE/B,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,kBAAkB,EAAE,gBAAgB;QACpC,gBAAgB,EAAE,cAAc;QAChC,gBAAgB,EAAE,cAAc;QAChC,oBAAoB,EAAE,kBAAkB;QACxC,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,MAAM;QAChB,gBAAgB,EAAE,cAAc;QAChC,eAAe,EAAE,aAAa;QAC9B,SAAS,EAAE,OAAO;IACtB,CAAC;AACD,MAAM,eAAe,CAAC,EAAE,kBAAkB,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,mBAAmB,EAAG;IAClU,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAClJ,MAAM,WAAW,gBAAgB,2BAA2B,eAAe;IAC3E,IAAI,CAAC,OAAO;QACR,OAAO;IACX;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MACxC,SAAS,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YAAE,KAAK;YAAO,OAAO;gBAAE,QAAQ;YAAM;YAAG,OAAO;YAAO,QAAQ;YAAQ,WAAW;QAA0C,GAClM,cAAc,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qBAAqB;YAAE,cAAc;YAAoB,MAAM;QAAK,IACtG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,MAAM,MAAM,GAAG,CAAC,CAAC;YACtC,MAAM,CAAC,gBAAgB,oBAAoB,cAAc,GAAG,YAAY,cAAc,GAAG,CAAC,KAAK,MAAM;YACrG,MAAM,CAAC,gBAAgB,oBAAoB,cAAc,GAAG,YAAY,cAAc,GAAG,CAAC,KAAK,MAAM;YACrG,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAClC,OAAO;YACX;YACA,IAAI,WAAW,KAAK,IAAI,IAAI;YAC5B,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;gBACtB,UAAU,OAAO,aAAa,CAAC,WAAW,CAAC;gBAC3C,WAAW;YACf;YACA,MAAM,gBAAgB,SAAS,CAAC,SAAS,IAAI,UAAU,OAAO;YAC9D,kGAAkG;YAClG,MAAM,oBAAoB,mBAAmB,eAAe,MAAM,GAC5D,mBAAmB,MAAM,GACzB,CAAC,mBAAmB,MAAM,IAAI,EAAE,EAAE,MAAM,CAAC,mBAAmB,MAAM,IAAI,EAAE;YAC9E,MAAM,eAAe,UAAU,mBAAmB,MAAM,EAAE,KAAK,YAAY;YAC3E,MAAM,eAAe,UAAU,mBAAmB,KAAK,YAAY;YACnE,MAAM,iBAAiB,cAAc,YAAY,SAAS,MAAM;YAChE,MAAM,iBAAiB,cAAc,YAAY,SAAS,GAAG;YAC7D,MAAM,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY;YAClG,MAAM,oBAAoB,KAAK,aAAa,IAAI,KAAK,SAAS;YAC9D,MAAM,kBAAkB,OAAO,gBAAgB,eAC3C,CAAC,qBAAsB,kBAAkB,OAAO,sBAAsB,WAAY;YACtF,IAAI,CAAC,gBAAgB,CAAC,cAAc;gBAChC,UAAU,OAAO,aAAa,CAAC,WAAW,CAAC,cAAc;gBACzD,OAAO;YACX;YACA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,iBAAiB,gBAAgB,cAAc,gBAAgB,gBAAgB,cAAc;YAC5I,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,eAAe;gBAAE,KAAK,KAAK,EAAE;gBAAE,IAAI,KAAK,EAAE;gBAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;oBAAC,KAAK,SAAS;oBAAE;iBAAe;gBAAG,MAAM;gBAAU,MAAM,KAAK,IAAI;gBAAE,UAAU,CAAC,CAAC,KAAK,QAAQ;gBAAE,UAAU,CAAC,CAAC,KAAK,QAAQ;gBAAE,QAAQ,CAAC,CAAC,KAAK,MAAM;gBAAE,OAAO,KAAK,KAAK;gBAAE,YAAY,KAAK,UAAU;gBAAE,aAAa,KAAK,WAAW;gBAAE,cAAc,KAAK,YAAY;gBAAE,gBAAgB,KAAK,cAAc;gBAAE,qBAAqB,KAAK,mBAAmB;gBAAE,OAAO,KAAK,KAAK;gBAAE,QAAQ,KAAK,MAAM;gBAAE,QAAQ,KAAK,MAAM;gBAAE,gBAAgB,KAAK,YAAY;gBAAE,gBAAgB,KAAK,YAAY;gBAAE,WAAW,KAAK,SAAS;gBAAE,aAAa,KAAK,WAAW;gBAAE,SAAS;gBAAS,SAAS;gBAAS,SAAS;gBAAS,SAAS;gBAAS,gBAAgB;gBAAgB,gBAAgB;gBAAgB,oBAAoB;gBAAoB,eAAe;gBAAmB,cAAc;gBAAkB,aAAa;gBAAiB,cAAc;gBAAkB,SAAS;gBAAa,mBAAmB;gBAAmB,aAAa;gBAAa,kBAAkB;gBAAkB,gBAAgB;gBAAgB,iBAAiB;gBAAiB,MAAM;gBAAM,WAAW,KAAK,SAAS;gBAAE,aAAa;gBAAa,iBAAiB;gBAAiB,aAAa,iBAAiB,OAAO,KAAK,WAAW,GAAG;gBAAW,kBAAkB,KAAK,gBAAgB;gBAAE,qBAAqB;YAAoB;QACh1C,OACJ;AACR;AACA,aAAa,WAAW,GAAG;AAC3B,IAAI,iBAAiB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAE1B,MAAM,aAAa,CAAC,IAAM,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG,SAAS,SAAS,EAAE,QAAQ,EAAE;IAC1B,MAAM,YAAY,SAAS;IAC3B,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW;QAA8C,OAAO;YAAE;QAAU;IAAE,GAAG;AAC1H;AAEA,SAAS,iBAAiB,MAAM;IAC5B,MAAM,aAAa;IACnB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,IAAI,CAAC,cAAc,OAAO,IAAI,WAAW,mBAAmB,IAAI,QAAQ;YACpE,WAAW,IAAM,OAAO,aAAa;YACrC,cAAc,OAAO,GAAG;QAC5B;IACJ,GAAG;QAAC;QAAQ,WAAW,mBAAmB;KAAC;AAC/C;AAEA,MAAM,mBAAmB;IACrB,CAAC,SAAS,IAAI,CAAC,EAAE,SAAS,KAAK;IAC/B,CAAC,SAAS,KAAK,CAAC,EAAE,SAAS,IAAI;IAC/B,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,MAAM;IAC/B,CAAC,SAAS,MAAM,CAAC,EAAE,SAAS,GAAG;AACnC;AACA,MAAM,iBAAiB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,mBAAmB,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAG;IACvH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,cAAc,EAAE,GAAG,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAM,CAAC;YAClF,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC;YAC9B,UAAU,EAAE,kBAAkB;YAC9B,KAAK,CAAC,EAAE,kBAAkB,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE;YAC/D,KAAK,CAAC,EAAE,kBAAkB,CAAC,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,EAAE;YAC/D,gBAAgB,EAAE,cAAc;QACpC,CAAC,GAAG;QAAC;KAAO,GAAG,iLAAA,CAAA,UAAO;IACtB,MAAM,mBAAmB,UAAU,CAAC,gBAAgB,EAAE;IACtD,IAAI,eAAe,kBAAkB,CAAC,WAAW;IACjD,IAAI,mBAAmB,eAAe,KAAK,EAAE;QACzC,eAAe,eAAe,eAAe,kBAAkB,CAAC,eAAe,WAAW,WAAW,SAAS;IAClH;IACA,IAAI,CAAC,YAAY,CAAC,cAAc;QAC5B,OAAO;IACX;IACA,MAAM,aAAa,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,YAAY,YAAY,CAAC,EAAE;IAC3F,MAAM,cAAc,aAAa,WAAW,CAAC,GAAG,WAAW,KAAK,GAAG,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,IAAI;IAC/F,MAAM,cAAc,aAAa,WAAW,CAAC,GAAG,WAAW,MAAM,GAAG,IAAI,SAAS,MAAM,IAAI;IAC3F,MAAM,QAAQ,CAAC,SAAS,gBAAgB,EAAE,KAAK,CAAC,IAAI;IACpD,MAAM,QAAQ,CAAC,SAAS,gBAAgB,EAAE,KAAK,CAAC,IAAI;IACpD,MAAM,eAAe,YAAY;IACjC,MAAM,aAAa,eAAe,gBAAgB,CAAC,aAAa,GAAG;IACnE,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAC9B,OAAO;IACX;IACA,IAAI,iBAAiB;QACjB,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iBAAiB;YAAE,oBAAoB;YAAM,qBAAqB;YAAO,UAAU;YAAU,YAAY;YAAY,OAAO;YAAO,OAAO;YAAO,KAAK;YAAK,KAAK;YAAK,cAAc;YAAc,YAAY;YAAY,kBAAkB;QAAiB;IAC5R;IACA,IAAI,QAAQ;IACZ,MAAM,aAAa;QACf,SAAS;QACT,SAAS;QACT,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,gBAAgB;IACpB;IACA,IAAI,SAAS,mBAAmB,MAAM,EAAE;QACpC,wEAAwE;QACxE,CAAC,MAAM,GAAG,cAAc;IAC5B,OACK,IAAI,SAAS,mBAAmB,IAAI,EAAE;QACvC,CAAC,MAAM,GAAG,kBAAkB;YACxB,GAAG,UAAU;YACb,cAAc;QAClB;IACJ,OACK,IAAI,SAAS,mBAAmB,UAAU,EAAE;QAC7C,CAAC,MAAM,GAAG,kBAAkB;IAChC,OACK,IAAI,SAAS,mBAAmB,YAAY,EAAE;QAC/C,CAAC,MAAM,GAAG,oBAAoB;IAClC,OACK;QACD,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK;IAC9C;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;QAAE,GAAG;QAAO,MAAM;QAAQ,WAAW;QAA+B,OAAO;IAAM;AACxH;AACA,eAAe,WAAW,GAAG;AAC7B,MAAM,aAAa,CAAC,IAAM,CAAC;QACvB,QAAQ,EAAE,gBAAgB;QAC1B,YAAY,EAAE,oBAAoB;QAClC,kBAAkB,EAAE,gBAAgB;QACpC,kBAAkB,EAAE,gBAAgB;QACpC,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,MAAM;IACpB,CAAC;AACD,SAAS,sBAAsB,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE;IACrE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG,SAAS,YAAY,iLAAA,CAAA,UAAO;IAC9G,MAAM,UAAU,CAAC,CAAC,CAAC,UAAU,cAAc,SAAS,gBAAgB;IACpE,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,OAAO;QAAgB,OAAO;QAAO,QAAQ;QAAQ,WAAW;IAAqE,GACtK,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK;QAAE,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAA0B;SAAiB;IAAE,GACnF,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB;QAAE,QAAQ;QAAQ,YAAY;QAAY,OAAO;QAAO,MAAM;QAAM,iBAAiB;QAAW,kBAAkB;IAAiB;AACnL;AAEA,8DAA8D;AAC9D,SAAS,mBAAmB,eAAe,EAAE,WAAW;IACpD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,QAAQ;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,wCAA4C;YACxC,MAAM,WAAW,OAAO,IAAI,CAAC;YAC7B,IAAI,CAAA,GAAA,iLAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,EAAE,WAAW;gBACzC,MAAM,QAAQ,GAAG,OAAO,GAAG,OAAO,aAAa,CAAC,WAAW;YAC/D;YACA,aAAa,OAAO,GAAG;QAC3B;QACA,OAAO,YAAY;IACvB,GAAG;QAAC;KAAgB;IACpB,OAAO;AACX;AAEA,MAAM,YAAY,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE,qBAAqB,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,aAAa,EAAE,yBAAyB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAG;IACnmC,MAAM,mBAAmB,mBAAmB,WAAW;IACvD,MAAM,mBAAmB,mBAAmB,WAAW;IACvD,iBAAiB;IACjB,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB;QAAE,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,mBAAmB;QAAmB,cAAc;QAAc,eAAe;QAAe,kBAAkB;QAAkB,iBAAiB;QAAiB,eAAe;QAAe,kBAAkB;QAAkB,gBAAgB;QAAgB,uBAAuB;QAAuB,sBAAsB;QAAsB,uBAAuB;QAAuB,oBAAoB;QAAoB,QAAQ;QAAQ,aAAa;QAAa,WAAW;QAAW,cAAc;QAAc,aAAa;QAAa,mBAAmB;QAAmB,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,WAAW;QAAW,iBAAiB;QAAiB,iBAAiB;QAAiB,SAAS;QAAS,SAAS;QAAS,wBAAwB;QAAwB,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,gBAAgB;QAAgB,qBAAqB;IAAoB,GACnsC,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU,MAC1B,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB;QAAE,WAAW;QAAkB,aAAa;QAAa,mBAAmB;QAAmB,2BAA2B;QAA2B,mBAAmB;QAAmB,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,aAAa;QAAa,kBAAkB;QAAkB,gBAAgB;QAAgB,iBAAiB;QAAiB,oBAAoB;QAAoB,gBAAgB;QAAgB,sBAAsB,CAAC,CAAC;QAAsB,qBAAqB;QAAqB,MAAM;IAAK,GAChnB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,uBAAuB;QAAE,OAAO;QAAqB,MAAM;QAAoB,WAAW;QAAyB,gBAAgB;IAA6B,KACxL,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,WAAW;IAAiC,IACzE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gBAAgB;QAAE,WAAW;QAAkB,aAAa;QAAa,mBAAmB;QAAmB,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,mBAAmB;QAAmB,mBAAmB;QAAmB,2BAA2B;QAA2B,gBAAgB;QAAgB,iBAAiB;QAAiB,qBAAqB;QAAqB,YAAY;QAAY,YAAY;QAAY,MAAM;IAAK;AACliB;AACA,UAAU,WAAW,GAAG;AACxB,IAAI,cAAc,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE;AAEvB,MAAM,iBAAiB;IACnB;QAAC,OAAO,iBAAiB;QAAE,OAAO,iBAAiB;KAAC;IACpD;QAAC,OAAO,iBAAiB;QAAE,OAAO,iBAAiB;KAAC;CACvD;AACD,MAAM,eAAe;IACjB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,WAAW;QAAC;QAAG;QAAG;KAAE;IACpB,eAAe,IAAI;IACnB,OAAO,EAAE;IACT,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,QAAQ;IACR,aAAa;IACb,eAAe;IACf,SAAS;IACT,SAAS;IACT,iBAAiB;IACjB,YAAY;IACZ,sBAAsB;IACtB,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;IAClB,oBAAoB;IACpB,sBAAsB;IACtB,oBAAoB;QAAE,GAAG;QAAG,GAAG;IAAE;IACjC,kBAAkB;IAClB,gBAAgB,eAAe,MAAM;IACrC,SAAS;IACT,cAAc;IACd,gBAAgB;IAChB,YAAY;QAAC;QAAG;KAAE;IAClB,mBAAmB;IACnB,UAAU;QAAC;QAAI;KAAG;IAClB,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,oBAAoB;IACpB,sBAAsB;IACtB,eAAe;IACf,mBAAmB;IACnB,sBAAsB;IACtB,mBAAmB,EAAE;IACrB,sBAAsB;IACtB,uBAAuB;IACvB,qBAAqB;IACrB,4BAA4B;IAC5B,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,mBAAmB;IACnB,kBAAkB;IAClB,SAAS;IACT,mBAAmB;AACvB;AAEA,MAAM,gBAAgB,IAAM,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,CAAC,KAAK,MAAQ,CAAC;YAC5D,GAAG,YAAY;YACf,UAAU,CAAC;gBACP,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,oBAAoB,EAAE,GAAG;gBAC5D,IAAI;oBAAE,eAAe,oBAAoB,OAAO,eAAe,YAAY;gBAAsB;YACrG;YACA,UAAU;gBACN,OAAO,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,MAAM;YAChD;YACA,UAAU,CAAC;gBACP,MAAM,EAAE,qBAAqB,CAAC,CAAC,EAAE,GAAG;gBACpC,IAAI;oBAAE,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;4BAAE,GAAG,kBAAkB;4BAAE,GAAG,CAAC;wBAAC,CAAC;gBAAG;YACrE;YACA,yBAAyB,CAAC,OAAO;gBAC7B,MAAM,kBAAkB,OAAO,UAAU;gBACzC,MAAM,kBAAkB,OAAO,UAAU;gBACzC,MAAM,gBAAgB,kBAChB,oBAAoB,OAAO,IAAI,OAAO,MAAM,UAAU,EAAE,MAAM,oBAAoB,IAClF,IAAI;gBACV,MAAM,YAAY,kBAAkB,QAAQ,EAAE;gBAC9C,IAAI;oBAAE;oBAAe,OAAO;oBAAW;oBAAiB;gBAAgB;YAC5E;YACA,sBAAsB,CAAC;gBACnB,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,OAAO,EAAE,UAAU,EAAG,GAAG;gBACvH,MAAM,eAAe,SAAS,cAAc;gBAC5C,IAAI,CAAC,cAAc;oBACf;gBACJ;gBACA,MAAM,QAAQ,OAAO,gBAAgB,CAAC;gBACtC,MAAM,EAAE,KAAK,IAAI,EAAE,GAAG,IAAI,OAAO,iBAAiB,CAAC,MAAM,SAAS;gBAClE,MAAM,UAAU,QAAQ,MAAM,CAAC,CAAC,KAAK;oBACjC,MAAM,OAAO,cAAc,GAAG,CAAC,OAAO,EAAE;oBACxC,IAAI,MAAM,QAAQ;wBACd,cAAc,GAAG,CAAC,KAAK,EAAE,EAAE;4BACvB,GAAG,IAAI;4BACP,CAAC,gBAAgB,EAAE;gCACf,GAAG,IAAI,CAAC,gBAAgB;gCACxB,6DAA6D;gCAC7D,mEAAmE;gCACnE,cAAc;4BAClB;wBACJ;oBACJ,OACK,IAAI,MAAM;wBACX,MAAM,aAAa,cAAc,OAAO,WAAW;wBACnD,MAAM,WAAW,CAAC,CAAC,CAAC,WAAW,KAAK,IAChC,WAAW,MAAM,IACjB,CAAC,KAAK,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,MAAM,KAAK,WAAW,MAAM,IAAI,OAAO,WAAW,CAAC;wBAChG,IAAI,UAAU;4BACV,cAAc,GAAG,CAAC,KAAK,EAAE,EAAE;gCACvB,GAAG,IAAI;gCACP,CAAC,gBAAgB,EAAE;oCACf,GAAG,IAAI,CAAC,gBAAgB;oCACxB,cAAc;wCACV,QAAQ,gBAAgB,WAAW,OAAO,WAAW,EAAE,MAAM;wCAC7D,QAAQ,gBAAgB,WAAW,OAAO,WAAW,EAAE,MAAM;oCACjE;gCACJ;gCACA,GAAG,UAAU;4BACjB;4BACA,IAAI,IAAI,CAAC;gCACL,IAAI,KAAK,EAAE;gCACX,MAAM;gCACN;4BACJ;wBACJ;oBACJ;oBACA,OAAO;gBACX,GAAG,EAAE;gBACL,4BAA4B,eAAe;gBAC3C,MAAM,wBAAwB,qBACzB,iBAAiB,CAAC,qBAAqB,QAAQ,KAAK;oBAAE,SAAS;oBAAM,GAAG,oBAAoB;gBAAC;gBAClG,IAAI;oBAAE,eAAe,IAAI,IAAI;oBAAgB,mBAAmB;gBAAsB;gBACtF,IAAI,SAAS,SAAS,GAAG;oBACrB,gBAAgB;gBACpB;YACJ;YACA,qBAAqB,CAAC,eAAe,kBAAkB,IAAI,EAAE,WAAW,KAAK;gBACzE,MAAM,EAAE,kBAAkB,EAAE,GAAG;gBAC/B,MAAM,UAAU,cAAc,GAAG,CAAC,CAAC;oBAC/B,MAAM,SAAS;wBACX,IAAI,KAAK,EAAE;wBACX,MAAM;wBACN;oBACJ;oBACA,IAAI,iBAAiB;wBACjB,OAAO,gBAAgB,GAAG,KAAK,gBAAgB;wBAC/C,OAAO,QAAQ,GAAG,KAAK,QAAQ;oBACnC;oBACA,OAAO;gBACX;gBACA,mBAAmB;YACvB;YACA,oBAAoB,CAAC;gBACjB,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,oBAAoB,EAAE,GAAG;gBACtG,IAAI,SAAS,QAAQ;oBACjB,IAAI,iBAAiB;wBACjB,MAAM,QAAQ,iBAAiB,SAAS;wBACxC,MAAM,oBAAoB,oBAAoB,OAAO,eAAe,YAAY;wBAChF,IAAI;4BAAE,eAAe;wBAAkB;oBAC3C;oBACA,gBAAgB;gBACpB;YACJ;YACA,kBAAkB,CAAC;gBACf,MAAM,EAAE,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAClD,IAAI;gBACJ,IAAI,eAAe;gBACnB,IAAI,sBAAsB;oBACtB,eAAe,gBAAgB,GAAG,CAAC,CAAC,SAAW,sBAAsB,QAAQ;gBACjF,OACK;oBACD,eAAe,oBAAoB,YAAY;oBAC/C,eAAe,oBAAoB,OAAO,EAAE;gBAChD;gBACA,8BAA8B;oBAC1B;oBACA;oBACA;oBACA;gBACJ;YACJ;YACA,kBAAkB,CAAC;gBACf,MAAM,EAAE,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAClD,IAAI;gBACJ,IAAI,eAAe;gBACnB,IAAI,sBAAsB;oBACtB,eAAe,gBAAgB,GAAG,CAAC,CAAC,SAAW,sBAAsB,QAAQ;gBACjF,OACK;oBACD,eAAe,oBAAoB,OAAO;oBAC1C,eAAe,oBAAoB,YAAY,EAAE;gBACrD;gBACA,8BAA8B;oBAC1B;oBACA;oBACA;oBACA;gBACJ;YACJ;YACA,uBAAuB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;gBACzC,MAAM,EAAE,OAAO,UAAU,EAAE,QAAQ,EAAE,GAAG;gBACxC,MAAM,kBAAkB,QAAQ,QAAQ;gBACxC,MAAM,kBAAkB,QAAQ,QAAQ;gBACxC,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAC;oBACtC,EAAE,QAAQ,GAAG;oBACb,OAAO,sBAAsB,EAAE,EAAE,EAAE;gBACvC;gBACA,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAC,OAAS,sBAAsB,KAAK,EAAE,EAAE;gBAClF,8BAA8B;oBAC1B;oBACA;oBACA;oBACA;gBACJ;YACJ;YACA,YAAY,CAAC;gBACT,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;gBAC5B,QAAQ,YAAY;oBAAC;oBAAS;iBAAQ;gBACtC,IAAI;oBAAE;gBAAQ;YAClB;YACA,YAAY,CAAC;gBACT,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;gBAC5B,QAAQ,YAAY;oBAAC;oBAAS;iBAAQ;gBACtC,IAAI;oBAAE;gBAAQ;YAClB;YACA,oBAAoB,CAAC;gBACjB,MAAM,MAAM,EAAE,gBAAgB;gBAC9B,IAAI;oBAAE;gBAAgB;YAC1B;YACA,uBAAuB;gBACnB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAC5B,MAAM,QAAQ;gBACd,MAAM,kBAAkB,MACnB,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EACxB,GAAG,CAAC,CAAC,IAAM,sBAAsB,EAAE,EAAE,EAAE;gBAC5C,MAAM,kBAAkB,MACnB,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,EACxB,GAAG,CAAC,CAAC,IAAM,sBAAsB,EAAE,EAAE,EAAE;gBAC5C,8BAA8B;oBAC1B,cAAc;oBACd,cAAc;oBACd;oBACA;gBACJ;YACJ;YACA,eAAe,CAAC;gBACZ,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,cAAc,OAAO,CAAC,CAAC;oBACnB,KAAK,gBAAgB,GAAG,cAAc,KAAK,QAAQ,EAAE;gBACzD;gBACA,IAAI;oBACA;oBACA,eAAe,IAAI,IAAI;gBAC3B;YACJ;YACA,OAAO,CAAC;gBACJ,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG;gBAC3E,IAAI,CAAC,UAAU,CAAC,eAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAG;oBACnD,OAAO;gBACX;gBACA,MAAM,gBAAgB,0LAAA,CAAA,eAAY,CAC7B,SAAS,CAAC,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC,EACxD,KAAK,CAAC,SAAS,CAAC,EAAE;gBACvB,MAAM,SAAS;oBACX;wBAAC;wBAAG;qBAAE;oBACN;wBAAC;wBAAO;qBAAO;iBAClB;gBACD,MAAM,uBAAuB,QAAQ,YAAY,eAAe,QAAQ;gBACxE,OAAO,SAAS,CAAC,aAAa;gBAC9B,MAAM,mBAAmB,SAAS,CAAC,EAAE,KAAK,qBAAqB,CAAC,IAC5D,SAAS,CAAC,EAAE,KAAK,qBAAqB,CAAC,IACvC,SAAS,CAAC,EAAE,KAAK,qBAAqB,CAAC;gBAC3C,OAAO;YACX;YACA,kBAAkB,IAAM,IAAI;oBACxB,kBAAkB,aAAa,gBAAgB;oBAC/C,oBAAoB,aAAa,kBAAkB;oBACnD,sBAAsB,aAAa,oBAAoB;oBACvD,kBAAkB,aAAa,gBAAgB;oBAC/C,uBAAuB,aAAa,qBAAqB;oBACzD,qBAAqB,aAAa,mBAAmB;gBACzD;YACA,OAAO,IAAM,IAAI;oBAAE,GAAG,YAAY;gBAAC;QACvC,CAAC,GAAG,OAAO,EAAE;AAEb,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACxB,IAAI,CAAC,SAAS,OAAO,EAAE;QACnB,SAAS,OAAO,GAAG;IACvB;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,YAAY;QAAE,OAAO,SAAS,OAAO;IAAC,GAAG;AACxE;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE;IACzB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,IAAI,WAAW;QACX,6FAA6F;QAC7F,kEAAkE;QAClE,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM;IACrD;IACA,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mBAAmB,MAAM;AACxD;AACA,QAAQ,WAAW,GAAG;AAEtB,MAAM,mBAAmB;IACrB,OAAO;IACP,SAAS;IACT,QAAQ;IACR,OAAO;AACX;AACA,MAAM,mBAAmB;IACrB,SAAS;IACT,UAAU;IACV,MAAM;IACN,YAAY;IACZ,cAAc;AAClB;AACA,MAAM,iBAAiB;IAAC;IAAG;CAAE;AAC7B,MAAM,eAAe;IAAC;IAAI;CAAG;AAC7B,MAAM,sBAAsB;IAAE,GAAG;IAAG,GAAG;IAAG,MAAM;AAAE;AAClD,MAAM,eAAe;IACjB,OAAO;IACP,QAAQ;IACR,UAAU;IACV,UAAU;IACV,QAAQ;AACZ;AACA,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,gBAAgB,EAAE,YAAY,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,eAAe,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,eAAe,MAAM,EAAE,qBAAqB,mBAAmB,MAAM,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,4BAA4B,EAAE,gBAAgB,WAAW,EAAE,mBAAmB,OAAO,EAAE,kBAAkB,KAAK,EAAE,gBAAgB,cAAc,IAAI,EAAE,uBAAuB,OAAO,EAAE,wBAAwB,YAAY,SAAS,SAAS,EAAE,wBAAwB,YAAY,SAAS,SAAS,EAAE,aAAa,KAAK,EAAE,WAAW,YAAY,EAAE,4BAA4B,KAAK,EAAE,oBAAoB,IAAI,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,mBAAmB,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,EAAE,kBAAkB,cAAc,EAAE,mBAAmB,IAAI,EAAE,UAAU,EAAE,qBAAqB,SAAS,EAAE,eAAe,IAAI,EAAE,cAAc,IAAI,EAAE,cAAc,KAAK,EAAE,mBAAmB,GAAG,EAAE,kBAAkB,gBAAgB,IAAI,EAAE,oBAAoB,IAAI,EAAE,YAAY,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,QAAQ,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,eAAe,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,EAAE,oBAAoB,EAAE,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,QAAQ,EAAE,mBAAmB,SAAS,EAAE,iBAAiB,OAAO,EAAE,UAAU,KAAK,EAAE,cAAc,EAAE,iBAAiB,IAAI,EAAE,mBAAmB,EAAE,UAAU,EAAE,kBAAkB,EAAE,uBAAuB,IAAI,EAAE,uBAAuB,KAAK,EAAE,sBAAsB,KAAK,EAAE,mBAAmB,IAAI,EAAE,oBAAoB,IAAI,EAAE,mBAAmB,EAAE,EAAE,iBAAiB,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,iBAAiB,EAAE,GAAG,MAAM,EAAE;IACv1E,MAAM,OAAO,MAAM;IACnB,OAAQ,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAAE,GAAG,IAAI;QAAE,OAAO;YAAE,GAAG,KAAK;YAAE,GAAG,YAAY;QAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,iIAAA,CAAA,UAAE,AAAD,EAAE;YAAC;YAAc;SAAU;QAAG,eAAe;QAAe,IAAI;IAAG,GACzK,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,MACzB,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,aAAa;QAAE,QAAQ;QAAQ,QAAQ;QAAQ,aAAa;QAAa,WAAW;QAAW,aAAa;QAAa,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,mBAAmB;QAAmB,mBAAmB;QAAmB,WAAW;QAAW,WAAW;QAAW,oBAAoB;QAAoB,qBAAqB;QAAqB,yBAAyB;QAAyB,8BAA8B;QAA8B,kBAAkB;QAAkB,iBAAiB;QAAiB,eAAe;QAAe,eAAe;QAAe,uBAAuB;QAAuB,sBAAsB;QAAsB,uBAAuB;QAAuB,2BAA2B;QAA2B,mBAAmB;QAAmB,iBAAiB;QAAiB,iBAAiB;QAAiB,SAAS;QAAS,SAAS;QAAS,kBAAkB;QAAkB,cAAc;QAAc,aAAa;QAAa,mBAAmB;QAAmB,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,WAAW;QAAW,aAAa;QAAa,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,cAAc;QAAc,mBAAmB;QAAmB,wBAAwB;QAAwB,kBAAkB;QAAkB,gBAAgB;QAAgB,mBAAmB;QAAmB,mBAAmB;QAAmB,kBAAkB;QAAkB,iBAAiB;QAAiB,kBAAkB;QAAkB,aAAa,eAAe;QAAc,kBAAkB,oBAAoB;QAAmB,gBAAgB,kBAAkB;QAAiB,iBAAiB,mBAAmB;QAAmB,oBAAoB;QAAoB,iBAAiB;QAAiB,kBAAkB;QAAkB,gBAAgB;QAAgB,sBAAsB;QAAsB,MAAM;QAAM,qBAAqB;QAAqB,YAAY;QAAY,YAAY;IAAW,IACzuE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,cAAc;QAAE,OAAO;QAAO,OAAO;QAAO,cAAc;QAAc,cAAc;QAAc,WAAW;QAAW,gBAAgB;QAAgB,cAAc;QAAc,qBAAqB;QAAqB,mBAAmB;QAAmB,gBAAgB;QAAgB,kBAAkB;QAAkB,gBAAgB;QAAgB,gBAAgB;QAAgB,gBAAgB;QAAgB,oBAAoB;QAAoB,sBAAsB;QAAsB,SAAS;QAAS,SAAS;QAAS,YAAY;QAAY,eAAe;QAAe,eAAe;QAAe,YAAY;QAAY,UAAU;QAAU,gBAAgB;QAAgB,iBAAiB;QAAiB,gBAAgB;QAAgB,oBAAoB;QAAoB,SAAS;QAAS,gBAAgB;QAAgB,eAAe;QAAe,eAAe;QAAe,iBAAiB;QAAiB,YAAY;QAAY,gBAAgB;QAAgB,iBAAiB;QAAiB,sBAAsB;QAAsB,qBAAqB;QAAqB,gBAAgB;QAAgB,YAAY;QAAY,MAAM;QAAM,kBAAkB;QAAkB,mBAAmB;QAAmB,SAAS;QAAS,kBAAkB;QAAkB,mBAAmB;QAAmB,mBAAmB;IAAkB,IAC/4C,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;QAAE,mBAAmB;IAAkB,IACtE,UACA,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,aAAa;QAAE,YAAY;QAAY,UAAU;IAAoB,IACzF,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,kBAAkB;QAAE,MAAM;QAAM,qBAAqB;IAAoB;AACzG;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,aAAa,CAAC,IAAM,EAAE,OAAO,EAAE,cAAc;AACnD,SAAS,kBAAkB,EAAE,QAAQ,EAAE;IACnC,MAAM,oBAAoB,SAAS;IACnC,IAAI,CAAC,mBAAmB;QACpB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AAClC;AAEA,SAAS;IACL,MAAM,QAAQ;IACd,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChB,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,GAAG,MAAM,QAAQ;QACxD,MAAM,YAAY,MAAM,OAAO,CAAC,MAAM,KAAK;YAAC;SAAG;QAC/C,MAAM,UAAU,UAAU,MAAM,CAAC,CAAC,KAAK;YACnC,MAAM,cAAc,SAAS,cAAc,CAAC,2BAA2B,EAAE,SAAS,EAAE,CAAC;YACrF,IAAI,aAAa;gBACb,IAAI,IAAI,CAAC;oBAAE,IAAI;oBAAU;oBAAa,aAAa;gBAAK;YAC5D;YACA,OAAO;QACX,GAAG,EAAE;QACL,sBAAsB,IAAM,qBAAqB;IACrD,GAAG,EAAE;AACT;AAEA,MAAM,gBAAgB,CAAC,QAAU,MAAM,QAAQ;AAC/C,SAAS;IACL,MAAM,QAAQ,SAAS,eAAe,iLAAA,CAAA,UAAO;IAC7C,OAAO;AACX;AAEA,MAAM,gBAAgB,CAAC,QAAU,MAAM,KAAK;AAC5C,SAAS;IACL,MAAM,QAAQ,SAAS,eAAe,iLAAA,CAAA,UAAO;IAC7C,OAAO;AACX;AAEA,MAAM,mBAAmB,CAAC,QAAU,CAAC;QACjC,GAAG,MAAM,SAAS,CAAC,EAAE;QACrB,GAAG,MAAM,SAAS,CAAC,EAAE;QACrB,MAAM,MAAM,SAAS,CAAC,EAAE;IAC5B,CAAC;AACD,SAAS;IACL,MAAM,WAAW,SAAS,kBAAkB,iLAAA,CAAA,UAAO;IACnD,OAAO;AACX;AAEA,qDAAqD,GACrD,SAAS,oBAAoB,YAAY;IACrC,OAAO,CAAC;QACJ,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAY,SAAS,CAAC,QAAU,aAAa,SAAS,SAAS,EAAE;QACpG,OAAO;YAAC;YAAO;YAAU;SAAc;IAC3C;AACJ;AACA,MAAM,gBAAgB,oBAAoB;AAC1C,MAAM,gBAAgB,oBAAoB;AAE1C,SAAS,oBAAoB,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;IACrD,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,uBAAuB;QAAQ;IACpD,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,kBAAkB;QAAS;IAChD,GAAG;QAAC;KAAS;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,QAAQ,CAAC;YAAE,qBAAqB;QAAM;IAChD,GAAG;QAAC;KAAM;AACd;AAEA,SAAS,qBAAqB,EAAE,QAAQ,EAAE;IACtC,MAAM,QAAQ;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,8BAA8B;eAAI,MAAM,QAAQ,GAAG,iBAAiB;YAAE;SAAS;QACrF,MAAM,QAAQ,CAAC;YAAE,mBAAmB;QAA4B;QAChE,OAAO;YACH,MAAM,eAAe,MAAM,QAAQ,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;YAC9E,MAAM,QAAQ,CAAC;gBAAE,mBAAmB;YAAa;QACrD;IACJ,GAAG;QAAC;KAAS;AACjB;AAEA,MAAM,WAAW,CAAC,UAAY,CAAC;QAC3B,IAAI,EAAE,aAAa,CAAC,IAAI,KAAK,GAAG;YAC5B,OAAO;QACX;QACA,OAAO,EACF,QAAQ,GACR,MAAM,CAAC,CAAC,IAAO,QAAQ,kBAAkB,GAAG,OAAO,CAAC,EAAE,MAAM,EAC5D,KAAK,CAAC,CAAC,IAAM,CAAC,CAAC,gBAAgB,EAAE,iBAAiB;IAC3D;AACA,MAAM,iBAAiB;IACnB,oBAAoB;AACxB;AACA,SAAS,oBAAoB,UAAU,cAAc;IACjD,MAAM,cAAc,SAAS,SAAS;IACtC,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}