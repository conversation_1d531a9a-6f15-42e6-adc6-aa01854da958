"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7764],{4982:(e,t,r)=>{r.d(t,{ZQ:()=>w,Ek:()=>x,yq:()=>m});var n=r(23464),a=r(79971);let o="access_token",i="refresh_token",s=async()=>{let e=(0,a.getCookie)(o);return e?String(e):null},c=async()=>{(0,a.deleteCookie)(o,{path:"/"}),(0,a.deleteCookie)(i,{path:"/"}),(0,a.deleteCookie)(i,{path:"/api/auth/refresh"}),console.log("Auth cookies cleared with specific paths")};var l=r(54897),d=r(40619),u=r(65453),h=r(46786);let p=(0,u.v)()((0,h.Zr)((e,t)=>({user:null,setUser:t=>e({user:{...t,isAuthenticated:!0}}),clearUser:()=>e({user:null}),isAuthenticated:()=>{var e;return!!(null===(e=t().user)||void 0===e?void 0:e.isAuthenticated)}}),{name:"user-storage"})),v=()=>{p.getState().clearUser()},g=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?(0,l.XI)():await s()},f=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return async t=>{t.headers||(t.headers={});let r=await g(e.enableClientSideToken);return r?(t.headers.Authorization="Bearer ".concat(r),console.log("[DEBUG] Added Authorization header with token (length: ".concat(r.length,")"))):console.log("[DEBUG] No token available for request to ".concat(t.url)),t.headers["ngrok-skip-browser-warning"]="true",e.customHeaders&&Object.assign(t.headers,e.customHeaders),t}},b=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return async r=>{var a,o;let i=r.config;if(!t.enableTokenRefresh)return Promise.reject(r);if(((null===(a=r.response)||void 0===a?void 0:a.status)===401||(null===(o=r.response)||void 0===o?void 0:o.status)===403)&&!i._retry){i._retry=!0;try{let t=n.A.create(),r=await t.post("/api/auth/refresh");if(r.data.success&&r.data.accessToken)return i.headers={...i.headers,Authorization:"Bearer ".concat(r.data.accessToken)},e(i);return await c(),v(),window.location.href=d.VV,Promise.reject(Error("Token refresh failed"))}catch(e){return await c(),v(),window.location.href=d.VV,Promise.reject(e)}}return Promise.reject(r)}},k=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=n.A.create({baseURL:t.baseURL||"https://app-dev.rapidinnovation.dev/api/v1",withCredentials:null!==(e=t.withCredentials)&&void 0!==e&&e});return r.interceptors.request.use(f(t),e=>Promise.reject(Error("Request interceptor error: ".concat(e.message||"Unknown error")))),r.interceptors.response.use(e=>e,b(r,t)),r};k({enableTokenRefresh:!0,enableClientSideToken:!0});let m=k({enableTokenRefresh:!1,enableClientSideToken:!0}),w=k({enableTokenRefresh:!0,enableClientSideToken:!0,withCredentials:!0}),x=n.A.create()},30285:(e,t,r)=>{r.d(t,{$:()=>c,r:()=>s});var n=r(95155);r(12115);var a=r(99708),o=r(74466),i=r(59434);let s=(0,o.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:o,asChild:c=!1,...l}=e,d=c?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,i.cn)(s({variant:r,size:o,className:t})),...l})}},40619:(e,t,r)=>{r.d(t,{C$:()=>o,VV:()=>n,_K:()=>a,qv:()=>i});let n="/login",a="".concat("http://localhost:3001/","?redirect_url=").concat("http://localhost:3000/"),o="/workflows",i=[n,"/signup","/verify-email","/reset-password","/about","/contact"]},54361:(e,t,r)=>{r.d(t,{k:()=>o});var n=r(65453),a=r(46786);let o=(0,n.v)()((0,a.Zr)((e,t)=>({user:null,isAuthenticated:!1,setUser:t=>e(e=>({user:e.user?{...e.user,...t}:t,isAuthenticated:null!=t&&!!t.accessToken})),clearUser:()=>e({user:null,isAuthenticated:!1}),logout:async()=>{try{let{authApi:e}=await Promise.all([r.e(7997),r.e(4477)]).then(r.bind(r,27997));await e.logout()}catch(e){console.error("Error during logout:",e),t().clearUser()}}}),{name:"user-storage"}))},54897:(e,t,r)=>{r.d(t,{BW:()=>i,VT:()=>o,XI:()=>a,gW:()=>s});var n=r(57383);let a=()=>n.A.get("accessToken")||"",o=()=>{let e=n.A.get("accessToken");return console.log("Client-side access token check:",!!e),!!e},i=(e,t,r,a)=>{n.A.set("accessToken",e,{path:"/",domain:"localhost",secure:!0,sameSite:"lax",expires:r/86400})},s=()=>{n.A.remove("accessToken",{path:"/",domain:"localhost"}),n.A.remove("refreshToken",{path:"/",domain:"localhost"}),n.A.remove("refreshToken",{path:"/api/auth/refresh",domain:"localhost"}),console.log("Client-side cookie clearing attempted for both tokens")}},59434:(e,t,r)=>{r.d(t,{cn:()=>o,s:()=>i});var n=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}function i(e,t){let r=null;return function(){for(var n=arguments.length,a=Array(n),o=0;o<n;o++)a[o]=arguments[o];null!==r&&clearTimeout(r),r=setTimeout(()=>{r=null,e(...a)},t)}}},62523:(e,t,r)=>{r.d(t,{p:()=>o});var n=r(95155);r(12115);var a=r(59434);function o(e){let{className:t,type:r,...o}=e;return(0,n.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},85057:(e,t,r)=>{r.d(t,{J:()=>i});var n=r(95155);r(12115);var a=r(40968),o=r(59434);function i(e){let{className:t,...r}=e;return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},89761:(e,t,r)=>{r.d(t,{JR:()=>n,Sn:()=>c});let n="https://app-dev.rapidinnovation.dev/api/v1",a="".concat(n,"/auth"),o="".concat(n,"/workflows"),i="".concat(n,"/mcps"),s="https://ruh-test-api.rapidinnovation.dev/api/v1",c={AUTH:{LOGIN:"".concat(a,"/login"),REGISTER:"".concat(a,"/register"),LOGOUT:"".concat(a,"/logout"),REFRESH:"".concat(a,"/refresh"),FORGOT_PASSWORD:"".concat(a,"/forgot-password"),RESET_PASSWORD:"".concat(a,"/reset-password"),VERIFY_EMAIL:"".concat(a,"/verify-email"),VERIFY_EMAIL_OTP:"".concat(a,"/verify-email-otp"),UPDATE_PASSWORD:"".concat(a,"/update-password")},WORKFLOWS:{LIST:"".concat(o),CREATE:"".concat(o),GET:e=>"".concat(o,"/").concat(e),UPDATE:e=>"".concat(o,"/").concat(e),DELETE:e=>"".concat(o,"/").concat(e),EXECUTE:e=>"".concat(o,"/").concat(e,"/execute")},MCPS:{LIST:"".concat(i),GET:e=>"".concat(i,"/").concat(e)},WORKFLOW_EXECUTION:{EXECUTE:"".concat(s,"/workflow-execute/execute"),APPROVE:"".concat(s,"/workflow-execute/approve"),STREAM:"".concat(s,"/workflow-execute/stream")}}}}]);