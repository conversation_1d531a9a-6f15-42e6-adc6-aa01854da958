(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{4982:(e,t,r)=>{"use strict";r.d(t,{ZQ:()=>w,Ek:()=>A,yq:()=>T});var o=r(23464),s=r(79971);let n="access_token",a="refresh_token",c=async()=>{let e=(0,s.getCookie)(n);return e?String(e):null},i=async()=>{(0,s.deleteCookie)(n,{path:"/"}),(0,s.deleteCookie)(a,{path:"/"}),(0,s.deleteCookie)(a,{path:"/api/auth/refresh"}),console.log("Auth cookies cleared with specific paths")};var l=r(54897),u=r(40619),d=r(65453),h=r(46786);let v=(0,d.v)()((0,h.Zr)((e,t)=>({user:null,setUser:t=>e({user:{...t,isAuthenticated:!0}}),clearUser:()=>e({user:null}),isAuthenticated:()=>{var e;return!!(null===(e=t().user)||void 0===e?void 0:e.isAuthenticated)}}),{name:"user-storage"})),p=()=>{v.getState().clearUser()},k=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?(0,l.XI)():await c()},f=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return async t=>{t.headers||(t.headers={});let r=await k(e.enableClientSideToken);return r?(t.headers.Authorization="Bearer ".concat(r),console.log("[DEBUG] Added Authorization header with token (length: ".concat(r.length,")"))):console.log("[DEBUG] No token available for request to ".concat(t.url)),t.headers["ngrok-skip-browser-warning"]="true",e.customHeaders&&Object.assign(t.headers,e.customHeaders),t}},g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return async r=>{var s,n;let a=r.config;if(!t.enableTokenRefresh)return Promise.reject(r);if(((null===(s=r.response)||void 0===s?void 0:s.status)===401||(null===(n=r.response)||void 0===n?void 0:n.status)===403)&&!a._retry){a._retry=!0;try{let t=o.A.create(),r=await t.post("/api/auth/refresh");if(r.data.success&&r.data.accessToken)return a.headers={...a.headers,Authorization:"Bearer ".concat(r.data.accessToken)},e(a);return await i(),p(),window.location.href=u.VV,Promise.reject(Error("Token refresh failed"))}catch(e){return await i(),p(),window.location.href=u.VV,Promise.reject(e)}}return Promise.reject(r)}},E=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=o.A.create({baseURL:t.baseURL||"https://app-dev.rapidinnovation.dev/api/v1",withCredentials:null!==(e=t.withCredentials)&&void 0!==e&&e});return r.interceptors.request.use(f(t),e=>Promise.reject(Error("Request interceptor error: ".concat(e.message||"Unknown error")))),r.interceptors.response.use(e=>e,g(r,t)),r};E({enableTokenRefresh:!0,enableClientSideToken:!0});let T=E({enableTokenRefresh:!1,enableClientSideToken:!0}),w=E({enableTokenRefresh:!0,enableClientSideToken:!0,withCredentials:!0}),A=o.A.create()},30347:()=>{},40619:(e,t,r)=>{"use strict";r.d(t,{C$:()=>n,VV:()=>o,_K:()=>s,qv:()=>a});let o="/login",s="".concat("http://localhost:3001/","?redirect_url=").concat("http://localhost:3000/"),n="/workflows",a=[o,"/signup","/verify-email","/reset-password","/about","/contact"]},42618:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>a});var o=r(95155),s=r(56671),n=r(51362);function a(){let{theme:e}=(0,n.D)();return(0,o.jsx)(s.Toaster,{position:"top-right",toastOptions:{style:{background:"dark"===e?"#222222":"#F9F7F7",color:"dark"===e?"#f0f0f0":"#112D4E",border:"dark"===e?"1px solid rgba(29, 205, 159, 0.1)":"1px solid rgba(63, 114, 175, 0.1)"}}})}},52807:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,62093,23)),Promise.resolve().then(r.t.bind(r,50116,23)),Promise.resolve().then(r.t.bind(r,13096,23)),Promise.resolve().then(r.t.bind(r,91223,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,94819)),Promise.resolve().then(r.bind(r,77890)),Promise.resolve().then(r.bind(r,70139)),Promise.resolve().then(r.bind(r,42618))},54361:(e,t,r)=>{"use strict";r.d(t,{k:()=>n});var o=r(65453),s=r(46786);let n=(0,o.v)()((0,s.Zr)((e,t)=>({user:null,isAuthenticated:!1,setUser:t=>e(e=>({user:e.user?{...e.user,...t}:t,isAuthenticated:null!=t&&!!t.accessToken})),clearUser:()=>e({user:null,isAuthenticated:!1}),logout:async()=>{try{let{authApi:e}=await Promise.all([r.e(7997),r.e(4477)]).then(r.bind(r,27997));await e.logout()}catch(e){console.error("Error during logout:",e),t().clearUser()}}}),{name:"user-storage"}))},54897:(e,t,r)=>{"use strict";r.d(t,{BW:()=>a,VT:()=>n,XI:()=>s,gW:()=>c});var o=r(57383);let s=()=>o.A.get("accessToken")||"",n=()=>{let e=o.A.get("accessToken");return console.log("Client-side access token check:",!!e),!!e},a=(e,t,r,s)=>{o.A.set("accessToken",e,{path:"/",domain:"localhost",secure:!0,sameSite:"lax",expires:r/86400})},c=()=>{o.A.remove("accessToken",{path:"/",domain:"localhost"}),o.A.remove("refreshToken",{path:"/",domain:"localhost"}),o.A.remove("refreshToken",{path:"/api/auth/refresh",domain:"localhost"}),console.log("Client-side cookie clearing attempted for both tokens")}},70139:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>c});var o=r(95155),s=r(21716),n=r(26715),a=r(12115);function c(e){let{children:t}=e,[r]=(0,a.useState)(()=>new s.E);return(0,o.jsx)(n.Ht,{client:r,children:t})}},77890:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(95155);r(12115);var s=r(51362);function n(e){let{children:t,...r}=e;return(0,o.jsx)(s.N,{...r,children:t})}},89761:(e,t,r)=>{"use strict";r.d(t,{JR:()=>o,Sn:()=>i});let o="https://app-dev.rapidinnovation.dev/api/v1",s="".concat(o,"/auth"),n="".concat(o,"/workflows"),a="".concat(o,"/mcps"),c="https://ruh-test-api.rapidinnovation.dev/api/v1",i={AUTH:{LOGIN:"".concat(s,"/login"),REGISTER:"".concat(s,"/register"),LOGOUT:"".concat(s,"/logout"),REFRESH:"".concat(s,"/refresh"),FORGOT_PASSWORD:"".concat(s,"/forgot-password"),RESET_PASSWORD:"".concat(s,"/reset-password"),VERIFY_EMAIL:"".concat(s,"/verify-email"),VERIFY_EMAIL_OTP:"".concat(s,"/verify-email-otp"),UPDATE_PASSWORD:"".concat(s,"/update-password")},WORKFLOWS:{LIST:"".concat(n),CREATE:"".concat(n),GET:e=>"".concat(n,"/").concat(e),UPDATE:e=>"".concat(n,"/").concat(e),DELETE:e=>"".concat(n,"/").concat(e),EXECUTE:e=>"".concat(n,"/").concat(e,"/execute")},MCPS:{LIST:"".concat(a),GET:e=>"".concat(a,"/").concat(e)},WORKFLOW_EXECUTION:{EXECUTE:"".concat(c,"/workflow-execute/execute"),APPROVE:"".concat(c,"/workflow-execute/approve"),STREAM:"".concat(c,"/workflow-execute/stream")}}},94819:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>l});var o=r(95155),s=r(12115),n=r(54361),a=r(35695),c=r(27997),i=r(40619);function l(e){let{children:t}=e,r=(0,n.k)(e=>e.user),l=(0,n.k)(e=>e.setUser),u=(0,n.k)(e=>e.clearUser),d=(0,a.useRouter)(),h=(0,a.usePathname)(),v=i.qv.some(e=>h===e||e.startsWith("/")&&h.startsWith(e)),p=!!(null==r?void 0:r.accessToken);return(0,s.useEffect)(()=>{(async()=>{try{let o=await c.authApi.isAuthenticated();if(console.log("Authentication check result:",o),o)try{let e=await c.authApi.getCurrentUser();console.log("User data retrieved successfully:",e);let t=await c.authApi.getAccessToken();l({...e,accessToken:t})}catch(o){var e,t,r;(null===(e=o.response)||void 0===e?void 0:e.status)===403||(null===(t=o.response)||void 0===t?void 0:t.status)===401?(console.log("Authentication error:",null===(r=o.response)||void 0===r?void 0:r.status),u(),v||d.push("/login")):console.error("Error fetching user data:",o)}else console.log("No authentication token found"),u()}catch(e){u(),console.error("Failed to load user:",e)}})()},[l,u,d,v]),(0,s.useEffect)(()=>{v||p||d.push("/login"),v&&p&&d.push("/")},[h,p,v,d]),(0,o.jsx)(o.Fragment,{children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9565,3291,6671,4766,4720,7997,8441,1684,7358],()=>t(52807)),_N_E=e.O()}]);