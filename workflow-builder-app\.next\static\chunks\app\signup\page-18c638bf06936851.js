(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{6165:(e,s,a)=>{Promise.resolve().then(a.bind(a,46818))},17759:(e,s,a)=>{"use strict";a.d(s,{C5:()=>j,MJ:()=>f,eI:()=>h,lR:()=>g,lV:()=>o,zB:()=>m});var t=a(95155),r=a(12115),n=a(99708),l=a(62177),i=a(59434),c=a(85057);let o=l.Op,d=r.createContext({}),m=e=>{let{...s}=e;return(0,t.jsx)(d.Provider,{value:{name:s.name},children:(0,t.jsx)(l.xI,{...s})})},u=()=>{let e=r.useContext(d),s=r.useContext(x),{getFieldState:a}=(0,l.xW)(),t=(0,l.lN)({name:e.name}),n=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=s;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},x=r.createContext({});function h(e){let{className:s,...a}=e,n=r.useId();return(0,t.jsx)(x.Provider,{value:{id:n},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",s),...a})})}function g(e){let{className:s,...a}=e,{error:r,formItemId:n}=u();return(0,t.jsx)(c.J,{"data-slot":"form-label","data-error":!!r,className:(0,i.cn)("data-[error=true]:text-destructive",s),htmlFor:n,...a})}function f(e){let{...s}=e,{error:a,formItemId:r,formDescriptionId:l,formMessageId:i}=u();return(0,t.jsx)(n.DX,{"data-slot":"form-control",id:r,"aria-describedby":a?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!a,...s})}function j(e){var s;let{className:a,...r}=e,{error:n,formMessageId:l}=u(),c=n?String(null!==(s=null==n?void 0:n.message)&&void 0!==s?s:""):r.children;return c?(0,t.jsx)("p",{"data-slot":"form-message",id:l,className:(0,i.cn)("text-destructive text-sm",a),...r,children:c}):null}},46818:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C});var t=a(95155),r=a(12115),n=a(90221),l=a(62177),i=a(78749),c=a(92657),o=a(30285),d=a(39952),m=a(5196),u=a(59434);function x(e){let{className:s,...a}=e;return(0,t.jsx)(d.bL,{"data-slot":"checkbox",className:(0,u.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...a,children:(0,t.jsx)(d.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(m.A,{className:"size-3.5"})})})}var h=a(62523),g=a(17759),f=a(48282),j=a(56671),p=a(27997),b=a(40646),w=a(85339);function v(e){let{password:s,showValidation:a=!1}=e,[n,l]=(0,r.useState)({length:!1,hasNumber:!1,hasSymbol:!1});return(0,r.useEffect)(()=>{l((0,f.Oj)(s))},[s]),(0,t.jsxs)("div",{className:"flex flex-col gap-4 py-4 text-sm",children:[(0,t.jsx)("h3",{className:"mb-3 font-medium",children:"Your password must have:"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[n.length?(0,t.jsx)(b.A,{className:"h-5 w-5 text-green-500"}):a?(0,t.jsx)(w.A,{className:"h-5 w-5 text-red-500"}):null,(0,t.jsx)("span",{className:"text-muted-foreground",children:"6-15 characters"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[n.hasNumber?(0,t.jsx)(b.A,{className:"h-5 w-5 text-green-500"}):a?(0,t.jsx)(w.A,{className:"h-5 w-5 text-red-500"}):null,(0,t.jsx)("span",{className:"text-muted-foreground",children:"At least one number (0-9)"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[n.hasSymbol?(0,t.jsx)(b.A,{className:"h-5 w-5 text-green-500"}):a?(0,t.jsx)(w.A,{className:"h-5 w-5 text-red-500"}):null,(0,t.jsx)("span",{className:"text-muted-foreground",children:"At least one symbol (@, #, $, %, etc.)"})]})]})]})}var N=a(12874);function y(e){let{title:s,message:a,email:r,onBackToLogin:n}=e;return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-6 text-center",children:[(0,t.jsx)("div",{className:"bg-primary/10 rounded-full p-3",children:(0,t.jsx)(N.A,{className:"text-primary h-6 w-6"})}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold",children:s}),(0,t.jsxs)("p",{className:"text-muted-foreground",children:[a,(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-foreground font-medium",children:r})]})]}),(0,t.jsx)(o.$,{variant:"link",onClick:n,className:"text-primary hover:text-primary/80 text-sm",children:"Back to Login"})]})}function P(){let[e,s]=(0,r.useState)(!1),[a,d]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),[b,w]=(0,r.useState)({length:!1,hasNumber:!1,hasSymbol:!1}),[N,P]=(0,r.useState)(!1),[z,k]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),S=(0,l.mN)({resolver:(0,n.u)(f.O5),defaultValues:{fullName:"",email:"",password:"",termsAccepted:!1}}),E=e=>{w((0,f.Oj)(e))};async function I(e){k(!0),d(!0);try{let s=await p.authApi.signup(e);s.success?C(!0):j.toast.error(s.message||"Signup failed. Please try again.")}catch(e){j.toast.error(e.message||"An unexpected error occurred."),console.error("Signup Error:",e)}finally{d(!1)}}return((0,r.useEffect)(()=>{let e=S.watch(e=>{E(e.password||"");let{fullName:s,email:a,password:t,termsAccepted:r}=e;P(!!(s&&a&&t&&!0===r))});return()=>e.unsubscribe()},[S]),A)?(0,t.jsx)(y,{title:"Verify Your Email",message:"We have sent a verification link to your email. Please verify your email to continue.",email:S.getValues("email"),onBackToLogin:()=>{C(!1),S.reset(),S.clearErrors(),P(!1),k(!1)}}):(0,t.jsx)(g.lV,{...S,children:(0,t.jsxs)("form",{onSubmit:S.handleSubmit(I,()=>k(!0)),className:"space-y-6",children:[(0,t.jsx)(g.zB,{control:S.control,name:"fullName",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Full Name"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(h.p,{placeholder:"Enter your full name",...s,disabled:a})}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:S.control,name:"email",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Email Address"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(h.p,{placeholder:"Enter your email address",type:"email",...s,disabled:a})}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:S.control,name:"password",render:r=>{let{field:n}=r;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Password"}),(0,t.jsx)(g.MJ,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(h.p,{placeholder:"Enter a unique password",type:e?"text":"password",...n,disabled:a,onFocus:()=>u(!0),onBlur:()=>u(!1),onChange:e=>{n.onChange(e),E(e.target.value)}}),(0,t.jsx)(o.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>s(!e),disabled:a,"aria-label":e?"Hide password":"Show password",children:e?(0,t.jsx)(i.A,{className:"h-4 w-4 text-gray-500"}):(0,t.jsx)(c.A,{className:"h-4 w-4 text-gray-500"})})]})}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:S.control,name:"termsAccepted",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{className:"flex flex-row items-center",children:[(0,t.jsx)(g.MJ,{children:(0,t.jsx)(x,{checked:s.value,onCheckedChange:s.onChange,disabled:a})}),(0,t.jsxs)("div",{className:"",children:[(0,t.jsxs)(g.lR,{className:"text-sm font-semibold",children:["I agree to the"," ",(0,t.jsx)("a",{href:"#",className:"text-primary hover:underline hover:opacity-80",children:"Terms"})," ","&"," ",(0,t.jsx)("a",{href:"#",className:"text-primary hover:underline hover:opacity-80",children:"Privacy Policy"})]}),(0,t.jsx)(g.C5,{})]})]})}}),(0,t.jsx)(v,{password:S.getValues("password")||"",showValidation:z}),(0,t.jsx)(o.$,{type:"submit",className:"w-full",disabled:!N||a,children:a?"Signing up...":"Signup"})]})})}var z=a(6874),k=a.n(z),A=a(40081);function C(){return(0,t.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,t.jsx)(A.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Workflow Builder"}),(0,t.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Create an account to get started"})]}),(0,t.jsx)(P,{}),(0,t.jsx)("div",{className:"text-center text-sm",children:(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Already have an account?"," ",(0,t.jsx)(k(),{href:"/login",className:"text-primary font-medium hover:underline",children:"Log in"})]})})]})})}},48282:(e,s,a)=>{"use strict";a.d(s,{O5:()=>r,Oh:()=>l,Oj:()=>i,jc:()=>n});var t=a(55594);t.z.object({email:t.z.string().email({message:"Please enter a valid email address."}),password:t.z.string().min(1,{message:"Password is required."})}),t.z.object({email:t.z.string().email({message:"Please enter a valid email address."})});let r=t.z.object({fullName:t.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:t.z.string().email({message:"Please enter a valid email address."}),password:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:t.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),n=t.z.object({email:t.z.string().email({message:"Please enter a valid email address."})});t.z.object({newPassword:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:t.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]});let l=t.z.object({password:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:t.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});function i(e){return{length:e.length>=6&&e.length<=15,hasNumber:/[0-9]/.test(e),hasSymbol:/[!@#$%^&*(),.?":{}|<>]/.test(e)}}}},e=>{var s=s=>e(e.s=s);e.O(0,[9352,3291,6671,6874,8517,2313,8343,7764,7997,8441,1684,7358],()=>s(6165)),_N_E=e.O()}]);