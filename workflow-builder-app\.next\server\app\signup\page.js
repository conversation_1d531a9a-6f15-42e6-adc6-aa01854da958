(()=>{var e={};e.id=879,e.ids=[879],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a,s:()=>i});var s=r(49384),n=r(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}function i(e,t){let r=null;return function(...s){null!==r&&clearTimeout(r),r=setTimeout(()=>{r=null,e(...s)},t)}}},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6569:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38255)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\signup\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11273:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,q:()=>a});var s=r(43210),n=r(60687);function a(e,t){let r=s.createContext(t),a=e=>{let{children:t,...a}=e,i=s.useMemo(()=>a,Object.values(a));return(0,n.jsx)(r.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(n){let a=s.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],a=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let i=s.createContext(a),o=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,d=r?.[e]?.[o]||i,c=s.useMemo(()=>l,Object.values(l));return(0,n.jsx)(d.Provider,{value:c,children:a})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[o]||i,d=s.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:s})=>{let n=r(e)[`__scope${s}`];return{...t,...n}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},18853:(e,t,r)=>{"use strict";r.d(t,{X:()=>a});var s=r(43210),n=r(66156);function a(e){let[t,r]=s.useState(void 0);return(0,n.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let s,n;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;s=t.inlineSize,n=t.blockSize}else s=e.offsetWidth,n=e.offsetHeight;r({width:s,height:n})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19328:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("mail-check",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]])},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>o});var s=r(60687);r(43210);var n=r(8730),a=r(24224),i=r(4780);let o=(0,a.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:a=!1,...l}){let d=a?n.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},35943:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Y});var s=r(60687),n=r(43210),a=r.t(n,2),i=r(63442),o=r(27605),l=r(12597),d=r(13861),c=r(29523),u=r(98599),m=r(11273),p=r(70569),f=r(66156),x=a[" useInsertionEffect ".trim().toString()]||f.N,h=(Symbol("RADIX:SYNC_STATE"),r(83721)),g=r(18853),v=e=>{let{present:t,children:r}=e,s=function(e){var t,r;let[s,a]=n.useState(),i=n.useRef(null),o=n.useRef(e),l=n.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=b(i.current);l.current="mounted"===d?e:"none"},[d]),(0,f.N)(()=>{let t=i.current,r=o.current;if(r!==e){let s=l.current,n=b(t);e?c("MOUNT"):"none"===n||t?.display==="none"?c("UNMOUNT"):r&&s!==n?c("ANIMATION_OUT"):c("UNMOUNT"),o.current=e}},[e,c]),(0,f.N)(()=>{if(s){let e,t=s.ownerDocument.defaultView??window,r=r=>{let n=b(i.current).includes(r.animationName);if(r.target===s&&n&&(c("ANIMATION_END"),!o.current)){let r=s.style.animationFillMode;s.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=r)})}},n=e=>{e.target===s&&(l.current=b(i.current))};return s.addEventListener("animationstart",n),s.addEventListener("animationcancel",r),s.addEventListener("animationend",r),()=>{t.clearTimeout(e),s.removeEventListener("animationstart",n),s.removeEventListener("animationcancel",r),s.removeEventListener("animationend",r)}}c("ANIMATION_END")},[s,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{i.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof r?r({present:s.isPresent}):n.Children.only(r),i=(0,u.s)(s.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||s.isPresent?n.cloneElement(a,{ref:i}):null};function b(e){return e?.animationName||"none"}v.displayName="Presence",r(51215);var y=r(8730),w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,y.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a?r:t,{...i,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),j="Checkbox",[N,k]=(0,m.A)(j),[P,A]=N(j),C=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:i,defaultChecked:o,required:l,disabled:d,value:c="on",onCheckedChange:m,form:f,...h}=e,[g,v]=n.useState(null),b=(0,u.s)(t,e=>v(e)),y=n.useRef(!1),N=!g||f||!!g.closest("form"),[k,A]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:s}){let[a,i,o]=function({defaultProp:e,onChange:t}){let[r,s]=n.useState(e),a=n.useRef(r),i=n.useRef(t);return x(()=>{i.current=t},[t]),n.useEffect(()=>{a.current!==r&&(i.current?.(r),a.current=r)},[r,a]),[r,s,i]}({defaultProp:t,onChange:r}),l=void 0!==e,d=l?e:a;{let t=n.useRef(void 0!==e);n.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${s} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,s])}return[d,n.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&o.current?.(r)}else i(t)},[l,e,i,o])]}({prop:i,defaultProp:o??!1,onChange:m,caller:j}),C=n.useRef(k);return n.useEffect(()=>{let e=g?.form;if(e){let t=()=>A(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[g,A]),(0,s.jsxs)(P,{scope:r,state:k,disabled:d,children:[(0,s.jsx)(w.button,{type:"button",role:"checkbox","aria-checked":_(k)?"mixed":k,"aria-required":l,"data-state":M(k),"data-disabled":d?"":void 0,disabled:d,value:c,...h,ref:b,onKeyDown:(0,p.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,p.m)(e.onClick,e=>{A(e=>!!_(e)||!e),N&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),N&&(0,s.jsx)(S,{control:g,bubbles:!y.current,name:a,value:c,checked:k,required:l,disabled:d,form:f,style:{transform:"translateX(-100%)"},defaultChecked:!_(o)&&o})]})});C.displayName=j;var z="CheckboxIndicator",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,i=A(z,r);return(0,s.jsx)(v,{present:n||_(i.state)||!0===i.state,children:(0,s.jsx)(w.span,{"data-state":M(i.state),"data-disabled":i.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=z;var S=n.forwardRef(({__scopeCheckbox:e,control:t,checked:r,bubbles:a=!0,defaultChecked:i,...o},l)=>{let d=n.useRef(null),c=(0,u.s)(d,l),m=(0,h.Z)(r),p=(0,g.X)(t);n.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==r&&t){let s=new Event("click",{bubbles:a});e.indeterminate=_(r),t.call(e,!_(r)&&r),e.dispatchEvent(s)}},[m,r,a]);let f=n.useRef(!_(r)&&r);return(0,s.jsx)(w.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??f.current,...o,tabIndex:-1,ref:c,style:{...o.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function _(e){return"indeterminate"===e}function M(e){return _(e)?"indeterminate":e?"checked":"unchecked"}S.displayName="CheckboxBubbleInput";var R=r(13964),I=r(4780);function O({className:e,...t}){return(0,s.jsx)(C,{"data-slot":"checkbox",className:(0,I.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(E,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(R.A,{className:"size-3.5"})})})}var $=r(89667),q=r(71669),T=r(44426),D=r(52581),L=r(35399),U=r(5336),F=r(93613);function B({password:e,showValidation:t=!1}){let[r,a]=(0,n.useState)({length:!1,hasNumber:!1,hasSymbol:!1});return(0,s.jsxs)("div",{className:"flex flex-col gap-4 py-4 text-sm",children:[(0,s.jsx)("h3",{className:"mb-3 font-medium",children:"Your password must have:"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[r.length?(0,s.jsx)(U.A,{className:"h-5 w-5 text-green-500"}):t?(0,s.jsx)(F.A,{className:"h-5 w-5 text-red-500"}):null,(0,s.jsx)("span",{className:"text-muted-foreground",children:"6-15 characters"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[r.hasNumber?(0,s.jsx)(U.A,{className:"h-5 w-5 text-green-500"}):t?(0,s.jsx)(F.A,{className:"h-5 w-5 text-red-500"}):null,(0,s.jsx)("span",{className:"text-muted-foreground",children:"At least one number (0-9)"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[r.hasSymbol?(0,s.jsx)(U.A,{className:"h-5 w-5 text-green-500"}):t?(0,s.jsx)(F.A,{className:"h-5 w-5 text-red-500"}):null,(0,s.jsx)("span",{className:"text-muted-foreground",children:"At least one symbol (@, #, $, %, etc.)"})]})]})]})}var V=r(19328);function W({title:e,message:t,email:r,onBackToLogin:n}){return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-6 text-center",children:[(0,s.jsx)("div",{className:"bg-primary/10 rounded-full p-3",children:(0,s.jsx)(V.A,{className:"text-primary h-6 w-6"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold",children:e}),(0,s.jsxs)("p",{className:"text-muted-foreground",children:[t,(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"text-foreground font-medium",children:r})]})]}),(0,s.jsx)(c.$,{variant:"link",onClick:n,className:"text-primary hover:text-primary/80 text-sm",children:"Back to Login"})]})}function J(){let[e,t]=(0,n.useState)(!1),[r,a]=(0,n.useState)(!1),[u,m]=(0,n.useState)(!1),[p,f]=(0,n.useState)({length:!1,hasNumber:!1,hasSymbol:!1}),[x,h]=(0,n.useState)(!1),[g,v]=(0,n.useState)(!1),[b,y]=(0,n.useState)(!1),w=(0,o.mN)({resolver:(0,i.u)(T.O5),defaultValues:{fullName:"",email:"",password:"",termsAccepted:!1}}),j=e=>{f((0,T.Oj)(e))};async function N(e){v(!0),a(!0);try{let t=await L.authApi.signup(e);t.success?y(!0):D.toast.error(t.message||"Signup failed. Please try again.")}catch(e){D.toast.error(e.message||"An unexpected error occurred."),console.error("Signup Error:",e)}finally{a(!1)}}return b?(0,s.jsx)(W,{title:"Verify Your Email",message:"We have sent a verification link to your email. Please verify your email to continue.",email:w.getValues("email"),onBackToLogin:()=>{y(!1),w.reset(),w.clearErrors(),h(!1),v(!1)}}):(0,s.jsx)(q.lV,{...w,children:(0,s.jsxs)("form",{onSubmit:w.handleSubmit(N,()=>v(!0)),className:"space-y-6",children:[(0,s.jsx)(q.zB,{control:w.control,name:"fullName",render:({field:e})=>(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"Full Name"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)($.p,{placeholder:"Enter your full name",...e,disabled:r})}),(0,s.jsx)(q.C5,{})]})}),(0,s.jsx)(q.zB,{control:w.control,name:"email",render:({field:e})=>(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"Email Address"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)($.p,{placeholder:"Enter your email address",type:"email",...e,disabled:r})}),(0,s.jsx)(q.C5,{})]})}),(0,s.jsx)(q.zB,{control:w.control,name:"password",render:({field:n})=>(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"Password"}),(0,s.jsx)(q.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)($.p,{placeholder:"Enter a unique password",type:e?"text":"password",...n,disabled:r,onFocus:()=>m(!0),onBlur:()=>m(!1),onChange:e=>{n.onChange(e),j(e.target.value)}}),(0,s.jsx)(c.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>t(!e),disabled:r,"aria-label":e?"Hide password":"Show password",children:e?(0,s.jsx)(l.A,{className:"h-4 w-4 text-gray-500"}):(0,s.jsx)(d.A,{className:"h-4 w-4 text-gray-500"})})]})}),(0,s.jsx)(q.C5,{})]})}),(0,s.jsx)(q.zB,{control:w.control,name:"termsAccepted",render:({field:e})=>(0,s.jsxs)(q.eI,{className:"flex flex-row items-center",children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(O,{checked:e.value,onCheckedChange:e.onChange,disabled:r})}),(0,s.jsxs)("div",{className:"",children:[(0,s.jsxs)(q.lR,{className:"text-sm font-semibold",children:["I agree to the"," ",(0,s.jsx)("a",{href:"#",className:"text-primary hover:underline hover:opacity-80",children:"Terms"})," ","&"," ",(0,s.jsx)("a",{href:"#",className:"text-primary hover:underline hover:opacity-80",children:"Privacy Policy"})]}),(0,s.jsx)(q.C5,{})]})]})}),(0,s.jsx)(B,{password:w.getValues("password")||"",showValidation:g}),(0,s.jsx)(c.$,{type:"submit",className:"w-full",disabled:!x||r,children:r?"Signing up...":"Signup"})]})})}var X=r(85814),G=r.n(X),H=r(48563);function Y(){return(0,s.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,s.jsx)(H.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Workflow Builder"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Create an account to get started"})]}),(0,s.jsx)(J,{}),(0,s.jsx)("div",{className:"text-center text-sm",children:(0,s.jsxs)("p",{className:"text-muted-foreground",children:["Already have an account?"," ",(0,s.jsx)(G(),{href:"/login",className:"text-primary font-medium hover:underline",children:"Log in"})]})})]})})}},38255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ruh_ai\\\\workflow_backend\\\\workflow-builder-app\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\signup\\page.tsx","default")},44426:(e,t,r)=>{"use strict";r.d(t,{O5:()=>n,Oh:()=>i,Oj:()=>o,jc:()=>a});var s=r(45880);s.z.object({email:s.z.string().email({message:"Please enter a valid email address."}),password:s.z.string().min(1,{message:"Password is required."})}),s.z.object({email:s.z.string().email({message:"Please enter a valid email address."})});let n=s.z.object({fullName:s.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:s.z.string().email({message:"Please enter a valid email address."}),password:s.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:s.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),a=s.z.object({email:s.z.string().email({message:"Please enter a valid email address."})});s.z.object({newPassword:s.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:s.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]});let i=s.z.object({password:s.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:s.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});function o(e){return{length:e.length>=6&&e.length<=15,hasNumber:/[0-9]/.test(e),hasSymbol:/[!@#$%^&*(),.?":{}|<>]/.test(e)}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57835:(e,t,r)=>{Promise.resolve().then(r.bind(r,35943))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63411:(e,t,r)=>{Promise.resolve().then(r.bind(r,38255))},66156:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(43210),n=globalThis?.document?s.useLayoutEffect:()=>{}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70569:(e,t,r)=>{"use strict";function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}r.d(t,{m:()=>s})},71669:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>h,eI:()=>f,lR:()=>x,lV:()=>d,zB:()=>u});var s=r(60687),n=r(43210),a=r(8730),i=r(27605),o=r(4780),l=r(80013);let d=i.Op,c=n.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(i.xI,{...e})}),m=()=>{let e=n.useContext(c),t=n.useContext(p),{getFieldState:r}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),a=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...a}},p=n.createContext({});function f({className:e,...t}){let r=n.useId();return(0,s.jsx)(p.Provider,{value:{id:r},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function x({className:e,...t}){let{error:r,formItemId:n}=m();return(0,s.jsx)(l.J,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...t})}function h({...e}){let{error:t,formItemId:r,formDescriptionId:n,formMessageId:i}=m();return(0,s.jsx)(a.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${n} ${i}`:`${n}`,"aria-invalid":!!t,...e})}function g({className:e,...t}){let{error:r,formMessageId:n}=m(),a=r?String(r?.message??""):t.children;return a?(0,s.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",e),...t,children:a}):null}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var s=r(60687);r(43210);var n=r(78148),a=r(4780);function i({className:e,...t}){return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,a.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83721:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(43210);function n(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(60687);r(43210);var n=r(4780);function a({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,915,581,261,658,928,814,497,442,651],()=>r(6569));module.exports=s})();