(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{9690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(95155);t(12115);var n=t(40081),i=t(30285),a=t(40619);function o(){return(0,s.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,s.jsx)(n.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Workflow Builder"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Log in to access your workflows"})]}),(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),window.location.href=a._K},children:(0,s.jsx)(i.$,{type:"submit",className:"w-full",children:"Login"})})]})})}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var s=t(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=i(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:c="",children:d,iconNode:u,...h}=e;return(0,s.createElement)("svg",{ref:r,...l,width:n,height:n,stroke:t,strokeWidth:a?24*Number(i)/Number(n):i,className:o("lucide",c),...h},[...u.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),d=(e,r)=>{let t=(0,s.forwardRef)((t,i)=>{let{className:l,...d}=t;return(0,s.createElement)(c,{ref:i,iconNode:r,className:o("lucide-".concat(n(a(e))),"lucide-".concat(e),l),...d})});return t.displayName=a(e),t}},20752:(e,r,t)=>{Promise.resolve().then(t.bind(t,9690))},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>o});var s=t(95155);t(12115);var n=t(99708),i=t(74466),a=t(59434);let o=(0,i.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:i,asChild:l=!1,...c}=e,d=l?n.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,a.cn)(o({variant:t,size:i,className:r})),...c})}},40081:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]])},40619:(e,r,t)=>{"use strict";t.d(r,{C$:()=>i,VV:()=>s,_K:()=>n,qv:()=>a});let s="/login",n="".concat("http://localhost:3001/","?redirect_url=").concat("http://localhost:3000/"),i="/workflows",a=[s,"/signup","/verify-email","/reset-password","/about","/contact"]},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i,s:()=>a});var s=t(52596),n=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,s.$)(r))}function a(e,r){let t=null;return function(){for(var s=arguments.length,n=Array(s),i=0;i<s;i++)n[i]=arguments[i];null!==t&&clearTimeout(t),t=setTimeout(()=>{t=null,e(...n)},r)}}}},e=>{var r=r=>e(e.s=r);e.O(0,[9352,8441,1684,7358],()=>r(20752)),_N_E=e.O()}]);