"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1847],{19103:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var s=a(95155),r=a(12115),n=a(30285),l=a(62523),o=a(59409),i=a(66695),c=a(85339),d=a(69527);let u=()=>{let[e,t]=(0,r.useState)([]),[a,u]=(0,r.useState)(!0),[m,p]=(0,r.useState)(null),[x,f]=(0,r.useState)({name:"",type:"api_key",value:""});(0,r.useEffect)(()=>{h()},[]);let h=async()=>{u(!0),p(null);try{let e=await (0,d.fetchCredentials)();t(e.credentials||[])}catch(e){p(e instanceof Error?e.message:"An unknown error occurred"),console.error("Error fetching credentials:",e)}finally{u(!1)}},v=e=>{let{name:t,value:a}=e.target;f(e=>({...e,[t]:a}))},g=async e=>{e.preventDefault(),u(!0),p(null);try{await (0,d.createCredential)(x),f({name:"",type:"api_key",value:""}),h()}catch(e){p(e instanceof Error?e.message:"An unknown error occurred"),console.error("Error creating credential:",e)}finally{u(!1)}},b=async e=>{if(window.confirm('Are you sure you want to delete credential "'.concat(e,'"?'))){u(!0),p(null);try{await (0,d.deleteCredential)(e),h()}catch(e){p(e instanceof Error?e.message:"An unknown error occurred"),console.error("Error deleting credential:",e)}finally{u(!1)}}};return(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h1",{className:"mb-4 text-2xl font-bold",children:"Credential Manager"}),m&&(0,s.jsxs)("div",{className:"bg-destructive/10 border-destructive text-destructive mb-4 flex items-center gap-2 rounded-md border p-3",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:m})]}),(0,s.jsxs)(i.Zp,{className:"mb-6",children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Add New Credential"})}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("form",{onSubmit:g,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,s.jsx)(l.p,{name:"name",value:x.name,onChange:v,placeholder:"Enter credential name",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Type"}),(0,s.jsxs)(o.l6,{name:"type",value:x.type,onValueChange:e=>f(t=>({...t,type:e})),required:!0,children:[(0,s.jsx)(o.bq,{children:(0,s.jsx)(o.yv,{placeholder:"Select credential type"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"api_key",children:"API Key"}),(0,s.jsx)(o.eb,{value:"oauth_token",children:"OAuth Token"}),(0,s.jsx)(o.eb,{value:"password",children:"Password"}),(0,s.jsx)(o.eb,{value:"connection_string",children:"Connection String"}),(0,s.jsx)(o.eb,{value:"other",children:"Other"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Value"}),(0,s.jsx)(l.p,{name:"value",type:"password",value:x.value,onChange:v,placeholder:"Enter credential value",required:!0})]}),(0,s.jsx)(n.$,{type:"submit",disabled:a,className:"w-full",children:a?"Adding...":"Add Credential"})]})})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{children:"Stored Credentials"})}),(0,s.jsxs)(i.Wu,{children:[a&&(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading credentials..."}),!a&&0===e.length&&(0,s.jsx)("p",{className:"text-muted-foreground",children:"No credentials found. Add one above."}),!a&&e.length>0&&(0,s.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,s.jsx)("div",{className:"rounded-lg border p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.name}),(0,s.jsxs)("p",{className:"text-muted-foreground text-sm",children:["ID: ",e.id]}),(0,s.jsxs)("p",{className:"text-muted-foreground text-sm",children:["Type: ",e.type]})]}),(0,s.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>b(e.id),disabled:a,children:"Delete"})]})},e.id))})]})]})]})}},54897:(e,t,a)=>{a.d(t,{BW:()=>l,VT:()=>n,XI:()=>r,gW:()=>o});var s=a(57383);let r=()=>s.A.get("accessToken")||"",n=()=>{let e=s.A.get("accessToken");return console.log("Client-side access token check:",!!e),!!e},l=(e,t,a,r)=>{s.A.set("accessToken",e,{path:"/",domain:"localhost",secure:!0,sameSite:"lax",expires:a/86400})},o=()=>{s.A.remove("accessToken",{path:"/",domain:"localhost"}),s.A.remove("refreshToken",{path:"/",domain:"localhost"}),s.A.remove("refreshToken",{path:"/api/auth/refresh",domain:"localhost"}),console.log("Client-side cookie clearing attempted for both tokens")}},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>c,yv:()=>d});var s=a(95155);a(12115);var r=a(33897),n=a(66474),l=a(5196),o=a(47863),i=a(59434);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:l,...o}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[l,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:n="popper",...l}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,s.jsx)(x,{}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(f,{})]})})}function p(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(l.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(o.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},62523:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,a)=>{a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>d});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-3 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},89761:(e,t,a)=>{a.d(t,{JR:()=>s,Sn:()=>i});let s="https://app-dev.rapidinnovation.dev/api/v1",r="".concat(s,"/auth"),n="".concat(s,"/workflows"),l="".concat(s,"/mcps"),o="https://ruh-test-api.rapidinnovation.dev/api/v1",i={AUTH:{LOGIN:"".concat(r,"/login"),REGISTER:"".concat(r,"/register"),LOGOUT:"".concat(r,"/logout"),REFRESH:"".concat(r,"/refresh"),FORGOT_PASSWORD:"".concat(r,"/forgot-password"),RESET_PASSWORD:"".concat(r,"/reset-password"),VERIFY_EMAIL:"".concat(r,"/verify-email"),VERIFY_EMAIL_OTP:"".concat(r,"/verify-email-otp"),UPDATE_PASSWORD:"".concat(r,"/update-password")},WORKFLOWS:{LIST:"".concat(n),CREATE:"".concat(n),GET:e=>"".concat(n,"/").concat(e),UPDATE:e=>"".concat(n,"/").concat(e),DELETE:e=>"".concat(n,"/").concat(e),EXECUTE:e=>"".concat(n,"/").concat(e,"/execute")},MCPS:{LIST:"".concat(l),GET:e=>"".concat(l,"/").concat(e)},WORKFLOW_EXECUTION:{EXECUTE:"".concat(o,"/workflow-execute/execute"),APPROVE:"".concat(o,"/workflow-execute/approve"),STREAM:"".concat(o,"/workflow-execute/stream")}}}}]);