(()=>{var e={};e.id=735,e.ids=[735],e.modules={875:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i,generateMetadata:()=>n});var s=t(37413);t(61120);var a=t(75962);async function n({params:e}){let r=await e;return{title:`Workflow: ${r.id}`,description:"View workflow details and metadata"}}async function i({params:e}){let{id:r}=await e;return(0,s.jsx)(a.default,{id:r})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4733:(e,r,t)=>{"use strict";t.d(r,{default:()=>y});var s=t(60687),a=t(43210),n=t(16189),i=t(85814),o=t.n(i);t(71563);var d=t(29523),l=t(44493),c=t(48563),u=t(62688);let x=(0,u.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var p=t(41862),m=t(93613),h=t(96882),f=t(40228);let b=(0,u.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var w=t(97840);let v=(0,u.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),k=e=>{try{return new Date(e).toLocaleString()}catch(e){return"Unknown date"}};function y({id:e}){let r=(0,n.useRouter)(),[t,i]=(0,a.useState)(null),[u,y]=(0,a.useState)(!0),[g,j]=(0,a.useState)(null),N=e=>"workflow"in e&&e.workflow?e.workflow:e,_=()=>{r.push(`/?workflow_id=${e}`)},A=async()=>{try{let r=await fetch(`https://app-dev.rapidinnovation.dev/api/v1/workflows/${e}/execute`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!r.ok)throw Error(`Failed to execute workflow: ${r.status} ${r.statusText}`);let t=await r.json();console.log("Execution result:",t),alert("Workflow execution started successfully!")}catch(e){console.error("Failed to execute workflow:",e),alert(`Failed to execute workflow: ${e instanceof Error?e.message:"Unknown error"}`)}};return(0,s.jsxs)("main",{className:"bg-background min-h-screen",children:[(0,s.jsx)("div",{className:"brand-gradient-indicator text-brand-white-text p-6 shadow-md",children:(0,s.jsx)("div",{className:"container mx-auto",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"rounded-md bg-white p-2 shadow-md",children:(0,s.jsx)(c.A,{className:"text-brand-primary h-6 w-6"})}),(0,s.jsx)("h1",{className:"font-primary text-2xl font-bold",children:"Workflow Details"})]}),(0,s.jsx)(o(),{href:"/workflows",children:(0,s.jsxs)(d.$,{variant:"outline",className:"text-brand-primary hover:bg-brand-card-hover border-brand-stroke bg-white",children:[(0,s.jsx)(x,{className:"mr-2 h-4 w-4"}),"Back to Workflows"]})})]})})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:u?(0,s.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,s.jsx)(p.A,{className:"text-brand-primary h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"font-primary ml-2 text-lg",children:"Loading workflow details..."})]}):g?(0,s.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center text-center",children:[(0,s.jsx)(m.A,{className:"text-brand-unpublish mb-4 h-12 w-12"}),(0,s.jsx)("h3",{className:"font-primary mb-2 text-xl font-semibold",children:"Failed to Load Workflow Details"}),(0,s.jsx)("p",{className:"text-brand-secondary-font mb-4",children:g}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(d.$,{onClick:()=>window.location.reload(),className:"brand-gradient-indicator text-brand-white-text",children:"Try Again"}),(0,s.jsx)(o(),{href:"/workflows",children:(0,s.jsx)(d.$,{variant:"outline",className:"border-brand-stroke text-brand-primary hover:bg-brand-clicked",children:"Back to Workflows"})})]})]}):t?(()=>{let e=N(t);return(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,s.jsxs)(l.Zp,{className:"border-brand-stroke bg-brand-card lg:col-span-2",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsx)(l.ZB,{className:"font-primary text-brand-primary-font text-2xl",children:e.name||"Untitled Workflow"}),(0,s.jsx)(l.BT,{className:"font-secondary text-brand-secondary-font",children:e.description||"No description provided"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"text-brand-secondary h-5 w-5"}),(0,s.jsx)("span",{className:"font-primary font-medium",children:"ID:"}),(0,s.jsx)("span",{className:"text-brand-secondary-font",children:e.id})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"text-brand-secondary h-5 w-5"}),(0,s.jsx)("span",{className:"font-primary font-medium",children:"Created:"}),(0,s.jsx)("span",{className:"text-brand-secondary-font",children:e.created_at?k(e.created_at):"Unknown"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(b,{className:"text-brand-secondary h-5 w-5"}),(0,s.jsx)("span",{className:"font-primary font-medium",children:"Last Updated:"}),(0,s.jsx)("span",{className:"text-brand-secondary-font",children:e.updated_at?k(e.updated_at):"Unknown"})]}),void 0!==e.execution_count&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"text-brand-secondary h-5 w-5"}),(0,s.jsx)("span",{className:"font-primary font-medium",children:"Executions:"}),(0,s.jsx)("span",{className:"text-brand-secondary-font",children:e.execution_count})]})]})}),(0,s.jsxs)(l.wL,{className:"flex justify-between",children:[(0,s.jsxs)(d.$,{variant:"outline",onClick:_,className:"border-brand-stroke text-brand-primary hover:bg-brand-clicked flex items-center gap-2",children:[(0,s.jsx)(v,{className:"h-4 w-4"}),"Open in Editor"]}),(0,s.jsxs)(d.$,{onClick:A,className:"brand-gradient-indicator text-brand-white-text flex items-center gap-2",children:[(0,s.jsx)(w.A,{className:"h-4 w-4"}),"Execute Workflow"]})]})]}),(0,s.jsxs)(l.Zp,{className:"border-brand-stroke bg-brand-card",children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{className:"font-primary text-brand-primary-font",children:"Workflow Metadata"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[e.builder_url&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Builder URL"}),(0,s.jsx)("p",{className:"text-brand-secondary-font text-xs break-all",children:e.builder_url})]}),e.user_id&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Owner"}),(0,s.jsx)("p",{className:"text-brand-secondary-font text-sm",children:e.user_id})]}),e.status&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Status"}),(0,s.jsx)("div",{className:`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${"active"===e.status?"bg-brand-tick/20 text-brand-tick":"bg-brand-unpublish/20 text-brand-unpublish"}`,children:e.status})]}),e.tags&&Array.isArray(e.tags)&&e.tags.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-primary mb-1 text-sm font-medium",children:"Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e,r)=>(0,s.jsx)("span",{className:"bg-brand-primary/10 text-brand-primary inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",children:e},r))})]})]})})]})]})})():(0,s.jsxs)("div",{className:"flex h-64 flex-col items-center justify-center text-center",children:[(0,s.jsx)(m.A,{className:"text-brand-unpublish mb-4 h-12 w-12"}),(0,s.jsx)("h3",{className:"font-primary mb-2 text-xl font-semibold",children:"Workflow Not Found"}),(0,s.jsx)("p",{className:"text-brand-secondary-font mb-4",children:"The workflow you're looking for doesn't exist or you don't have permission to view it."}),(0,s.jsx)(o(),{href:"/workflows",children:(0,s.jsx)(d.$,{className:"brand-gradient-indicator text-brand-white-text",children:"Back to Workflows"})})]})})]})}},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n,s:()=>i});var s=t(49384),a=t(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}function i(e,r){let t=null;return function(...s){null!==t&&clearTimeout(t),t=setTimeout(()=>{t=null,e(...s)},r)}}},10634:(e,r,t)=>{Promise.resolve().then(t.bind(t,4733))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>o});var s=t(60687);t(43210);var a=t(8730),n=t(24224),i=t(4780);let o=(0,n.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:t,className:e})),...d})}},30073:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["workflows",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,875)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\workflows\\[id]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,73242)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\workflows\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\workflows\\[id]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/workflows/[id]/page",pathname:"/workflows/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:e=>{"use strict";e.exports=require("path")},40228:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var s=t(60687);t(43210);var a=t(4780);function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-3 shadow-sm",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...r})}},48563:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:n="",children:i,iconNode:l,...c},u)=>(0,s.createElement)("svg",{ref:u,...d,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:o("lucide",n),...c},[...l.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(i)?i:[i]])),c=(e,r)=>{let t=(0,s.forwardRef)(({className:t,...n},d)=>(0,s.createElement)(l,{ref:d,iconNode:r,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,t),...n}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73242:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(37413);function a({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-background",children:e})}},74075:e=>{"use strict";e.exports=require("zlib")},75962:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ruh_ai\\\\workflow_backend\\\\workflow-builder-app\\\\src\\\\app\\\\workflows\\\\[id]\\\\WorkflowDetailsClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\workflows\\[id]\\WorkflowDetailsClient.tsx","default")},76130:(e,r,t)=>{Promise.resolve().then(t.bind(t,75962))},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93613:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96882:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97840:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,915,581,261,658,928,814,651],()=>t(30073));module.exports=s})();