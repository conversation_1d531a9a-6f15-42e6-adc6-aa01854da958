(()=>{var e={};e.id=700,e.ids=[700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,s)=>{"use strict";s.d(r,{cn:()=>i,s:()=>n});var t=s(49384),a=s(82348);function i(...e){return(0,a.QP)((0,t.$)(e))}function n(e,r){let s=null;return function(...t){null!==s&&clearTimeout(s),s=setTimeout(()=>{s=null,e(...t)},r)}}},5336:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},17978:(e,r,s)=>{Promise.resolve().then(s.bind(s,85316))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,s)=>{"use strict";s.d(r,{$:()=>d,r:()=>o});var t=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let o=(0,i.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:s,asChild:i=!1,...d}){let l=i?a.DX:"button";return(0,t.jsx)(l,{"data-slot":"button",className:(0,n.cn)(o({variant:r,size:s,className:e})),...d})}},33277:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>b});var t=s(60687),a=s(43210),i=s(27605),n=s(63442),o=s(44426),d=s(29523),l=s(89667),c=s(71669),u=s(5336),m=s(41862),p=s(35399);function x(){let[e,r]=(0,a.useState)(!1),[s,x]=(0,a.useState)(!1),f=(0,i.mN)({resolver:(0,n.u)(o.jc),defaultValues:{email:""}}),h=async e=>{r(!0);try{await p.authApi.forgotPassword(e.email),x(!0)}catch(e){console.error("Reset password error:",e),f.setError("root",{type:"manual",message:e.message||"Failed to send reset link. Please try again."})}finally{r(!1)}};return s?(0,t.jsx)("div",{className:"space-y-6 text-center",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)("div",{className:"rounded-full bg-green-100 p-3",children:(0,t.jsx)(u.A,{className:"h-10 w-10 text-green-600"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Reset Link Sent"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"We've sent a password reset link to your email. Please check your inbox and follow the instructions to reset your password."})]})}):(0,t.jsx)(c.lV,{...f,children:(0,t.jsxs)("form",{onSubmit:f.handleSubmit(h),className:"space-y-6",children:[f.formState.errors.root&&(0,t.jsx)("div",{className:"rounded-md bg-red-50 p-3 text-sm text-red-500",children:f.formState.errors.root.message}),(0,t.jsx)(c.zB,{control:f.control,name:"email",render:({field:r})=>(0,t.jsxs)(c.eI,{children:[(0,t.jsx)(c.lR,{children:"Email"}),(0,t.jsx)(c.MJ,{children:(0,t.jsx)(l.p,{placeholder:"Enter your email",type:"email",disabled:e,...r})}),(0,t.jsx)(c.C5,{})]})}),(0,t.jsxs)(d.$,{type:"submit",className:"w-full",disabled:e,children:[e&&(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Send Reset Link"]})]})})}var f=s(85814),h=s.n(f),g=s(48563);function b(){return(0,t.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,t.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,t.jsx)(g.A,{className:"h-8 w-8 text-white"})}),(0,t.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Reset Password"}),(0,t.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Enter your email to receive a password reset link"})]}),(0,t.jsx)(x,{}),(0,t.jsx)("div",{className:"text-center text-sm",children:(0,t.jsxs)("p",{className:"text-muted-foreground",children:["Remember your password?"," ",(0,t.jsx)(h(),{href:"/login",className:"text-primary font-medium hover:underline",children:"Log in"})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},41862:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44426:(e,r,s)=>{"use strict";s.d(r,{O5:()=>a,Oh:()=>n,Oj:()=>o,jc:()=>i});var t=s(45880);t.z.object({email:t.z.string().email({message:"Please enter a valid email address."}),password:t.z.string().min(1,{message:"Password is required."})}),t.z.object({email:t.z.string().email({message:"Please enter a valid email address."})});let a=t.z.object({fullName:t.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:t.z.string().email({message:"Please enter a valid email address."}),password:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:t.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),i=t.z.object({email:t.z.string().email({message:"Please enter a valid email address."})});t.z.object({newPassword:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:t.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]});let n=t.z.object({password:t.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:t.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});function o(e){return{length:e.length>=6&&e.length<=15,hasNumber:/[0-9]/.test(e),hasSymbol:/[!@#$%^&*(),.?":{}|<>]/.test(e)}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58146:(e,r,s)=>{Promise.resolve().then(s.bind(s,33277))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63209:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var t=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let l={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85316)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\reset-password\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,r,s)=>{"use strict";s.d(r,{C5:()=>g,MJ:()=>h,eI:()=>x,lR:()=>f,lV:()=>l,zB:()=>u});var t=s(60687),a=s(43210),i=s(8730),n=s(27605),o=s(4780),d=s(80013);let l=n.Op,c=a.createContext({}),u=({...e})=>(0,t.jsx)(c.Provider,{value:{name:e.name},children:(0,t.jsx)(n.xI,{...e})}),m=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:s}=(0,n.xW)(),t=(0,n.lN)({name:e.name}),i=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...i}},p=a.createContext({});function x({className:e,...r}){let s=a.useId();return(0,t.jsx)(p.Provider,{value:{id:s},children:(0,t.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...r})})}function f({className:e,...r}){let{error:s,formItemId:a}=m();return(0,t.jsx)(d.J,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function h({...e}){let{error:r,formItemId:s,formDescriptionId:a,formMessageId:n}=m();return(0,t.jsx)(i.DX,{"data-slot":"form-control",id:s,"aria-describedby":r?`${a} ${n}`:`${a}`,"aria-invalid":!!r,...e})}function g({className:e,...r}){let{error:s,formMessageId:a}=m(),i=s?String(s?.message??""):r.children;return i?(0,t.jsx)("p",{"data-slot":"form-message",id:a,className:(0,o.cn)("text-destructive text-sm",e),...r,children:i}):null}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,s)=>{"use strict";s.d(r,{J:()=>n});var t=s(60687);s(43210);var a=s(78148),i=s(4780);function n({className:e,...r}){return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85316:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ruh_ai\\\\workflow_backend\\\\workflow-builder-app\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\reset-password\\page.tsx","default")},89667:(e,r,s)=>{"use strict";s.d(r,{p:()=>i});var t=s(60687);s(43210);var a=s(4780);function i({className:e,type:r,...s}){return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,915,581,261,658,928,814,497,442,651],()=>s(63209));module.exports=t})();