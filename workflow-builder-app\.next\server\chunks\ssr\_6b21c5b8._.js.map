{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/ruh_ai/workflow_backend/workflow-builder-app/src/lib/cookies.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport const getAccessToken = async () => {\r\n  const cookieStore = await cookies();\r\n  const accessToken = cookieStore.get(\"accessToken\");\r\n  if (accessToken) {\r\n    return accessToken.value;\r\n  } else {\r\n    return \"\";\r\n  }\r\n};\r\n\r\nexport const getRefreshToken = async () => {\r\n  const cookieStore = await cookies();\r\n  const refreshToken = cookieStore.get(\"refreshToken\");\r\n  return refreshToken?.value || null;\r\n};\r\n\r\nexport const checkAccessToken = async () => {\r\n  const cookieStore = await cookies();\r\n  const tokenCookie = cookieStore.get(\"accessToken\");\r\n  return Boolean(tokenCookie?.value);\r\n};\r\n\r\nexport const setAuthCookies = async (\r\n  accessToken: string,\r\n  refreshToken: string | null,\r\n  accessTokenAge: number,\r\n  refreshTokenAge: number | null,\r\n) => {\r\n  const cookieStore = await cookies();\r\n\r\n  // Set access token as a non-HttpOnly cookie (accessible to JavaScript)\r\n  // This is for client-side access to the token for API calls\r\n  cookieStore.set(\"accessToken\", accessToken, {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: false, // Accessible to JavaScript\r\n    sameSite: \"lax\", // Changed from \"none\" to \"lax\" for better CSRF protection\r\n    secure: true,\r\n    maxAge: accessTokenAge,\r\n  });\r\n\r\n  // Set a secure HTTP-only cookie for the refresh token\r\n  // This provides better security as it's not accessible to JavaScript\r\n  if (refreshToken && refreshTokenAge) {\r\n    cookieStore.set(\"refreshToken\", refreshToken, {\r\n      path: \"/api/auth/refresh\", // Restrict to refresh endpoint only\r\n      domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n      httpOnly: true, // Not accessible to JavaScript\r\n      sameSite: \"lax\", // Changed from \"none\" to \"lax\" for better CSRF protection\r\n      secure: true,\r\n      maxAge: refreshTokenAge,\r\n    });\r\n  }\r\n};\r\n\r\nexport const clearAuthCookies = async () => {\r\n  \"use server\";\r\n\r\n  const cookieStore = await cookies();\r\n\r\n  // Clear access token cookie\r\n  cookieStore.set(\"accessToken\", \"\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: false, // Match the setting used when creating the cookie\r\n    secure: true,\r\n    sameSite: \"lax\", // Match the setting used when creating the cookie\r\n    maxAge: 0,\r\n    expires: new Date(0),\r\n  });\r\n\r\n  // Clear refresh token cookie with the specific path used when setting it\r\n  cookieStore.set(\"refreshToken\", \"\", {\r\n    path: \"/api/auth/refresh\", // Match the path restriction used when creating the cookie\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: true, // Match the setting used when creating the cookie\r\n    secure: true,\r\n    sameSite: \"lax\", // Match the setting used when creating the cookie\r\n    maxAge: 0,\r\n    expires: new Date(0),\r\n  });\r\n\r\n  // Also clear refresh token with root path as a fallback\r\n  cookieStore.set(\"refreshToken\", \"\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: true,\r\n    secure: true,\r\n    sameSite: \"lax\",\r\n    maxAge: 0,\r\n    expires: new Date(0),\r\n  });\r\n\r\n  console.log(\"Server-side auth cookies cleared with multiple paths\");\r\n};\r\n\r\nexport const setRefreshingTokenCookie = async () => {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set(\"refreshingToken\", \"true\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    httpOnly: false, // Must be false to be accessible from JavaScript\r\n    sameSite: \"none\",\r\n    maxAge: 60, // 1 minute should be enough to handle the refresh\r\n  });\r\n};\r\n\r\nexport const clearRefreshingTokenCookie = async () => {\r\n  const cookieStore = await cookies();\r\n  cookieStore.set(\"refreshingToken\", \"\", {\r\n    path: \"/\",\r\n    domain: process.env.NEXT_PUBLIC_COOKIES_DOMAIN,\r\n    maxAge: 0,\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;;;;;AAEO,MAAM,uCAAY,GAAZ,iBAAiB;IAC5B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,YAAY,GAAG,CAAC;IACpC,IAAI,aAAa;QACf,OAAO,YAAY,KAAK;IAC1B,OAAO;QACL,OAAO;IACT;AACF;AAEO,MAAM,uCAAa,GAAb,kBAAkB;IAC7B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,OAAO,cAAc,SAAS;AAChC;AAEO,MAAM,uCAAc,GAAd,mBAAmB;IAC9B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc,YAAY,GAAG,CAAC;IACpC,OAAO,QAAQ,aAAa;AAC9B;AAEO,MAAM,uCAAY,GAAZ,iBAAiB,OAC5B,aACA,cACA,gBACA;IAEA,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,uEAAuE;IACvE,4DAA4D;IAC5D,YAAY,GAAG,CAAC,eAAe,aAAa;QAC1C,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IAEA,sDAAsD;IACtD,qEAAqE;IACrE,IAAI,gBAAgB,iBAAiB;QACnC,YAAY,GAAG,CAAC,gBAAgB,cAAc;YAC5C,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;QACV;IACF;AACF;AAEO,MAAM,uCAAc,GAAd,mBAAmB;IAG9B,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhC,4BAA4B;IAC5B,YAAY,GAAG,CAAC,eAAe,IAAI;QACjC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS,IAAI,KAAK;IACpB;IAEA,yEAAyE;IACzE,YAAY,GAAG,CAAC,gBAAgB,IAAI;QAClC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS,IAAI,KAAK;IACpB;IAEA,wDAAwD;IACxD,YAAY,GAAG,CAAC,gBAAgB,IAAI;QAClC,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,SAAS,IAAI,KAAK;IACpB;IAEA,QAAQ,GAAG,CAAC;AACd;AAEO,MAAM,uCAAsB,GAAtB,2BAA2B;IACtC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,mBAAmB,QAAQ;QACzC,MAAM;QACN,MAAM;QACN,UAAU;QACV,UAAU;QACV,QAAQ;IACV;AACF;AAEO,MAAM,uCAAwB,GAAxB,6BAA6B;IACxC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,YAAY,GAAG,CAAC,mBAAmB,IAAI;QACrC,MAAM;QACN,MAAM;QACN,QAAQ;IACV;AACF;;;IAlHa;IAUA;IAMA;IAMA;IAiCA;IAyCA;IAWA;;AA3GA,+OAAA;AAUA,+OAAA;AAMA,+OAAA;AAMA,+OAAA;AAiCA,+OAAA;AAyCA,+OAAA;AAWA,+OAAA", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}