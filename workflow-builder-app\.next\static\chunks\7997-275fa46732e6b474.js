"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7997],{8699:(e,a,o)=>{o.d(a,{w:()=>r});var t=o(34477);let r=(0,t.createServerReference)("7f918b96ddb9292f8563a124aa443704dbe4f5b95f",t.callServer,void 0,t.findSourceMapURL,"setAuthCookies")},27997:(e,a,o)=>{o.d(a,{authApi:()=>v});var t=o(4982),r=o(8699),s=o(38378),l=o(34477);let n=(0,l.createServerReference)("7fb944170acb997720913e910ba1bd3af14f6af1c3",l.callServer,void 0,l.findSourceMapURL,"getAccessToken");var i=o(54897),d=o(54361),c=o(89761),u=o(40619);let v={login:async e=>{let{email:a,password:o}=e;try{let e=await t.ZQ.post(c.Sn.AUTH.LOGIN,{email:a,password:o});if(!e.data.access_token)throw Error("Login failed: Unexpected response from server.");await (0,r.w)(e.data.access_token,e.data.refresh_token,e.data.accessTokenAge||36e3,e.data.refreshTokenAge||86400),(0,i.BW)(e.data.access_token,e.data.refresh_token,e.data.accessTokenAge||36e3,e.data.refreshTokenAge||86400),console.log("Client-side auth cookies set after login");let s=u.C$;try{let a=await v.getCurrentUser();d.k.getState().setUser({fullName:a.fullName,email:a.email,accessToken:e.data.access_token}),s=u.C$}catch(e){console.error("Failed to fetch user details:",e)}return{loginData:e.data,redirectPath:s}}catch(e){var s,l,n,g,f,w;if(d.k.getState().clearUser(),(null===(s=e.response)||void 0===s?void 0:s.status)===404)throw Error("User not found.");if((null===(l=e.response)||void 0===l?void 0:l.status)===412)throw Error("Account inactive. Please check your email for verification.");throw Error((null===(g=e.response)||void 0===g?void 0:null===(n=g.data)||void 0===n?void 0:n.detail)||(null===(w=e.response)||void 0===w?void 0:null===(f=w.data)||void 0===f?void 0:f.message)||"Invalid Credentials")}},signup:async e=>{try{let{email:a,fullName:o,password:r}=e;return(await t.ZQ.post(c.Sn.AUTH.REGISTER,{full_name:o,email:a,password:r})).data}catch(e){var a,o,r,s,l,n,i;if((null===(a=e.response)||void 0===a?void 0:a.status)===409)throw Error((null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.detail)||"Email already registered.");throw Error((null===(r=e.response)||void 0===r?void 0:null===(o=r.data)||void 0===o?void 0:o.detail)||(null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message)||"Signup failed")}},logout:async()=>{try{await (0,s.h)(),(0,i.gW)(),console.log("Client-side auth cookies cleared during logout"),d.k.getState().clearUser(),console.log("User store cleared during logout"),window.location.href=u.VV}catch(e){console.error("Error during logout:",e),await (0,s.h)(),(0,i.gW)(),d.k.getState().clearUser(),window.location.href=u.VV}},forgotPassword:async e=>{try{return(await t.ZQ.post(c.Sn.AUTH.FORGOT_PASSWORD,null,{params:{email:e}})).data}catch(e){var a,o,r,s;throw Error((null===(o=e.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.detail)||(null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||"Failed to send password reset email")}},resetPassword:async(e,a)=>{let{newPassword:o,confirmNewPassword:r}=a;try{return(await t.ZQ.post(c.Sn.AUTH.UPDATE_PASSWORD,{token:e,new_password:o,confirm_new_password:r})).data}catch(e){var s,l,n,i;throw Error((null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.detail)||(null===(i=e.response)||void 0===i?void 0:null===(n=i.data)||void 0===n?void 0:n.message)||"Password reset failed")}},updatePassword:async e=>{try{let a={token:e.token,new_password:e.password,confirm_new_password:e.password};return(await t.ZQ.post(c.Sn.AUTH.UPDATE_PASSWORD,a)).data}catch(e){var a,o,r,s;throw Error((null===(o=e.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.detail)||(null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||"Password update failed")}},verifyEmailOtp:async e=>{try{return(await t.ZQ.post(c.Sn.AUTH.VERIFY_EMAIL_OTP,{token:e})).data}catch(e){var a,o,r,s;throw Error((null===(o=e.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.detail)||(null===(s=e.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||"Email verification failed")}},getCurrentUser:async()=>{try{console.log("Making request to /users/me");let e=await t.ZQ.get("/users/me");return console.log("Successfully retrieved user data"),e.data}catch(t){var e,a,o,r,s;throw console.error("Get current user error:",t),(null===(e=t.response)||void 0===e?void 0:e.status)===403&&(console.log("Authentication error: 403 Forbidden. Token may be invalid or expired."),d.k.getState().clearUser(),(0,i.gW)()),Error((null===(o=t.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.detail)||(null===(s=t.response)||void 0===s?void 0:null===(r=s.data)||void 0===r?void 0:r.message)||"Failed to fetch user details")}},isLoggedIn:async()=>{try{return await v.getCurrentUser(),!0}catch(e){return!1}},isAuthenticated:async()=>{{let e=(0,i.VT)();return console.log("Client-side isAuthenticated check:",e),e}},getAccessToken:async()=>(0,i.XI)(),generateAccessToken:async e=>{try{let a=await t.ZQ.post("/auth/access-token",{},{params:{refresh_token:e}});if(a.data.success&&a.data.access_token){let e=new Date(a.data.tokenExpireAt).getTime(),o=new Date().getTime(),t=Math.floor((e-o)/1e3);await (0,r.w)(a.data.access_token,null,t,null);let s=d.k.getState().user;s&&d.k.getState().setUser({...s,accessToken:a.data.access_token})}return a.data}catch(e){var a,o,s,l;throw Error((null===(o=e.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.detail)||(null===(l=e.response)||void 0===l?void 0:null===(s=l.data)||void 0===s?void 0:s.message)||"Failed to generate new access token")}},googleLogin:async()=>{try{let e="".concat(c.JR,"/auth/google-login");window.location.href=e}catch(e){console.error("Error during Google login:",e),window.location.href="".concat(c.JR,"/auth/google-login")}},finalizeGoogleLogin:async()=>{try{let e=await v.getCurrentUser(),a=await n();d.k.getState().setUser({fullName:e.fullName,email:e.email,accessToken:a})}catch(e){d.k.getState().clearUser(),console.error("Failed to retrieve user details after Google login:",e)}}}},38378:(e,a,o)=>{o.d(a,{h:()=>r});var t=o(34477);let r=(0,t.createServerReference)("7f4f3df198d69edddaa774690ffaa844c9a5b9ebda",t.callServer,void 0,t.findSourceMapURL,"clearAuthCookies")}}]);