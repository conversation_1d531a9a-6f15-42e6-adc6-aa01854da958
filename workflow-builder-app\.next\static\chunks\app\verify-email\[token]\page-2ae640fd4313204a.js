(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1477,4477],{4982:(e,t,r)=>{"use strict";r.d(t,{ZQ:()=>x,Ek:()=>y,yq:()=>w});var a=r(23464),n=r(79971);let s="access_token",o="refresh_token",c=async()=>{let e=(0,n.getCookie)(s);return e?String(e):null},i=async()=>{(0,n.deleteCookie)(s,{path:"/"}),(0,n.deleteCookie)(o,{path:"/"}),(0,n.deleteCookie)(o,{path:"/api/auth/refresh"}),console.log("Auth cookies cleared with specific paths")};var l=r(54897),u=r(40619),d=r(65453),h=r(46786);let f=(0,d.v)()((0,h.Zr)((e,t)=>({user:null,setUser:t=>e({user:{...t,isAuthenticated:!0}}),clearUser:()=>e({user:null}),isAuthenticated:()=>{var e;return!!(null===(e=t().user)||void 0===e?void 0:e.isAuthenticated)}}),{name:"user-storage"})),v=()=>{f.getState().clearUser()},p=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?(0,l.XI)():await c()},g=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return async t=>{t.headers||(t.headers={});let r=await p(e.enableClientSideToken);return r?(t.headers.Authorization="Bearer ".concat(r),console.log("[DEBUG] Added Authorization header with token (length: ".concat(r.length,")"))):console.log("[DEBUG] No token available for request to ".concat(t.url)),t.headers["ngrok-skip-browser-warning"]="true",e.customHeaders&&Object.assign(t.headers,e.customHeaders),t}},m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return async r=>{var n,s;let o=r.config;if(!t.enableTokenRefresh)return Promise.reject(r);if(((null===(n=r.response)||void 0===n?void 0:n.status)===401||(null===(s=r.response)||void 0===s?void 0:s.status)===403)&&!o._retry){o._retry=!0;try{let t=a.A.create(),r=await t.post("/api/auth/refresh");if(r.data.success&&r.data.accessToken)return o.headers={...o.headers,Authorization:"Bearer ".concat(r.data.accessToken)},e(o);return await i(),v(),window.location.href=u.VV,Promise.reject(Error("Token refresh failed"))}catch(e){return await i(),v(),window.location.href=u.VV,Promise.reject(e)}}return Promise.reject(r)}},k=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=a.A.create({baseURL:t.baseURL||"https://app-dev.rapidinnovation.dev/api/v1",withCredentials:null!==(e=t.withCredentials)&&void 0!==e&&e});return r.interceptors.request.use(g(t),e=>Promise.reject(Error("Request interceptor error: ".concat(e.message||"Unknown error")))),r.interceptors.response.use(e=>e,m(r,t)),r};k({enableTokenRefresh:!0,enableClientSideToken:!0});let w=k({enableTokenRefresh:!1,enableClientSideToken:!0}),x=k({enableTokenRefresh:!0,enableClientSideToken:!0,withCredentials:!0}),y=a.A.create()},19274:(e,t,r)=>{Promise.resolve().then(r.bind(r,52555))},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:l="",children:u,iconNode:d,...h}=e;return(0,a.createElement)("svg",{ref:t,...i,width:n,height:n,stroke:r,strokeWidth:o?24*Number(s)/Number(n):s,className:c("lucide",l),...h},[...d.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,s)=>{let{className:i,...u}=r;return(0,a.createElement)(l,{ref:s,iconNode:t,className:c("lucide-".concat(n(o(e))),"lucide-".concat(e),i),...u})});return r.displayName=o(e),r}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,r:()=>c});var a=r(95155);r(12115);var n=r(99708),s=r(74466),o=r(59434);let c=(0,s.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:r,size:s,asChild:i=!1,...l}=e,u=i?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,o.cn)(c({variant:r,size:s,className:t})),...l})}},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return a.callServer},createServerReference:function(){return s},findSourceMapURL:function(){return n.findSourceMapURL}});let a=r(53806),n=r(31818),s=r(34979).createServerReference},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},40081:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("workflow",[["rect",{width:"8",height:"8",x:"3",y:"3",rx:"2",key:"by2w9f"}],["path",{d:"M7 11v4a2 2 0 0 0 2 2h4",key:"xkn7yn"}],["rect",{width:"8",height:"8",x:"13",y:"13",rx:"2",key:"1cgmvn"}]])},40619:(e,t,r)=>{"use strict";r.d(t,{C$:()=>s,VV:()=>a,_K:()=>n,qv:()=>o});let a="/login",n="".concat("http://localhost:3001/","?redirect_url=").concat("http://localhost:3000/"),s="/workflows",o=[a,"/signup","/verify-email","/reset-password","/about","/contact"]},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var a=r(95155),n=r(12115),s=r(35695),o=r(27997),c=r(30285),i=r(40081),l=r(51154),u=r(40646),d=r(54861),h=r(6874),f=r.n(h);function v(){let e=(0,s.useParams)();(0,s.useRouter)();let[t,r]=(0,n.useState)("loading"),[h,v]=(0,n.useState)("Verifying your email...");return(0,n.useEffect)(()=>{(async()=>{try{let t=e.token;await o.authApi.verifyEmailOtp(t),r("success"),v("Your email has been verified successfully!")}catch(e){console.error("Email verification error:",e),r("error"),v("Failed to verify your email. The link may be invalid or expired.")}})()},[e.token]),(0,a.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,a.jsx)(i.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Email Verification"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-4 py-8",children:["loading"===t&&(0,a.jsx)(l.A,{className:"text-primary h-16 w-16 animate-spin"}),"success"===t&&(0,a.jsx)(u.A,{className:"h-16 w-16 text-green-500"}),"error"===t&&(0,a.jsx)(d.A,{className:"h-16 w-16 text-red-500"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center",children:h})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(c.$,{asChild:!0,children:(0,a.jsx)(f(),{href:"/login",children:"Go to Login"})})})]})})}},54361:(e,t,r)=>{"use strict";r.d(t,{k:()=>s});var a=r(65453),n=r(46786);let s=(0,a.v)()((0,n.Zr)((e,t)=>({user:null,isAuthenticated:!1,setUser:t=>e(e=>({user:e.user?{...e.user,...t}:t,isAuthenticated:null!=t&&!!t.accessToken})),clearUser:()=>e({user:null,isAuthenticated:!1}),logout:async()=>{try{let{authApi:e}=await Promise.all([r.e(7997),r.e(4477)]).then(r.bind(r,27997));await e.logout()}catch(e){console.error("Error during logout:",e),t().clearUser()}}}),{name:"user-storage"}))},54861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},54897:(e,t,r)=>{"use strict";r.d(t,{BW:()=>o,VT:()=>s,XI:()=>n,gW:()=>c});var a=r(57383);let n=()=>a.A.get("accessToken")||"",s=()=>{let e=a.A.get("accessToken");return console.log("Client-side access token check:",!!e),!!e},o=(e,t,r,n)=>{a.A.set("accessToken",e,{path:"/",domain:"localhost",secure:!0,sameSite:"lax",expires:r/86400})},c=()=>{a.A.remove("accessToken",{path:"/",domain:"localhost"}),a.A.remove("refreshToken",{path:"/",domain:"localhost"}),a.A.remove("refreshToken",{path:"/api/auth/refresh",domain:"localhost"}),console.log("Client-side cookie clearing attempted for both tokens")}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s,s:()=>o});var a=r(52596),n=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function o(e,t){let r=null;return function(){for(var a=arguments.length,n=Array(a),s=0;s<a;s++)n[s]=arguments[s];null!==r&&clearTimeout(r),r=setTimeout(()=>{r=null,e(...n)},t)}}},89761:(e,t,r)=>{"use strict";r.d(t,{JR:()=>a,Sn:()=>i});let a="https://app-dev.rapidinnovation.dev/api/v1",n="".concat(a,"/auth"),s="".concat(a,"/workflows"),o="".concat(a,"/mcps"),c="https://ruh-test-api.rapidinnovation.dev/api/v1",i={AUTH:{LOGIN:"".concat(n,"/login"),REGISTER:"".concat(n,"/register"),LOGOUT:"".concat(n,"/logout"),REFRESH:"".concat(n,"/refresh"),FORGOT_PASSWORD:"".concat(n,"/forgot-password"),RESET_PASSWORD:"".concat(n,"/reset-password"),VERIFY_EMAIL:"".concat(n,"/verify-email"),VERIFY_EMAIL_OTP:"".concat(n,"/verify-email-otp"),UPDATE_PASSWORD:"".concat(n,"/update-password")},WORKFLOWS:{LIST:"".concat(s),CREATE:"".concat(s),GET:e=>"".concat(s,"/").concat(e),UPDATE:e=>"".concat(s,"/").concat(e),DELETE:e=>"".concat(s,"/").concat(e),EXECUTE:e=>"".concat(s,"/").concat(e,"/execute")},MCPS:{LIST:"".concat(o),GET:e=>"".concat(o,"/").concat(e)},WORKFLOW_EXECUTION:{EXECUTE:"".concat(c,"/workflow-execute/execute"),APPROVE:"".concat(c,"/workflow-execute/approve"),STREAM:"".concat(c,"/workflow-execute/stream")}}}},e=>{var t=t=>e(e.s=t);e.O(0,[9352,3291,6874,7997,8441,1684,7358],()=>t(19274)),_N_E=e.O()}]);