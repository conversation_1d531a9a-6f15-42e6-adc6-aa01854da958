<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/940018d4805e35d7.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/886f7af331f6427c.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-c24ca988b8c86a1d.js"/><script src="/_next/static/chunks/4bd1b696-35d63e82222b9a20.js" async=""></script><script src="/_next/static/chunks/1684-c6324801e8a4e70b.js" async=""></script><script src="/_next/static/chunks/main-app-adb8bed3920ae4b0.js" async=""></script><script src="/_next/static/chunks/3291-50bd181800545647.js" async=""></script><script src="/_next/static/chunks/6671-a8c26f670d082532.js" async=""></script><script src="/_next/static/chunks/4766-7031d698975a3144.js" async=""></script><script src="/_next/static/chunks/4720-b1b7051d8a12e717.js" async=""></script><script src="/_next/static/chunks/7997-275fa46732e6b474.js" async=""></script><script src="/_next/static/chunks/app/layout-91ca3da93030ddbf.js" async=""></script><script src="/_next/static/chunks/c37d3baf-9aa588d5c888329e.js" async=""></script><script src="/_next/static/chunks/9352-7a620227c8bd8866.js" async=""></script><script src="/_next/static/chunks/6874-fb0c1de9033b59b3.js" async=""></script><script src="/_next/static/chunks/8517-831b5b845916db51.js" async=""></script><script src="/_next/static/chunks/8095-94d0b070cc35e144.js" async=""></script><script src="/_next/static/chunks/2740-6a5fac3b046ec2bc.js" async=""></script><script src="/_next/static/chunks/1822-e31b1dec02db54f2.js" async=""></script><script src="/_next/static/chunks/7764-5a5c577cb8574a2a.js" async=""></script><script src="/_next/static/chunks/7907-9f4e62eee9a19a6a.js" async=""></script><script src="/_next/static/chunks/9527-ed4ee7bf68ccbf98.js" async=""></script><script src="/_next/static/chunks/app/page-d68632df459da7fb.js" async=""></script><title>Workflow Builder</title><meta name="description" content="Visual workflow builder for automation"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><link rel="icon" href="/favicon.svg" type="image/svg+xml"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 __variable_fb6e91 __variable_3a3c2d antialiased"><script>((e,t,n,r,a,i,o,s)=>{let c=document.documentElement,u=["light","dark"];function l(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&i?a.map(e=>i[e]||e):a;n?(c.classList.remove(...r),c.classList.add(i&&i[t]?i[t]:t)):c.setAttribute(e,t)}),n=t,s&&u.includes(n)&&(c.style.colorScheme=n)}if(r)l(r);else try{let e=localStorage.getItem(t)||n,r=o&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;l(r)}catch(e){}})("class","theme","system",null,["light","dark"],null,true,true)</script><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><main class="relative flex h-screen w-screen flex-col overflow-hidden "><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><div>Loading workflow...</div><!--/$--><div class="bg-card/30 flex h-16 shrink-0 items-center justify-between border-b px-4 py-2 shadow-sm backdrop-blur-sm flex-shrink-0"><div class="flex items-center gap-3"><a class="mr-2 flex items-center" href="/workflows"><img alt="Workflow Builder Logo" loading="lazy" width="120" height="30" decoding="async" data-nimg="1" class="h-8 w-auto" style="color:transparent" src="/wflogo_white.svg"/></a><h1 class="hover:text-primary cursor-pointer text-lg font-semibold transition-colors" title="Click to edit workflow title">Untitled Workflow</h1><div data-orientation="vertical" role="none" data-slot="separator-root" class="bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px h-8"></div><button type="button" class="bg-secondary text-secondary-foreground hover:bg-secondary/80 inline-flex h-8 items-center justify-center gap-1.5 rounded-md px-3 py-2 text-sm font-medium shadow-xs" title="Save workflow (Ctrl+S)"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-save h-4 w-4"><path d="M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z"></path><path d="M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7"></path><path d="M7 3v4a1 1 0 0 0 1 1h7"></path></svg> Save</button><button type="button" class="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-8 items-center justify-center gap-1.5 rounded-md border px-3 py-2 text-sm font-medium shadow-xs" title="Load existing workflow">Load <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-up-down h-4 w-4"><path d="m7 15 5 5 5-5"></path><path d="m7 9 5-5 5 5"></path></svg></button></div><div class="flex items-center gap-2"><div data-orientation="vertical" role="none" data-slot="separator-root" class="bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px mx-1 h-8"></div><a class="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium" title="Manage Credentials" href="/credentials"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-key h-4 w-4"><path d="m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4"></path><path d="m21 2-9.6 9.6"></path><circle cx="7.5" cy="15.5" r="5.5"></circle></svg></a><button type="button" class="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium" title="Toggle theme"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon h-4 w-4"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg></button><button type="button" class="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium" title="Help"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-help h-4 w-4"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg></button><div data-orientation="vertical" role="none" data-slot="separator-root" class="bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px mx-1 h-8"></div><button type="button" class="inline-flex h-8 items-center justify-center gap-1.5 rounded-md border border-yellow-500/30 bg-yellow-500/10 px-3 py-2 text-sm font-medium text-yellow-700 hover:bg-yellow-500/20 dark:text-yellow-400" data-state="closed" data-slot="tooltip-trigger"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check h-4 w-4"><circle cx="12" cy="12" r="10"></circle><path d="m9 12 2 2 4-4"></path></svg> Validate Workflow</button><button type="button" class="inline-flex h-8 items-center justify-center gap-1.5 rounded-md bg-green-600 px-3 py-2 text-sm font-medium text-white hover:bg-green-700 disabled:pointer-events-none disabled:opacity-50" disabled="">Run</button><div data-orientation="vertical" role="none" data-slot="separator-root" class="bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px mx-1 h-8"></div><button data-slot="button" class="focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg:not([class*=&#x27;size-&#x27;])]:size-4 bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs h-8 gap-1.5 rounded-md px-3 has-[&gt;svg]:px-2.5">Log in</button></div></div><div class="flex flex-grow overflow-hidden"><aside class="bg-sidebar border-brand-stroke relative flex h-full shrink-0 flex-col overflow-hidden border-r shadow-md transition-all duration-300 w-80"><div class="pointer-events-none absolute inset-0 z-0 bg-black/5 dark:bg-black/20"></div><div class="relative z-10 flex-shrink-0 bg-[#FEFEFE] dark:bg-black p-5"><div class="relative flex items-center"><div class="relative flex-grow"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search text-brand-secondary absolute top-3 left-3 h-5 w-5"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><input data-slot="input" class="file:text-foreground selection:bg-primary selection:text-primary-foreground flex w-full min-w-0 border px-3 py-1 shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border-brand-stroke text-brand-primary-font placeholder:text-brand-secondary-font focus-visible:ring-brand-primary/30 dark:text-brand-white-text dark:placeholder:text-brand-secondary-font h-11 rounded-md bg-white/90 pl-10 text-sm dark:border-[#3F3F46] dark:bg-[#18181B]" placeholder="Search components..." value=""/></div><button class="text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary ml-2 flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]" aria-label="Collapse sidebar" title="Collapse sidebar (Alt+S)"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15 6L9 12L15 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div></div><div class="custom-scrollbar overflow-auto custom-scrollbar relative z-10 flex-grow bg-[#FEFEFE] dark:bg-black"><div data-slot="accordion" class="w-full space-y-3 px-5 py-4" data-orientation="vertical"></div><div class="border-brand-stroke bg-brand-card-hover text-brand-secondary-font dark:border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font m-4 rounded-lg border p-6 text-center text-sm"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search text-brand-primary/70 dark:text-brand-secondary/70 mx-auto mb-3 h-6 w-6"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>No components match your search.</div></div></aside><div class="bg-background/50 flex flex-grow flex-col overflow-hidden backdrop-blur-sm"><div class="flex flex-grow items-center justify-center"><div class="flex animate-pulse flex-col items-center"><div class="bg-brand-primary/20 mb-4 h-12 w-12 rounded-full"></div><div class="bg-brand-primary/20 h-4 w-48 rounded"></div><div class="font-secondary text-brand-secondary-font mt-2 text-sm">Loading components...</div></div></div></div></div></main><script src="/_next/static/chunks/webpack-c24ca988b8c86a1d.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[77890,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"ThemeProvider\"]\n3:I[70139,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"QueryProvider\"]\n4:I[94819,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"AuthProvider\"]\n5:I[42618,[\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6671\",\"static/chunks/6671-a8c26f670d082532.js\",\"4766\",\"static/chunks/4766-7031d698975a3144.js\",\"4720\",\"static/chunks/4720-b1b7051d8a12e717.js\",\"7997\",\"static/chunks/7997-275fa46732e6b474.js\",\"7177\",\"static/chunks/app/layout-91ca3da93030ddbf.js\"],\"ToastProvider\"]\n6:I[87555,[],\"\"]\n7:I[31295,[],\"\"]\n8:I[90894,[],\"ClientPageRoot\"]\n9:I[12415,[\"8702\",\"static/chunks/c37d3baf-9aa588d5c888329e.js\",\"9352\",\"static/chunks/9352-7a620227c8bd8866.js\",\"3291\",\"static/chunks/3291-50bd181800545647.js\",\"6874\",\"static/chunks/6874-fb0c1de9033b59b3.js\",\"8517\",\"static/chunks/8517-831b5b845916db51.js\",\"8095\",\"static/chunks/8095-94d0b070cc35e144.js\",\"2740\",\"static/chunks/2740-6a5fac3b046ec2bc.js\",\"1822\",\"static/chunks/1822-e31b1dec02db54f2.js\",\"7764\",\"static/chunks/7764-5a5c577cb8574a2a.js\",\"7907\",\"static/chunks/7907-9f4e62eee9a19a6a.js\",\"9527\",\"static/chunks/9527-ed4ee7bf68ccbf98.js\",\"8974\",\"static/chunks/app/page-d68632df459da7fb.js\"],\"default\"]\nc:I[59665,[],\"OutletBoundary\"]\nf:I[59665,[],\"ViewportBoundary\"]\n"])</script><script>self.__next_f.push([1,"11:I[59665,[],\"MetadataBoundary\"]\n13:I[26614,[],\"\"]\n:HL[\"/_next/static/css/940018d4805e35d7.css\",\"style\"]\n:HL[\"/_next/static/css/886f7af331f6427c.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"1QAyynrQ8SZ0fgEBA9EXT\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/940018d4805e35d7.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 __variable_fb6e91 __variable_3a3c2d antialiased\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[[\"$\",\"$L5\",null,{}],[\"$\",\"$L6\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L7\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]}]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L8\",null,{\"Component\":\"$9\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@a\",\"$@b\"]}],\"$undefined\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/886f7af331f6427c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$Lc\",null,{\"children\":[\"$Ld\",\"$Le\",null]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"MBnjYuSkIvguIT68-SZJX\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null]}],[\"$\",\"$L11\",null,{\"children\":\"$L12\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$13\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"a:{}\nb:{}\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nd:null\n"])</script><script>self.__next_f.push([1,"e:null\n12:[[\"$\",\"title\",\"0\",{\"children\":\"Workflow Builder\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Visual workflow builder for automation\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.svg\",\"type\":\"image/svg+xml\"}]]\n"])</script></body></html>