"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8095],{2564:(e,t,n)=>{n.d(t,{b:()=>a,s:()=>l});var r=n(12115),o=n(63655),i=n(95155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));l.displayName="VisuallyHidden";var a=l},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(12115),o=n(39033);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,l]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,l=r.useRef(i),a=(0,o.c)(t);return r.useEffect(()=>{l.current!==i&&(a(i),l.current=i)},[i,l,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,u=a?e:i,c=(0,o.c)(n);return[u,r.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&c(n)}else l(t)},[a,e,l,c])]}},11275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(12115),o=n(52712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},22475:(e,t,n)=>{n.d(t,{UE:()=>ef,ll:()=>el,rD:()=>ev,UU:()=>ec,jD:()=>ed,ER:()=>ep,cY:()=>ea,BN:()=>eu,Ej:()=>es});let r=["top","right","bottom","left"],o=Math.min,i=Math.max,l=Math.round,a=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}function m(e){return["top","bottom"].includes(f(e))?"y":"x"}function g(e){return e.replace(/start|end/g,e=>s[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function b(e,t,n){let r,{reference:o,floating:i}=e,l=m(t),a=v(m(t)),u=h(a),c=f(t),s="y"===l,d=o.x+o.width/2-i.width/2,g=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:g};break;case"left":r={x:o.x-i.width,y:g};break;default:r={x:o.x,y:o.y}}switch(p(t)){case"start":r[a]-=y*(n&&s?-1:1);break;case"end":r[a]+=y*(n&&s?-1:1)}return r}let E=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=b(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=b(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:v=0}=d(t,e),h=w(v),m=a[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),E=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},S=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:b,strategy:u}):y);return{top:(g.top-S.top+h.top)/E.y,bottom:(S.bottom-g.bottom+h.bottom)/E.y,left:(g.left-S.left+h.left)/E.x,right:(S.right-g.right+h.right)/E.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function R(e){return r.some(t=>e[t]>=0)}async function A(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=f(n),a=p(n),u="y"===m(n),c=["left","top"].includes(l)?-1:1,s=i&&u?-1:1,v=d(t,e),{mainAxis:h,crossAxis:g,alignmentAxis:y}="number"==typeof v?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return a&&"number"==typeof y&&(g="end"===a?-+y:y),u?{x:g*s,y:h*c}:{x:h*c,y:g*s}}function T(){return"undefined"!=typeof window}function L(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!T()&&(e instanceof Node||e instanceof P(e).Node)}function j(e){return!!T()&&(e instanceof Element||e instanceof P(e).Element)}function M(e){return!!T()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function D(e){return!!T()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=F(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=W(),n=j(e)?F(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function H(e){return["html","body","#document"].includes(L(e))}function F(e){return P(e).getComputedStyle(e)}function _(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function U(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||k(e);return D(t)?t.host:t}function z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=U(t);return H(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&O(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=P(o);if(i){let e=V(l);return t.concat(l,l.visualViewport||[],O(o)?o:[],e&&n?z(e):[])}return t.concat(o,z(o,[],n))}function V(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=F(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=M(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function G(e){return j(e)?e:e.contextElement}function q(e){let t=G(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),a=(i?l(n.width):n.width)/r,c=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),c&&Number.isFinite(c)||(c=1),{x:a,y:c}}let Y=u(0);function X(e){let t=P(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function $(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=G(e),a=u(1);t&&(r?j(r)&&(a=q(r)):a=q(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===P(l))&&o)?X(l):u(0),s=(i.left+c.x)/a.x,d=(i.top+c.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=P(l),t=r&&j(r)?P(r):r,n=e,o=V(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=F(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=l,o=V(n=P(o))}}return x({width:f,height:p,x:s,y:d})}function Z(e,t){let n=_(e).scrollLeft;return t?t.left+n:$(k(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Z(e,r)),y:r.top+t.scrollTop}}function Q(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=k(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=W();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=_(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Z(e),u=-n.scrollTop;return"rtl"===F(r).direction&&(a+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:l,x:a,y:u}}(k(e));else if(j(t))r=function(e,t){let n=$(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=M(e)?q(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=X(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function ee(e){return"static"===F(e).position}function et(e,t){if(!M(e)||"fixed"===F(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=P(e);if(I(e))return n;if(!M(e)){let t=U(e);for(;t&&!H(t);){if(j(t)&&!ee(t))return t;t=U(t)}return n}let r=et(e,t);for(;r&&["table","td","th"].includes(L(r))&&ee(r);)r=et(r,t);return r&&H(r)&&ee(r)&&!B(r)?n:r||function(e){let t=U(e);for(;M(t)&&!H(t);){if(B(t))return t;if(I(t))break;t=U(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),o=k(t),i="fixed"===n,l=$(e,!0,i,t),a={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!i)if(("body"!==L(t)||O(o))&&(a=_(t)),r){let e=$(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=Z(o));let s=!o||r||i?u(0):J(o,a);return{x:l.left+a.scrollLeft-c.x-s.x,y:l.top+a.scrollTop-c.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=k(r),a=!!t&&I(t.floating);if(r===l||a&&i)return n;let c={scrollLeft:0,scrollTop:0},s=u(1),d=u(0),f=M(r);if((f||!f&&!i)&&(("body"!==L(r)||O(l))&&(c=_(r)),M(r))){let e=$(r);s=q(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!l||f||i?u(0):J(l,c,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-c.scrollTop*s.y+d.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:l}=e,a=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=z(e,[],!1).filter(e=>j(e)&&"body"!==L(e)),o=null,i="fixed"===F(e).position,l=i?U(e):e;for(;j(l)&&!H(l);){let t=F(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||O(l)&&!n&&function e(t,n){let r=U(t);return!(r===n||!j(r)||H(r))&&("fixed"===F(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=U(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=a[0],c=a.reduce((e,n)=>{let r=Q(t,n,l);return e.top=i(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=i(r.left,e.left),e},Q(t,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:en,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:q,isElement:j,isRTL:function(e){return"rtl"===F(e).direction}};function ei(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function el(e,t,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=G(e),v=u||c?[...p?z(p):[],...z(t)]:[];v.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=p&&d?function(e,t){let n,r=null,l=k(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),u();let f=e.getBoundingClientRect(),{left:p,top:v,width:h,height:m}=f;if(s||t(),!h||!m)return;let g=a(v),y=a(l.clientWidth-(p+h)),w={rootMargin:-g+"px "+-y+"px "+-a(l.clientHeight-(v+m))+"px "+-a(p)+"px",threshold:i(0,o(1,d))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==d){if(!x)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ei(f,e.getBoundingClientRect())||c(),x=!1}try{r=new IntersectionObserver(b,{...w,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),u}(p,n):null,m=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?$(e):null;return f&&function t(){let r=$(e);y&&!ei(y,r)&&n(),y=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;v.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(l)}}let ea=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await A(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}},eu=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:l}=t,{mainAxis:a=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),p={x:n,y:r},h=await S(t,s),g=m(f(l)),y=v(g),w=p[y],x=p[g];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=i(n,o(w,r))}if(u){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=i(n,o(x,r))}let b=c.fn({...t,[y]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[g]:u}}}}}},ec=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:w,elements:x}=t,{mainAxis:b=!0,crossAxis:E=!0,fallbackPlacements:C,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:A="none",flipAlignment:T=!0,...L}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let P=f(a),k=m(s),N=f(s)===s,j=await (null==w.isRTL?void 0:w.isRTL(x.floating)),M=C||(N||!T?[y(s)]:function(e){let t=y(e);return[g(e),t,g(t)]}(s)),D="none"!==A;!C&&D&&M.push(...function(e,t,n,r){let o=p(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(f(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(g)))),i}(s,T,A,j));let O=[s,...M],I=await S(t,L),B=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&B.push(I[P]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),o=v(m(e)),i=h(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=y(l)),[l,y(l)]}(a,c,j);B.push(I[e[0]],I[e[1]])}if(W=[...W,{placement:a,overflows:B}],!B.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=O[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(R){case"bestFit":{let e=null==(l=W.filter(e=>{if(D){let t=m(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}},es=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let l,a,{placement:u,rects:c,platform:s,elements:v}=t,{apply:h=()=>{},...g}=d(e,t),y=await S(t,g),w=f(u),x=p(u),b="y"===m(u),{width:E,height:C}=c.floating;"top"===w||"bottom"===w?(l=w,a=x===(await (null==s.isRTL?void 0:s.isRTL(v.floating))?"start":"end")?"left":"right"):(a=w,l="end"===x?"top":"bottom");let R=C-y.top-y.bottom,A=E-y.left-y.right,T=o(C-y[l],R),L=o(E-y[a],A),P=!t.middlewareData.shift,k=T,N=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(N=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=R),P&&!x){let e=i(y.left,0),t=i(y.right,0),n=i(y.top,0),r=i(y.bottom,0);b?N=E-2*(0!==e||0!==t?e+t:i(y.left,y.right)):k=C-2*(0!==n||0!==r?n+r:i(y.top,y.bottom))}await h({...t,availableWidth:N,availableHeight:k});let j=await s.getDimensions(v.floating);return E!==j.width||C!==j.height?{reset:{rects:!0}}:{}}}},ed=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=C(await S(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:R(e)}}}case"escaped":{let e=C(await S(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:R(e)}}}default:return{}}}}},ef=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:l,rects:a,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:g=0}=d(e,t)||{};if(null==f)return{};let y=w(g),x={x:n,y:r},b=v(m(l)),E=h(b),S=await u.getDimensions(f),C="y"===b,R=C?"clientHeight":"clientWidth",A=a.reference[E]+a.reference[b]-x[b]-a.floating[E],T=x[b]-a.reference[b],L=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),P=L?L[R]:0;P&&await (null==u.isElement?void 0:u.isElement(L))||(P=c.floating[R]||a.floating[E]);let k=P/2-S[E]/2-1,N=o(y[C?"top":"left"],k),j=o(y[C?"bottom":"right"],k),M=P-S[E]-j,D=P/2-S[E]/2+(A/2-T/2),O=i(N,o(D,M)),I=!s.arrow&&null!=p(l)&&D!==O&&a.reference[E]/2-(D<N?N:j)-S[E]/2<0,B=I?D<N?D-N:D-M:0;return{[b]:x[b]+B,data:{[b]:O,centerOffset:D-O-B,...I&&{alignmentOffset:B}},reset:I}}}),ep=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},p=m(o),h=v(p),g=s[h],y=s[p],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(c){var b,E;let e="y"===h?"width":"height",t=["top","left"].includes(f(o)),n=i.reference[p]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[p])||0)+(t?0:x.crossAxis),r=i.reference[p]+i.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[p])||0)-(t?x.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:g,[p]:y}}}},ev=(e,t,n)=>{let r=new Map,o={platform:eo,...n},i={...o.platform,_c:r};return E(e,t,{...o,platform:i})}},33897:(e,t,n)=>{n.d(t,{UC:()=>eF,In:()=>eW,q7:()=>eU,VF:()=>eV,p4:()=>ez,ZL:()=>eH,bL:()=>eO,wn:()=>eG,PP:()=>eK,l9:()=>eI,WT:()=>eB,LM:()=>e_});var r=n(12115),o=n(47650),i=n(89367),l=n(85185),a=n(82284),u=n(6101),c=n(46081),s=n(94315),d=n(58434),f=n(92293),p=n(63655),v=n(39033),h=n(95155),m="focusScope.autoFocusOnMount",g="focusScope.autoFocusOnUnmount",y={bubbles:!1,cancelable:!0},w=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...a}=e,[c,s]=r.useState(null),d=(0,v.c)(i),f=(0,v.c)(l),w=r.useRef(null),C=(0,u.s)(t,e=>s(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(o){let e=function(e){if(R.paused||!c)return;let t=e.target;c.contains(t)?w.current=t:E(w.current,{select:!0})},t=function(e){if(R.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||E(w.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&E(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[o,c,R.paused]),r.useEffect(()=>{if(c){S.add(R);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(m,y);c.addEventListener(m,d),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(E(r,{select:t}),document.activeElement!==n)return}(x(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&E(c))}return()=>{c.removeEventListener(m,d),setTimeout(()=>{let t=new CustomEvent(g,y);c.addEventListener(g,f),c.dispatchEvent(t),t.defaultPrevented||E(null!=e?e:document.body,{select:!0}),c.removeEventListener(g,f),S.remove(R)},0)}}},[c,d,f,R]);let A=r.useCallback(e=>{if(!n&&!o||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=x(e);return[b(t,e),b(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&E(i,{select:!0})):(e.preventDefault(),n&&E(o,{select:!0})):r===t&&e.preventDefault()}},[n,o,R.paused]);return(0,h.jsx)(p.sG.div,{tabIndex:-1,...a,ref:C,onKeyDown:A})});function x(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function b(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function E(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}w.displayName="FocusScope";var S=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=C(e,t)).unshift(t)},remove(t){var n;null===(n=(e=C(e,t))[0])||void 0===n||n.resume()}}}();function C(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var R=n(61285),A=n(63753),T=n(34378),L=n(99708),P=n(5845),k=n(52712),N=n(45503),j=n(2564),M=n(38168),D=n(93795),O=[" ","Enter","ArrowUp","ArrowDown"],I=[" ","Enter"],B="Select",[W,H,F]=(0,a.N)(B),[_,U]=(0,c.A)(B,[F,A.Bk]),z=(0,A.Bk)(),[V,K]=_(B),[G,q]=_(B),Y=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:v,required:m,form:g}=e,y=z(t),[w,x]=r.useState(null),[b,E]=r.useState(null),[S,C]=r.useState(!1),T=(0,s.jH)(d),[L=!1,k]=(0,P.i)({prop:o,defaultProp:i,onChange:l}),[N,j]=(0,P.i)({prop:a,defaultProp:u,onChange:c}),M=r.useRef(null),D=!w||g||!!w.closest("form"),[O,I]=r.useState(new Set),B=Array.from(O).map(e=>e.props.value).join(";");return(0,h.jsx)(A.bL,{...y,children:(0,h.jsxs)(V,{required:m,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,R.B)(),value:N,onValueChange:j,open:L,onOpenChange:k,dir:T,triggerPointerDownPosRef:M,disabled:v,children:[(0,h.jsx)(W.Provider,{scope:t,children:(0,h.jsx)(G,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{I(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{I(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),D?(0,h.jsxs)(ej,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>j(e.target.value),disabled:v,form:g,children:[void 0===N?(0,h.jsx)("option",{value:""}):null,Array.from(O)]},B):null]})})};Y.displayName=B;var X="SelectTrigger",$=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=z(n),c=K(X,n),s=c.disabled||o,d=(0,u.s)(t,c.onTriggerChange),f=H(n),v=r.useRef("touch"),[m,g,y]=eM(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=eD(t,e,n);void 0!==r&&c.onValueChange(r.value)}),w=e=>{s||(c.onOpenChange(!0),y()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,h.jsx)(A.Mz,{asChild:!0,...a,children:(0,h.jsx)(p.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":eN(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==v.current&&w(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{v.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&O.includes(e.key)&&(w(),e.preventDefault())})})})});$.displayName=X;var Z="SelectValue",J=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=K(Z,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.s)(t,c.onValueNodeChange);return(0,k.N)(()=>{s(d)},[s,d]),(0,h.jsx)(p.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eN(c.value)?(0,h.jsx)(h.Fragment,{children:l}):i})});J.displayName=Z;var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,h.jsx)(p.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});Q.displayName="SelectIcon";var ee=e=>(0,h.jsx)(T.Z,{asChild:!0,...e});ee.displayName="SelectPortal";var et="SelectContent",en=r.forwardRef((e,t)=>{let n=K(et,e.__scopeSelect),[i,l]=r.useState();return((0,k.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,h.jsx)(el,{...e,ref:t}):i?o.createPortal((0,h.jsx)(er,{scope:e.__scopeSelect,children:(0,h.jsx)(W.Slot,{scope:e.__scopeSelect,children:(0,h.jsx)("div",{children:e.children})})}),i):null});en.displayName=et;var[er,eo]=_(et),ei=(0,L.TL)("SelectContent.RemoveScroll"),el=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:p,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:E,avoidCollisions:S,...C}=e,R=K(et,n),[A,T]=r.useState(null),[L,P]=r.useState(null),k=(0,u.s)(t,e=>T(e)),[N,j]=r.useState(null),[O,I]=r.useState(null),B=H(n),[W,F]=r.useState(!1),_=r.useRef(!1);r.useEffect(()=>{if(A)return(0,M.Eq)(A)},[A]),(0,f.Oh)();let U=r.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&L&&(L.scrollTop=0),n===r&&L&&(L.scrollTop=L.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[B,L]),z=r.useCallback(()=>U([N,A]),[U,N,A]);r.useEffect(()=>{W&&z()},[W,z]);let{onOpenChange:V,triggerPointerDownPosRef:G}=R;r.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=G.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=G.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,V,G]),r.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[q,Y]=eM(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eD(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),X=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==R.value&&R.value===t||r)&&(j(e),r&&(_.current=!0))},[R.value]),$=r.useCallback(()=>null==A?void 0:A.focus(),[A]),Z=r.useCallback((e,t,n)=>{let r=!_.current&&!n;(void 0!==R.value&&R.value===t||r)&&I(e)},[R.value]),J="popper"===o?eu:ea,Q=J===eu?{side:s,sideOffset:p,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:E,avoidCollisions:S}:{};return(0,h.jsx)(er,{scope:n,content:A,viewport:L,onViewportChange:P,itemRefCallback:X,selectedItem:N,onItemLeave:$,itemTextRefCallback:Z,focusSelectedItem:z,selectedItemText:O,position:o,isPositioned:W,searchRef:q,children:(0,h.jsx)(D.A,{as:ei,allowPinchZoom:!0,children:(0,h.jsx)(w,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null===(t=R.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,h.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,h.jsx)(J,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...Q,onPlaced:()=>F(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,l.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});el.displayName="SelectContentImpl";var ea=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=K(et,n),c=eo(et,n),[s,d]=r.useState(null),[f,v]=r.useState(null),m=(0,u.s)(t,e=>v(e)),g=H(n),y=r.useRef(!1),w=r.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:S}=c,C=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&x&&b&&E){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=E.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.left=f+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.right=f+"px"}let l=g(),u=window.innerHeight-20,c=x.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),h=parseInt(d.borderBottomWidth,10),m=p+v+c+parseInt(d.paddingBottom,10)+h,w=Math.min(5*b.offsetHeight,m),S=window.getComputedStyle(x),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),A=e.top+e.height/2-10,T=b.offsetHeight/2,L=p+v+(b.offsetTop+T);if(L<=A){let e=l.length>0&&b===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-A,T+(e?R:0)+(f.clientHeight-x.offsetTop-x.offsetHeight)+h);s.style.height=L+t+"px"}else{let e=l.length>0&&b===l[0].ref.current;s.style.top="0px";let t=Math.max(A,p+x.offsetTop+(e?C:0)+T);s.style.height=t+(m-L)+"px",x.scrollTop=L-A+x.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=w+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>y.current=!0)}},[g,a.trigger,a.valueNode,s,f,x,b,E,a.dir,o]);(0,k.N)(()=>C(),[C]);let[R,A]=r.useState();(0,k.N)(()=>{f&&A(window.getComputedStyle(f).zIndex)},[f]);let T=r.useCallback(e=>{e&&!0===w.current&&(C(),null==S||S(),w.current=!1)},[C,S]);return(0,h.jsx)(ec,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:y,onScrollButtonChange:T,children:(0,h.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,h.jsx)(p.sG.div,{...l,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});ea.displayName="SelectItemAlignedPosition";var eu=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=z(n);return(0,h.jsx)(A.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eu.displayName="SelectPopperPosition";var[ec,es]=_(et,{}),ed="SelectViewport",ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=eo(ed,n),c=es(ed,n),s=(0,u.s)(t,a.onViewportChange),d=r.useRef(0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,h.jsx)(W.Slot,{scope:n,children:(0,h.jsx)(p.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});ef.displayName=ed;var ep="SelectGroup",[ev,eh]=_(ep);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,R.B)();return(0,h.jsx)(ev,{scope:n,id:o,children:(0,h.jsx)(p.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})}).displayName=ep;var em="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=eh(em,n);return(0,h.jsx)(p.sG.div,{id:o.id,...r,ref:t})}).displayName=em;var eg="SelectItem",[ey,ew]=_(eg),ex=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=K(eg,n),d=eo(eg,n),f=s.value===o,[v,m]=r.useState(null!=a?a:""),[g,y]=r.useState(!1),w=(0,u.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),x=(0,R.B)(),b=r.useRef("touch"),E=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,h.jsx)(ey,{scope:n,value:o,disabled:i,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,h.jsx)(W.ItemSlot,{scope:n,value:o,disabled:i,textValue:v,children:(0,h.jsx)(p.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":f&&g,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:w,onFocus:(0,l.m)(c.onFocus,()=>y(!0)),onBlur:(0,l.m)(c.onBlur,()=>y(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(I.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});ex.displayName=eg;var eb="SelectItemText",eE=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=K(eb,n),s=eo(eb,n),d=ew(eb,n),f=q(eb,n),[v,m]=r.useState(null),g=(0,u.s)(t,e=>m(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),y=null==v?void 0:v.textContent,w=r.useMemo(()=>(0,h.jsx)("option",{value:d.value,disabled:d.disabled,children:y},d.value),[d.disabled,d.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=f;return(0,k.N)(()=>(x(w),()=>b(w)),[x,b,w]),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(p.sG.span,{id:d.textId,...a,ref:g}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});eE.displayName=eb;var eS="SelectItemIndicator",eC=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ew(eS,n).isSelected?(0,h.jsx)(p.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eC.displayName=eS;var eR="SelectScrollUpButton",eA=r.forwardRef((e,t)=>{let n=eo(eR,e.__scopeSelect),o=es(eR,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,k.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,h.jsx)(eP,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eA.displayName=eR;var eT="SelectScrollDownButton",eL=r.forwardRef((e,t)=>{let n=eo(eT,e.__scopeSelect),o=es(eT,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,k.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,h.jsx)(eP,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eL.displayName=eT;var eP=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=eo("SelectScrollButton",n),u=r.useRef(null),c=H(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,k.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,h.jsx)(p.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{s()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,h.jsx)(p.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var ek="SelectArrow";function eN(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=z(n),i=K(ek,n),l=eo(ek,n);return i.open&&"popper"===l.position?(0,h.jsx)(A.i3,{...o,...r,ref:t}):null}).displayName=ek;var ej=r.forwardRef((e,t)=>{let{value:n,...o}=e,i=r.useRef(null),l=(0,u.s)(t,i),a=(0,N.Z)(n);return r.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,h.jsx)(j.s,{asChild:!0,children:(0,h.jsx)("select",{...o,ref:l,defaultValue:n})})});function eM(e){let t=(0,v.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function eD(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}ej.displayName="BubbleSelect";var eO=Y,eI=$,eB=J,eW=Q,eH=ee,eF=en,e_=ef,eU=ex,ez=eE,eV=eC,eK=eA,eG=eL},34378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(12115),o=n(47650),i=n(63655),l=n(52712),a=n(95155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=c||d&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),v=function(e){!e||f.has(e)||(f.add(e),v(e.parentNode))};c.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),c(o,i,n,"aria-hidden")):function(){return null}}},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46081:(e,t,n)=>{n.d(t,{A:()=>l,q:()=>i});var r=n(12115),o=n(95155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,l=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:l,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},51595:(e,t,n)=>{n.d(t,{U:()=>i});var r=n(12115),o=n(39033);function i(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},58434:(e,t,n)=>{n.d(t,{qW:()=>p});var r,o=n(12115),i=n(85185),l=n(63655),a=n(6101),u=n(39033),c=n(51595),s=n(95155),d="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=o.forwardRef((e,t)=>{var n,p;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:x,onDismiss:b,...E}=e,S=o.useContext(f),[C,R]=o.useState(null),A=null!==(p=null==C?void 0:C.ownerDocument)&&void 0!==p?p:null===(n=globalThis)||void 0===n?void 0:n.document,[,T]=o.useState({}),L=(0,a.s)(t,e=>R(e)),P=Array.from(S.layers),[k]=[...S.layersWithOutsidePointerEventsDisabled].slice(-1),N=P.indexOf(k),j=C?P.indexOf(C):-1,M=S.layersWithOutsidePointerEventsDisabled.size>0,D=j>=N,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...S.branches].some(e=>e.contains(t));!D||n||(null==y||y(e),null==x||x(e),e.defaultPrevented||null==b||b())},A),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...S.branches].some(e=>e.contains(t))||(null==w||w(e),null==x||x(e),e.defaultPrevented||null==b||b())},A);return(0,c.U)(e=>{j===S.layers.size-1&&(null==g||g(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},A),o.useEffect(()=>{if(C)return m&&(0===S.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),S.layersWithOutsidePointerEventsDisabled.add(C)),S.layers.add(C),v(),()=>{m&&1===S.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[C,A,m,S]),o.useEffect(()=>()=>{C&&(S.layers.delete(C),S.layersWithOutsidePointerEventsDisabled.delete(C),v())},[C,S]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,s.jsx)(l.sG.div,{...E,ref:L,style:{pointerEvents:M?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function v(){let e=new CustomEvent(d);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},61285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(12115),i=n(52712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function u(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},63753:(e,t,n)=>{n.d(t,{Mz:()=>M,i3:()=>O,UC:()=>D,bL:()=>j,Bk:()=>m});var r=n(12115),o=n(84945),i=n(22475),l=n(63655),a=n(95155),u=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,a.jsx)(l.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});u.displayName="Arrow";var c=n(6101),s=n(46081),d=n(39033),f=n(52712),p=n(11275),v="Popper",[h,m]=(0,s.A)(v),[g,y]=h(v),w=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,a.jsx)(g,{scope:t,anchor:o,onAnchorChange:i,children:n})};w.displayName=v;var x="PopperAnchor",b=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,u=y(x,n),s=r.useRef(null),d=(0,c.s)(t,s);return r.useEffect(()=>{u.onAnchorChange((null==o?void 0:o.current)||s.current)}),o?null:(0,a.jsx)(l.sG.div,{...i,ref:d})});b.displayName=x;var E="PopperContent",[S,C]=h(E),R=r.forwardRef((e,t)=>{var n,u,s,v,h,m,g,w;let{__scopePopper:x,side:b="bottom",sideOffset:C=0,align:R="center",alignOffset:A=0,arrowPadding:T=0,avoidCollisions:L=!0,collisionBoundary:j=[],collisionPadding:M=0,sticky:D="partial",hideWhenDetached:O=!1,updatePositionStrategy:I="optimized",onPlaced:B,...W}=e,H=y(E,x),[F,_]=r.useState(null),U=(0,c.s)(t,e=>_(e)),[z,V]=r.useState(null),K=(0,p.X)(z),G=null!==(g=null==K?void 0:K.width)&&void 0!==g?g:0,q=null!==(w=null==K?void 0:K.height)&&void 0!==w?w:0,Y="number"==typeof M?M:{top:0,right:0,bottom:0,left:0,...M},X=Array.isArray(j)?j:[j],$=X.length>0,Z={padding:Y,boundary:X.filter(P),altBoundary:$},{refs:J,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:en}=(0,o.we)({strategy:"fixed",placement:b+("center"!==R?"-"+R:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.ll)(...t,{animationFrame:"always"===I})},elements:{reference:H.anchor},middleware:[(0,o.cY)({mainAxis:C+q,alignmentAxis:A}),L&&(0,o.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===D?(0,o.ER)():void 0,...Z}),L&&(0,o.UU)({...Z}),(0,o.Ej)({...Z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),z&&(0,o.UE)({element:z,padding:T}),k({arrowWidth:G,arrowHeight:q}),O&&(0,o.jD)({strategy:"referenceHidden",...Z})]}),[er,eo]=N(ee),ei=(0,d.c)(B);(0,f.N)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null===(n=en.arrow)||void 0===n?void 0:n.x,ea=null===(u=en.arrow)||void 0===u?void 0:u.y,eu=(null===(s=en.arrow)||void 0===s?void 0:s.centerOffset)!==0,[ec,es]=r.useState();return(0,f.N)(()=>{F&&es(window.getComputedStyle(F).zIndex)},[F]),(0,a.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ec,"--radix-popper-transform-origin":[null===(v=en.transformOrigin)||void 0===v?void 0:v.x,null===(h=en.transformOrigin)||void 0===h?void 0:h.y].join(" "),...(null===(m=en.hide)||void 0===m?void 0:m.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(S,{scope:x,placedSide:er,onArrowChange:V,arrowX:el,arrowY:ea,shouldHideArrow:eu,children:(0,a.jsx)(l.sG.div,{"data-side":er,"data-align":eo,...W,ref:U,style:{...W.style,animation:et?void 0:"none"}})})})});R.displayName=E;var A="PopperArrow",T={top:"bottom",right:"left",bottom:"top",left:"right"},L=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=C(A,n),i=T[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(u,{...r,ref:t,style:{...r.style,display:"block"}})})});function P(e){return null!==e}L.displayName=A;var k=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=N(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function N(e){let[t,n="center"]=e.split("-");return[t,n]}var j=w,M=b,D=R,O=L},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},82284:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(12115),o=n(46081),i=n(6101),l=n(99708),a=n(95155);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,l.TL)(f),v=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(f,n),l=(0,i.s)(t,o.collectionRef);return(0,a.jsx)(p,{ref:l,children:r})});v.displayName=f;let h=e+"CollectionItemSlot",m="data-radix-collection-item",g=(0,l.TL)(h),y=r.forwardRef((e,t)=>{let{scope:n,children:o,...l}=e,u=r.useRef(null),c=(0,i.s)(t,u),d=s(h,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...l}),()=>void d.itemMap.delete(u))),(0,a.jsx)(g,{[m]:"",ref:c,children:o})});return y.displayName=h,[{Provider:d,Slot:v,ItemSlot:y},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},84945:(e,t,n)=>{n.d(t,{BN:()=>v,ER:()=>h,Ej:()=>g,UE:()=>w,UU:()=>m,cY:()=>p,jD:()=>y,we:()=>d});var r=n(22475),o=n(12115),i=n(47650),l="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function a(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function u(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function c(e,t){let n=u(e);return Math.round(t*n)/n}function s(e){let t=o.useRef(e);return l(()=>{t.current=e}),t}function d(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:d=[],platform:f,elements:{reference:p,floating:v}={},transform:h=!0,whileElementsMounted:m,open:g}=e,[y,w]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[x,b]=o.useState(d);a(x,d)||b(d);let[E,S]=o.useState(null),[C,R]=o.useState(null),A=o.useCallback(e=>{e!==k.current&&(k.current=e,S(e))},[]),T=o.useCallback(e=>{e!==N.current&&(N.current=e,R(e))},[]),L=p||E,P=v||C,k=o.useRef(null),N=o.useRef(null),j=o.useRef(y),M=null!=m,D=s(m),O=s(f),I=s(g),B=o.useCallback(()=>{if(!k.current||!N.current)return;let e={placement:t,strategy:n,middleware:x};O.current&&(e.platform=O.current),(0,r.rD)(k.current,N.current,e).then(e=>{let t={...e,isPositioned:!1!==I.current};W.current&&!a(j.current,t)&&(j.current=t,i.flushSync(()=>{w(t)}))})},[x,t,n,O,I]);l(()=>{!1===g&&j.current.isPositioned&&(j.current.isPositioned=!1,w(e=>({...e,isPositioned:!1})))},[g]);let W=o.useRef(!1);l(()=>(W.current=!0,()=>{W.current=!1}),[]),l(()=>{if(L&&(k.current=L),P&&(N.current=P),L&&P){if(D.current)return D.current(L,P,B);B()}},[L,P,B,D,M]);let H=o.useMemo(()=>({reference:k,floating:N,setReference:A,setFloating:T}),[A,T]),F=o.useMemo(()=>({reference:L,floating:P}),[L,P]),_=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!F.floating)return e;let t=c(F.floating,y.x),r=c(F.floating,y.y);return h?{...e,transform:"translate("+t+"px, "+r+"px)",...u(F.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,h,F.floating,y.x,y.y]);return o.useMemo(()=>({...y,update:B,refs:H,elements:F,floatingStyles:_}),[y,B,H,F,_])}let f=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:o}).fn(t):{}:n?(0,r.UE)({element:n,padding:o}).fn(t):{}}}),p=(e,t)=>({...(0,r.cY)(e),options:[e,t]}),v=(e,t)=>({...(0,r.BN)(e),options:[e,t]}),h=(e,t)=>({...(0,r.ER)(e),options:[e,t]}),m=(e,t)=>({...(0,r.UU)(e),options:[e,t]}),g=(e,t)=>({...(0,r.Ej)(e),options:[e,t]}),y=(e,t)=>({...(0,r.jD)(e),options:[e,t]}),w=(e,t)=>({...f(e),options:[e,t]})},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},85339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},89367:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,n)=>{n.d(t,{A:()=>K});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(12115)),a="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,l=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=o({async:!0,ssr:!1},e),l}(),v=function(){},h=l.forwardRef(function(e,t){var n,r,a,u,f=l.useRef(null),h=l.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=h[0],g=h[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noIsolation,A=e.inert,T=e.allowPinchZoom,L=e.as,P=e.gapMode,k=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,u=a.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),j=o(o({},k),m);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:p,removeScrollBar:b,shards:S,noIsolation:R,inert:A,setCallbacks:g,allowPinchZoom:!!T,lockRef:f,gapMode:P}),y?l.cloneElement(l.Children.only(w),o(o({},j),{ref:N})):l.createElement(void 0===L?"div":L,o({},j,{className:x,ref:N}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:a};var m=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,o({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=w(),R="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},L=function(){l.useEffect(function(){return document.body.setAttribute(R,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var i=l.useMemo(function(){return S(o)},[o]);return l.createElement(C,{styles:A(i,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){k=!1}var j=!!k&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),O(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},O=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{var v=I(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&O(e,u)&&(f+=m,p+=h),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},F=function(e){return e&&"current"in e?e.current:e},_=0,U=[];let z=(p.useMedium(function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(_++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(F),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=D(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return B(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(U.length&&U[U.length-1]===i){var n="deltaY"in e?H(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(F).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return U.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){U=U.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var v=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?l.createElement(P,{gapMode:e.gapMode}):null)}),m);var V=l.forwardRef(function(e,t){return l.createElement(h,o({},e,{ref:t,sideCar:z}))});V.classNames=h.classNames;let K=V},94315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(12115);n(95155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}}}]);