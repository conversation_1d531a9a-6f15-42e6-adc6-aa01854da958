(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,s,t)=>{"use strict";t.d(s,{cn:()=>n,s:()=>o});var r=t(49384),a=t(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}function o(e,s){let t=null;return function(...r){null!==t&&clearTimeout(t),t=setTimeout(()=>{t=null,e(...r)},s)}}},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},18820:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var r=t(60687),a=t(43210),n=t(27605),o=t(63442),i=t(44426),d=t(29523),l=t(89667),c=t(71669),u=t(5336),m=t(12597),p=t(13861),x=t(35071),h=t(41862),f=t(16189),g=t(35399);function b({token:e}){let[s,t]=(0,a.useState)(!1),[b,w]=(0,a.useState)(!1),[v,y]=(0,a.useState)(!1),[j,k]=(0,a.useState)({length:!1,hasNumber:!1,hasSymbol:!1}),[N,P]=(0,a.useState)(!1),z=(0,f.useRouter)(),A=(0,n.mN)({resolver:(0,o.u)(i.Oh),defaultValues:{password:"",confirmPassword:""}}),_=async s=>{y(!0);try{await g.authApi.updatePassword({token:e,password:s.password}),P(!0)}catch(e){console.error("Update password error:",e),A.setError("root",{type:"manual",message:e.message||"Failed to update password. Please try again."})}finally{y(!1)}};return N?(0,r.jsxs)("div",{className:"space-y-6 text-center",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,r.jsx)("div",{className:"rounded-full bg-green-100 p-3",children:(0,r.jsx)(u.A,{className:"h-10 w-10 text-green-600"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Password Updated"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Your password has been updated successfully. You can now log in with your new password."})]}),(0,r.jsx)(d.$,{onClick:()=>z.push("/login"),className:"w-full",children:"Go to Login"})]}):(0,r.jsx)(c.lV,{...A,children:(0,r.jsxs)("form",{onSubmit:A.handleSubmit(_),className:"space-y-6",children:[A.formState.errors.root&&(0,r.jsx)("div",{className:"rounded-md bg-red-50 p-3 text-sm text-red-500",children:A.formState.errors.root.message}),(0,r.jsx)(c.zB,{control:A.control,name:"password",render:({field:e})=>(0,r.jsxs)(c.eI,{children:[(0,r.jsx)(c.lR,{children:"New Password"}),(0,r.jsx)(c.MJ,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{placeholder:"Create a new password",type:s?"text":"password",disabled:v,...e}),(0,r.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>t(!s),disabled:v,children:s?(0,r.jsx)(m.A,{className:"text-muted-foreground h-4 w-4"}):(0,r.jsx)(p.A,{className:"text-muted-foreground h-4 w-4"})})]})}),(0,r.jsx)(c.C5,{}),(0,r.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[j.length?(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,r.jsx)("span",{className:j.length?"text-green-500":"text-muted-foreground",children:"Between 6-15 characters"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[j.hasNumber?(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,r.jsx)("span",{className:j.hasNumber?"text-green-500":"text-muted-foreground",children:"At least one number"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[j.hasSymbol?(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-500"}):(0,r.jsx)(x.A,{className:"text-muted-foreground h-4 w-4"}),(0,r.jsx)("span",{className:j.hasSymbol?"text-green-500":"text-muted-foreground",children:"At least one symbol"})]})]})]})}),(0,r.jsx)(c.zB,{control:A.control,name:"confirmPassword",render:({field:e})=>(0,r.jsxs)(c.eI,{children:[(0,r.jsx)(c.lR,{children:"Confirm Password"}),(0,r.jsx)(c.MJ,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{placeholder:"Confirm your new password",type:b?"text":"password",disabled:v,...e}),(0,r.jsx)(d.$,{type:"button",variant:"ghost",size:"icon",className:"absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!b),disabled:v,children:b?(0,r.jsx)(m.A,{className:"text-muted-foreground h-4 w-4"}):(0,r.jsx)(p.A,{className:"text-muted-foreground h-4 w-4"})})]})}),(0,r.jsx)(c.C5,{})]})}),(0,r.jsxs)(d.$,{type:"submit",className:"w-full",disabled:v,children:[v&&(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Password"]})]})})}var w=t(85814),v=t.n(w),y=t(48563);function j(){let e=(0,f.useParams)().token;return(0,r.jsx)("div",{className:"bg-background flex min-h-screen flex-col items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-card border-border w-full max-w-md space-y-8 rounded-lg border p-8 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsx)("div",{className:"rounded-md bg-blue-600 p-2 shadow-md",children:(0,r.jsx)(y.A,{className:"h-8 w-8 text-white"})}),(0,r.jsx)("h1",{className:"text-center text-2xl font-bold",children:"Update Password"}),(0,r.jsx)("p",{className:"text-muted-foreground text-center text-sm",children:"Create a new password for your account"})]}),(0,r.jsx)(b,{token:e}),(0,r.jsx)("div",{className:"text-center text-sm",children:(0,r.jsxs)("p",{className:"text-muted-foreground",children:["Remember your password?"," ",(0,r.jsx)(v(),{href:"/login",className:"text-primary font-medium hover:underline",children:"Log in"})]})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23828:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ruh_ai\\\\workflow_backend\\\\workflow-builder-app\\\\src\\\\app\\\\update-password\\\\[token]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\update-password\\[token]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>i});var r=t(60687);t(43210);var a=t(8730),n=t(24224),o=t(4780);let i=(0,n.F)("focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 rounded-md text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 shadow-xs",destructive:"bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs",outline:"bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:s,size:t,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,o.cn)(i({variant:s,size:t,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},39523:(e,s,t)=>{Promise.resolve().then(t.bind(t,18820))},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44426:(e,s,t)=>{"use strict";t.d(s,{O5:()=>a,Oh:()=>o,Oj:()=>i,jc:()=>n});var r=t(45880);r.z.object({email:r.z.string().email({message:"Please enter a valid email address."}),password:r.z.string().min(1,{message:"Password is required."})}),r.z.object({email:r.z.string().email({message:"Please enter a valid email address."})});let a=r.z.object({fullName:r.z.string().min(2,{message:"Full name must be at least 2 characters."}),email:r.z.string().email({message:"Please enter a valid email address."}),password:r.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),termsAccepted:r.z.boolean().refine(e=>!0===e,{message:"You must agree to the terms and privacy policy."})}),n=r.z.object({email:r.z.string().email({message:"Please enter a valid email address."})});r.z.object({newPassword:r.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmNewPassword:r.z.string()}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"Passwords do not match.",path:["confirmNewPassword"]});let o=r.z.object({password:r.z.string().min(6,{message:"Password must be at least 6 characters."}).max(15,{message:"Password must not exceed 15 characters."}).refine(e=>/[0-9]/.test(e),{message:"Password must contain at least one number."}).refine(e=>/[!@#$%^&*(),.?":{}|<>]/.test(e),{message:"Password must contain at least one symbol."}),confirmPassword:r.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match.",path:["confirmPassword"]});function i(e){return{length:e.length>=6&&e.length<=15,hasNumber:/[0-9]/.test(e),hasSymbol:/[!@#$%^&*(),.?":{}|<>]/.test(e)}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,s,t)=>{"use strict";t.d(s,{C5:()=>g,MJ:()=>f,eI:()=>x,lR:()=>h,lV:()=>l,zB:()=>u});var r=t(60687),a=t(43210),n=t(8730),o=t(27605),i=t(4780),d=t(80013);let l=o.Op,c=a.createContext({}),u=({...e})=>(0,r.jsx)(c.Provider,{value:{name:e.name},children:(0,r.jsx)(o.xI,{...e})}),m=()=>{let e=a.useContext(c),s=a.useContext(p),{getFieldState:t}=(0,o.xW)(),r=(0,o.lN)({name:e.name}),n=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=s;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},p=a.createContext({});function x({className:e,...s}){let t=a.useId();return(0,r.jsx)(p.Provider,{value:{id:t},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",e),...s})})}function h({className:e,...s}){let{error:t,formItemId:a}=m();return(0,r.jsx)(d.J,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...s})}function f({...e}){let{error:s,formItemId:t,formDescriptionId:a,formMessageId:o}=m();return(0,r.jsx)(n.DX,{"data-slot":"form-control",id:t,"aria-describedby":s?`${a} ${o}`:`${a}`,"aria-invalid":!!s,...e})}function g({className:e,...s}){let{error:t,formMessageId:a}=m(),n=t?String(t?.message??""):s.children;return n?(0,r.jsx)("p",{"data-slot":"form-message",id:a,className:(0,i.cn)("text-destructive text-sm",e),...s,children:n}):null}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79851:(e,s,t)=>{Promise.resolve().then(t.bind(t,23828))},80013:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var r=t(60687);t(43210);var a=t(78148),n=t(4780);function o({className:e,...s}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89667:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(60687);t(43210);var a=t(4780);function n({className:e,type:s,...t}){return(0,r.jsx)("input",{type:s,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94735:e=>{"use strict";e.exports=require("events")},98793:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var r=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let l={children:["",{children:["update-password",{children:["[token]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23828)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\update-password\\[token]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\ruh_ai\\workflow_backend\\workflow-builder-app\\src\\app\\update-password\\[token]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/update-password/[token]/page",pathname:"/update-password/[token]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,915,581,261,658,928,814,497,442,651],()=>t(98793));module.exports=r})();