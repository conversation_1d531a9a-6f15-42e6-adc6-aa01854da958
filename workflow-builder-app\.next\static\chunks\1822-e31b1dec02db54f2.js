(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1822],{381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1936:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("grip",[["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"19",cy:"5",r:"1",key:"w8mnmm"}],["circle",{cx:"5",cy:"5",r:"1",key:"lttvr7"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}],["circle",{cx:"19",cy:"19",r:"1",key:"shf9b7"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]])},4229:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4884:(e,t,n)=>{"use strict";n.d(t,{bL:()=>_,zi:()=>k});var r=n(12115),o=n(85185),i=n(6101),a=n(46081),l=n(5845),u=n(45503),s=n(11275),c=n(63655),d=n(95155),f="Switch",[h,p]=(0,a.A)(f),[m,v]=h(f),y=r.forwardRef((e,t)=>{let{__scopeSwitch:n,name:a,checked:u,defaultChecked:s,required:f,disabled:h,value:p="on",onCheckedChange:v,form:y,...g}=e,[w,_]=r.useState(null),k=(0,i.s)(t,e=>_(e)),A=r.useRef(!1),E=!w||y||!!w.closest("form"),[M=!1,C]=(0,l.i)({prop:u,defaultProp:s,onChange:v});return(0,d.jsxs)(m,{scope:n,checked:M,disabled:h,children:[(0,d.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":f,"data-state":x(M),"data-disabled":h?"":void 0,disabled:h,value:p,...g,ref:k,onClick:(0,o.m)(e.onClick,e=>{C(e=>!e),E&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),E&&(0,d.jsx)(b,{control:w,bubbles:!A.current,name:a,value:p,checked:M,required:f,disabled:h,form:y,style:{transform:"translateX(-100%)"}})]})});y.displayName=f;var g="SwitchThumb",w=r.forwardRef((e,t)=>{let{__scopeSwitch:n,...r}=e,o=v(g,n);return(0,d.jsx)(c.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})});w.displayName=g;var b=e=>{let{control:t,checked:n,bubbles:o=!0,...i}=e,a=r.useRef(null),l=(0,u.Z)(n),c=(0,s.X)(t);return r.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(l!==n&&t){let r=new Event("click",{bubbles:o});t.call(e,n),e.dispatchEvent(r)}},[l,n,o]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...i,tabIndex:-1,ref:a,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return e?"checked":"unchecked"}var _=y,k=w},5937:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]])},6115:(e,t,n)=>{"use strict";var r=n(12115),o=n(49033),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,u=r.useEffect,s=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=l(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var h=a(e,(d=s(function(){function e(e){if(!u){if(u=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,u=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,n,r,o]))[0],d[1]);return u(function(){f.hasValue=!0,f.value=h},[h]),c(h),h}},9393:(e,t,n)=>{"use strict";n.d(t,{A:()=>o,j:()=>i});var r=n(27271);function o(e,t,n){return arguments.length>1?this.each((null==t?function(e){return function(){this.style.removeProperty(e)}}:"function"==typeof t?function(e,t,n){return function(){var r=t.apply(this,arguments);null==r?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}:function(e,t,n){return function(){this.style.setProperty(e,t,n)}})(e,t,null==n?"":n)):i(this.node(),e)}function i(e,t){return e.style.getPropertyValue(t)||(0,r.A)(e).getComputedStyle(e,null).getPropertyValue(t)}},10081:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},11687:()=>{},14711:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("sliders-vertical",[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]])},14897:(e,t,n)=>{"use strict";function r(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}n.d(t,{A:()=>r})},16672:(e,t,n)=>{"use strict";n.d(t,{A:()=>p});var r=n(61235),o=n(82903),i=n(14897),a=n(29204),l=n(50806);let u=e=>()=>e;function s(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:l,dx:u,dy:s,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:l,enumerable:!0,configurable:!0},dx:{value:u,enumerable:!0,configurable:!0},dy:{value:s,enumerable:!0,configurable:!0},_:{value:c}})}function c(e){return!e.ctrlKey&&!e.button}function d(){return this.parentNode}function f(e,t){return null==t?{x:e.x,y:e.y}:t}function h(){return navigator.maxTouchPoints||"ontouchstart"in this}function p(){var e,t,n,p,m=c,v=d,y=f,g=h,w={},b=(0,r.A)("start","drag","end"),x=0,_=0;function k(e){e.on("mousedown.drag",A).filter(g).on("touchstart.drag",C).on("touchmove.drag",T,l.vr).on("touchend.drag touchcancel.drag",R).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function A(r,i){if(!p&&m.call(this,r,i)){var u=S(this,v.call(this,r,i),r,i,"mouse");u&&((0,o.A)(r.view).on("mousemove.drag",E,l.Rw).on("mouseup.drag",M,l.Rw),(0,a.A)(r.view),(0,l.GK)(r),n=!1,e=r.clientX,t=r.clientY,u("start",r))}}function E(r){if((0,l.Ay)(r),!n){var o=r.clientX-e,i=r.clientY-t;n=o*o+i*i>_}w.mouse("drag",r)}function M(e){(0,o.A)(e.view).on("mousemove.drag mouseup.drag",null),(0,a.y)(e.view,n),(0,l.Ay)(e),w.mouse("end",e)}function C(e,t){if(m.call(this,e,t)){var n,r,o=e.changedTouches,i=v.call(this,e,t),a=o.length;for(n=0;n<a;++n)(r=S(this,i,e,t,o[n].identifier,o[n]))&&((0,l.GK)(e),r("start",e,o[n]))}}function T(e){var t,n,r=e.changedTouches,o=r.length;for(t=0;t<o;++t)(n=w[r[t].identifier])&&((0,l.Ay)(e),n("drag",e,r[t]))}function R(e){var t,n,r=e.changedTouches,o=r.length;for(p&&clearTimeout(p),p=setTimeout(function(){p=null},500),t=0;t<o;++t)(n=w[r[t].identifier])&&((0,l.GK)(e),n("end",e,r[t]))}function S(e,t,n,r,o,a){var l,u,c,d=b.copy(),f=(0,i.A)(a||n,t);if(null!=(c=y.call(e,new s("beforestart",{sourceEvent:n,target:k,identifier:o,active:x,x:f[0],y:f[1],dx:0,dy:0,dispatch:d}),r)))return l=c.x-f[0]||0,u=c.y-f[1]||0,function n(a,h,p){var m,v=f;switch(a){case"start":w[o]=n,m=x++;break;case"end":delete w[o],--x;case"drag":f=(0,i.A)(p||h,t),m=x}d.call(a,e,new s(a,{sourceEvent:h,subject:c,target:k,identifier:o,active:m,x:f[0]+l,y:f[1]+u,dx:f[0]-v[0],dy:f[1]-v[1],dispatch:d}),r)}}return k.filter=function(e){return arguments.length?(m="function"==typeof e?e:u(!!e),k):m},k.container=function(e){return arguments.length?(v="function"==typeof e?e:u(e),k):v},k.subject=function(e){return arguments.length?(y="function"==typeof e?e:u(e),k):y},k.touchable=function(e){return arguments.length?(g="function"==typeof e?e:u(!!e),k):g},k.on=function(){var e=b.on.apply(b,arguments);return e===b?k:e},k.clickDistance=function(e){return arguments.length?(_=(e*=1)*e,k):Math.sqrt(_)},k}s.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e}},17051:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("cog",[["path",{d:"M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z",key:"sobvz5"}],["path",{d:"M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z",key:"11i496"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 22v-2",key:"1osdcq"}],["path",{d:"m17 20.66-1-1.73",key:"eq3orb"}],["path",{d:"M11 10.27 7 3.34",key:"16pf9h"}],["path",{d:"m20.66 17-1.73-1",key:"sg0v6f"}],["path",{d:"m3.34 7 1.73 1",key:"1ulond"}],["path",{d:"M14 12h8",key:"4f43i9"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"m20.66 7-1.73 1",key:"1ow05n"}],["path",{d:"m3.34 17 1.73-1",key:"nuk764"}],["path",{d:"m17 3.34-1 1.73",key:"2wel8s"}],["path",{d:"m11 13.73-4 6.93",key:"794ttg"}]])},18979:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]])},22436:(e,t,n)=>{"use strict";var r=n(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return l(function(){o.value=n,o.getSnapshot=t,s(o)&&c({inst:o})},[e,n,t]),a(function(){return s(o)&&c({inst:o}),e(function(){s(o)&&c({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},23478:(e,t,n)=>{"use strict";n.d(t,{UC:()=>ea,Y9:()=>eo,q7:()=>er,bL:()=>en,l9:()=>ei});var r=n(12115),o=n(46081),i=n(82284),a=n(6101),l=n(85185),u=n(5845),s=n(63655),c=n(52712),d=n(28905),f=n(61285),h=n(95155),p="Collapsible",[m,v]=(0,o.A)(p),[y,g]=m(p),w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:i,disabled:a,onOpenChange:l,...c}=e,[d=!1,p]=(0,u.i)({prop:o,defaultProp:i,onChange:l});return(0,h.jsx)(y,{scope:n,disabled:a,contentId:(0,f.B)(),open:d,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),children:(0,h.jsx)(s.sG.div,{"data-state":E(d),"data-disabled":a?"":void 0,...c,ref:t})})});w.displayName=p;var b="CollapsibleTrigger",x=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,o=g(b,n);return(0,h.jsx)(s.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":E(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:(0,l.m)(e.onClick,o.onOpenToggle)})});x.displayName=b;var _="CollapsibleContent",k=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=g(_,e.__scopeCollapsible);return(0,h.jsx)(d.C,{present:n||o.open,children:e=>{let{present:n}=e;return(0,h.jsx)(A,{...r,ref:t,present:n})}})});k.displayName=_;var A=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:i,...l}=e,u=g(_,n),[d,f]=r.useState(o),p=r.useRef(null),m=(0,a.s)(t,p),v=r.useRef(0),y=v.current,w=r.useRef(0),b=w.current,x=u.open||d,k=r.useRef(x),A=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,c.N)(()=>{let e=p.current;if(e){A.current=A.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,w.current=t.width,k.current||(e.style.transitionDuration=A.current.transitionDuration,e.style.animationName=A.current.animationName),f(o)}},[u.open,o]),(0,h.jsx)(s.sG.div,{"data-state":E(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!x,...l,ref:m,style:{"--radix-collapsible-content-height":y?"".concat(y,"px"):void 0,"--radix-collapsible-content-width":b?"".concat(b,"px"):void 0,...e.style},children:x&&i})});function E(e){return e?"open":"closed"}var M=n(94315),C="Accordion",T=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[R,S,j]=(0,i.N)(C),[N,z]=(0,o.A)(C,[j,v]),D=v(),P=r.forwardRef((e,t)=>{let{type:n,...r}=e;return(0,h.jsx)(R.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,h.jsx)($,{...r,ref:t}):(0,h.jsx)(q,{...r,ref:t})})});P.displayName=C;var[O,L]=N(C),[I,H]=N(C,{collapsible:!1}),q=r.forwardRef((e,t)=>{let{value:n,defaultValue:o,onValueChange:i=()=>{},collapsible:a=!1,...l}=e,[s,c]=(0,u.i)({prop:n,defaultProp:o,onChange:i});return(0,h.jsx)(O,{scope:e.__scopeAccordion,value:s?[s]:[],onItemOpen:c,onItemClose:r.useCallback(()=>a&&c(""),[a,c]),children:(0,h.jsx)(I,{scope:e.__scopeAccordion,collapsible:a,children:(0,h.jsx)(V,{...l,ref:t})})})}),$=r.forwardRef((e,t)=>{let{value:n,defaultValue:o,onValueChange:i=()=>{},...a}=e,[l=[],s]=(0,u.i)({prop:n,defaultProp:o,onChange:i}),c=r.useCallback(e=>s(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[s]),d=r.useCallback(e=>s(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[s]);return(0,h.jsx)(O,{scope:e.__scopeAccordion,value:l,onItemOpen:c,onItemClose:d,children:(0,h.jsx)(I,{scope:e.__scopeAccordion,collapsible:!0,children:(0,h.jsx)(V,{...a,ref:t})})})}),[B,F]=N(C),V=r.forwardRef((e,t)=>{let{__scopeAccordion:n,disabled:o,dir:i,orientation:u="vertical",...c}=e,d=r.useRef(null),f=(0,a.s)(d,t),p=S(n),m="ltr"===(0,M.jH)(i),v=(0,l.m)(e.onKeyDown,e=>{var t;if(!T.includes(e.key))return;let n=e.target,r=p().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=r.findIndex(e=>e.ref.current===n),i=r.length;if(-1===o)return;e.preventDefault();let a=o,l=i-1,s=()=>{(a=o+1)>l&&(a=0)},c=()=>{(a=o-1)<0&&(a=l)};switch(e.key){case"Home":a=0;break;case"End":a=l;break;case"ArrowRight":"horizontal"===u&&(m?s():c());break;case"ArrowDown":"vertical"===u&&s();break;case"ArrowLeft":"horizontal"===u&&(m?c():s());break;case"ArrowUp":"vertical"===u&&c()}null===(t=r[a%i].ref.current)||void 0===t||t.focus()});return(0,h.jsx)(B,{scope:n,disabled:o,direction:i,orientation:u,children:(0,h.jsx)(R.Slot,{scope:n,children:(0,h.jsx)(s.sG.div,{...c,"data-orientation":u,ref:f,onKeyDown:o?void 0:v})})})}),U="AccordionItem",[X,Y]=N(U),W=r.forwardRef((e,t)=>{let{__scopeAccordion:n,value:r,...o}=e,i=F(U,n),a=L(U,n),l=D(n),u=(0,f.B)(),s=r&&a.value.includes(r)||!1,c=i.disabled||e.disabled;return(0,h.jsx)(X,{scope:n,open:s,disabled:c,triggerId:u,children:(0,h.jsx)(w,{"data-orientation":i.orientation,"data-state":et(s),...l,...o,ref:t,disabled:c,open:s,onOpenChange:e=>{e?a.onItemOpen(r):a.onItemClose(r)}})})});W.displayName=U;var G="AccordionHeader",K=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=F(C,n),i=Y(G,n);return(0,h.jsx)(s.sG.h3,{"data-orientation":o.orientation,"data-state":et(i.open),"data-disabled":i.disabled?"":void 0,...r,ref:t})});K.displayName=G;var Z="AccordionTrigger",J=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=F(C,n),i=Y(Z,n),a=H(Z,n),l=D(n);return(0,h.jsx)(R.ItemSlot,{scope:n,children:(0,h.jsx)(x,{"aria-disabled":i.open&&!a.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId,...l,...r,ref:t})})});J.displayName=Z;var Q="AccordionContent",ee=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=F(C,n),i=Y(Q,n),a=D(n);return(0,h.jsx)(k,{role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation,...a,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=Q;var en=P,er=W,eo=K,ei=J,ea=ee},23837:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]])},24357:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},25541:(e,t,n)=>{"use strict";n.d(t,{V:()=>p,_:()=>r});var r,o=n(12115),i=n(75694),a=n(74211);function l(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}function u({color:e,dimensions:t,lineWidth:n}){return o.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function s({color:e,radius:t}){return o.createElement("circle",{cx:t,cy:t,r:t,fill:e})}!function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"}(r||(r={}));let c={[r.Dots]:"#91919a",[r.Lines]:"#eee",[r.Cross]:"#e2e2e2"},d={[r.Dots]:1,[r.Lines]:1,[r.Cross]:6},f=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function h({id:e,variant:t=r.Dots,gap:n=20,size:h,lineWidth:p=1,offset:m=2,color:v,style:y,className:g}){let w=(0,o.useRef)(null),{transform:b,patternId:x}=(0,a.Pj)(f,l),_=v||c[t],k=h||d[t],A=t===r.Dots,E=t===r.Cross,M=Array.isArray(n)?n:[n,n],C=[M[0]*b[2]||1,M[1]*b[2]||1],T=k*b[2],R=E?[T,T]:C,S=A?[T/m,T/m]:[R[0]/m,R[1]/m];return o.createElement("svg",{className:(0,i.A)(["react-flow__background",g]),style:{...y,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:w,"data-testid":"rf__background"},o.createElement("pattern",{id:x+e,x:b[0]%C[0],y:b[1]%C[1],width:C[0],height:C[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${S[0]},-${S[1]})`},A?o.createElement(s,{color:_,radius:T/m}):o.createElement(u,{dimensions:R,color:_,lineWidth:p})),o.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${x+e})`}))}h.displayName="Background";var p=(0,o.memo)(h)},27271:(e,t,n)=>{"use strict";function r(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}n.d(t,{A:()=>r})},28309:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-minus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]])},28905:(e,t,n)=>{"use strict";n.d(t,{C:()=>a});var r=n(12115),o=n(6101),i=n(52712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},29204:(e,t,n)=>{"use strict";n.d(t,{A:()=>i,y:()=>a});var r=n(82903),o=n(50806);function i(e){var t=e.document.documentElement,n=(0,r.A)(e).on("dragstart.drag",o.Ay,o.Rw);"onselectstart"in t?n.on("selectstart.drag",o.Ay,o.Rw):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function a(e,t){var n=e.document.documentElement,i=(0,r.A)(e).on("dragstart.drag",null);t&&(i.on("click.drag",o.Ay,o.Rw),setTimeout(function(){i.on("click.drag",null)},0)),"onselectstart"in n?i.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}},29621:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},30064:(e,t,n)=>{"use strict";n.d(t,{UC:()=>G,B8:()=>Y,bL:()=>X,l9:()=>W});var r=n(12115),o=n(85185),i=n(46081),a=n(82284),l=n(6101),u=n(61285),s=n(63655),c=n(39033),d=n(5845),f=n(94315),h=n(95155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,g,w]=(0,a.N)(v),[b,x]=(0,i.A)(v,[w]),[_,k]=b(v),A=r.forwardRef((e,t)=>(0,h.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(E,{...e,ref:t})})}));A.displayName=v;var E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:a=!1,dir:u,currentTabStopId:v,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:w,onEntryFocus:b,preventScrollOnEntryFocus:x=!1,...k}=e,A=r.useRef(null),E=(0,l.s)(t,A),M=(0,f.jH)(u),[C=null,T]=(0,d.i)({prop:v,defaultProp:y,onChange:w}),[S,j]=r.useState(!1),N=(0,c.c)(b),z=g(n),D=r.useRef(!1),[P,O]=r.useState(0);return r.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,N),()=>e.removeEventListener(p,N)},[N]),(0,h.jsx)(_,{scope:n,orientation:i,dir:M,loop:a,currentTabStopId:C,onItemFocus:r.useCallback(e=>T(e),[T]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>O(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>O(e=>e-1),[]),children:(0,h.jsx)(s.sG.div,{tabIndex:S||0===P?-1:0,"data-orientation":i,...k,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=z().filter(e=>e.focusable);R([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),x)}}D.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),M="RovingFocusGroupItem",C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,...c}=e,d=(0,u.B)(),f=l||d,p=k(M,n),m=p.currentTabStopId===f,v=g(n),{onFocusableItemAdd:w,onFocusableItemRemove:b}=p;return r.useEffect(()=>{if(i)return w(),()=>b()},[i,w,b]),(0,h.jsx)(y.ItemSlot,{scope:n,id:f,focusable:i,active:a,children:(0,h.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>R(n))}})})})});C.displayName=M;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function R(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var S=n(28905),j="Tabs",[N,z]=(0,i.A)(j,[x]),D=x(),[P,O]=N(j),L=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:l,activationMode:c="automatic",...p}=e,m=(0,f.jH)(l),[v,y]=(0,d.i)({prop:r,onChange:o,defaultProp:i});return(0,h.jsx)(P,{scope:n,baseId:(0,u.B)(),value:v,onValueChange:y,orientation:a,dir:m,activationMode:c,children:(0,h.jsx)(s.sG.div,{dir:m,"data-orientation":a,...p,ref:t})})});L.displayName=j;var I="TabsList",H=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=O(I,n),a=D(n);return(0,h.jsx)(A,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,h.jsx)(s.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});H.displayName=I;var q="TabsTrigger",$=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,l=O(q,n),u=D(n),c=V(l.baseId,r),d=U(l.baseId,r),f=r===l.value;return(0,h.jsx)(C,{asChild:!0,...u,focusable:!i,active:f,children:(0,h.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:c,...a,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;f||i||!e||l.onValueChange(r)})})})});$.displayName=q;var B="TabsContent",F=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:a,...l}=e,u=O(B,n),c=V(u.baseId,o),d=U(u.baseId,o),f=o===u.value,p=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(S.C,{present:i||f,children:n=>{let{present:r}=n;return(0,h.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:d,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&a})}})});function V(e,t){return"".concat(e,"-trigger-").concat(t)}function U(e,t){return"".concat(e,"-content-").concat(t)}F.displayName=B;var X=L,Y=H,W=$,G=F},32650:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("clipboard",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}]])},34835:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},35131:(e,t,n)=>{"use strict";n.d(t,{A:()=>o,g:()=>r});var r="http://www.w3.org/1999/xhtml";let o={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},38164:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},40425:(e,t,n)=>{"use strict";n.d(t,{h:()=>f,n:()=>c});var r=n(12115),o=n(45643);let i=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},a=e=>e?i(e):i,{useDebugValue:l}=r,{useSyncExternalStoreWithSelector:u}=o,s=e=>e;function c(e,t=s,n){let r=u(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return l(r),r}let d=(e,t)=>{let n=a(e),r=(e,r=t)=>c(n,e,r);return Object.assign(r,n),r},f=(e,t)=>e?d(e,t):d},40646:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},41684:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},43453:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},43454:(e,t,n)=>{"use strict";n.d(t,{o:()=>g});var r=n(12115),o=n(75694);function i(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var a=n(93219),l=n(82903),u=n(14897),s=n(74211);let c=({id:e,x:t,y:n,width:i,height:a,style:l,color:u,strokeColor:s,strokeWidth:c,className:d,borderRadius:f,shapeRendering:h,onClick:p,selected:m})=>{let{background:v,backgroundColor:y}=l||{};return r.createElement("rect",{className:(0,o.A)(["react-flow__minimap-node",{selected:m},d]),x:t,y:n,rx:f,ry:f,width:i,height:a,fill:u||v||y,stroke:s,strokeWidth:c,shapeRendering:h,onClick:p?t=>p(t,e):void 0})};c.displayName="MiniMapNode";var d=(0,r.memo)(c);let f=e=>e.nodeOrigin,h=e=>e.getNodes().filter(e=>!e.hidden&&e.width&&e.height),p=e=>e instanceof Function?e:()=>e;var m=(0,r.memo)(function({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:o=5,nodeStrokeWidth:a=2,nodeComponent:l=d,onClick:u}){let c=(0,s.Pj)(h,i),m=(0,s.Pj)(f),v=p(t),y=p(e),g=p(n),w="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision";return r.createElement(r.Fragment,null,c.map(e=>{let{x:t,y:n}=(0,s.Cz)(e,m).positionAbsolute;return r.createElement(l,{key:e.id,x:t,y:n,width:e.width,height:e.height,style:e.style,selected:e.selected,className:g(e),color:v(e),borderRadius:o,strokeColor:y(e),strokeWidth:a,shapeRendering:w,onClick:u,id:e.id})}))});let v=e=>{let t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?(0,s.Mi)((0,s.Jo)(t,e.nodeOrigin),n):n,rfId:e.rfId}};function y({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:c="#e2e2e2",nodeClassName:d="",nodeBorderRadius:f=5,nodeStrokeWidth:h=2,nodeComponent:p,maskColor:y="rgb(240, 240, 240, 0.6)",maskStrokeColor:g="none",maskStrokeWidth:w=1,position:b="bottom-right",onClick:x,onNodeClick:_,pannable:k=!1,zoomable:A=!1,ariaLabel:E="React Flow mini map",inversePan:M=!1,zoomStep:C=10,offsetScale:T=5}){let R=(0,s.PI)(),S=(0,r.useRef)(null),{boundingRect:j,viewBB:N,rfId:z}=(0,s.Pj)(v,i),D=e?.width??200,P=e?.height??150,O=Math.max(j.width/D,j.height/P),L=O*D,I=O*P,H=T*O,q=j.x-(L-j.width)/2-H,$=j.y-(I-j.height)/2-H,B=L+2*H,F=I+2*H,V=`react-flow__minimap-desc-${z}`,U=(0,r.useRef)(0);U.current=O,(0,r.useEffect)(()=>{if(S.current){let e=(0,l.A)(S.current),t=(0,a.s_)().on("zoom",k?e=>{let{transform:t,d3Selection:n,d3Zoom:r,translateExtent:o,width:i,height:l}=R.getState();if("mousemove"!==e.sourceEvent.type||!n||!r)return;let u=U.current*Math.max(1,t[2])*(M?-1:1),s={x:t[0]-e.sourceEvent.movementX*u,y:t[1]-e.sourceEvent.movementY*u},c=a.GS.translate(s.x,s.y).scale(t[2]),d=r.constrain()(c,[[0,0],[i,l]],o);r.transform(n,d)}:null).on("zoom.wheel",A?e=>{let{transform:t,d3Selection:n,d3Zoom:r}=R.getState();if("wheel"!==e.sourceEvent.type||!n||!r)return;let o=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*C,i=t[2]*Math.pow(2,o);r.scaleTo(n,i)}:null);return e.call(t),()=>{e.on("zoom",null)}}},[k,A,M,C]);let X=x?e=>{let t=(0,u.A)(e);x(e,{x:t[0],y:t[1]})}:void 0,Y=_?(e,t)=>{_(e,R.getState().nodeInternals.get(t))}:void 0;return r.createElement(s.Zk,{position:b,style:e,className:(0,o.A)(["react-flow__minimap",t]),"data-testid":"rf__minimap"},r.createElement("svg",{width:D,height:P,viewBox:`${q} ${$} ${B} ${F}`,role:"img","aria-labelledby":V,ref:S,onClick:X},E&&r.createElement("title",{id:V},E),r.createElement(m,{onClick:Y,nodeColor:c,nodeStrokeColor:n,nodeBorderRadius:f,nodeClassName:d,nodeStrokeWidth:h,nodeComponent:p}),r.createElement("path",{className:"react-flow__minimap-mask",d:`M${q-H},${$-H}h${B+2*H}v${F+2*H}h${-B-2*H}z
        M${N.x},${N.y}h${N.width}v${N.height}h${-N.width}z`,fill:y,fillRule:"evenodd",stroke:g,strokeWidth:w,pointerEvents:"none"})))}y.displayName="MiniMap";var g=(0,r.memo)(y)},45643:(e,t,n)=>{"use strict";e.exports=n(6115)},47655:(e,t,n)=>{"use strict";n.d(t,{LM:()=>W,OK:()=>G,VM:()=>k,bL:()=>Y,lr:()=>D});var r=n(12115),o=n(63655),i=n(28905),a=n(46081),l=n(6101),u=n(39033),s=n(94315),c=n(52712),d=n(89367),f=n(85185),h=n(95155),p="ScrollArea",[m,v]=(0,a.A)(p),[y,g]=m(p),w=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:i="hover",dir:a,scrollHideDelay:u=600,...c}=e,[d,f]=r.useState(null),[p,m]=r.useState(null),[v,g]=r.useState(null),[w,b]=r.useState(null),[x,_]=r.useState(null),[k,A]=r.useState(0),[E,M]=r.useState(0),[C,T]=r.useState(!1),[R,S]=r.useState(!1),j=(0,l.s)(t,e=>f(e)),N=(0,s.jH)(a);return(0,h.jsx)(y,{scope:n,type:i,dir:N,scrollHideDelay:u,scrollArea:d,viewport:p,onViewportChange:m,content:v,onContentChange:g,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:C,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:_,scrollbarYEnabled:R,onScrollbarYEnabledChange:S,onCornerWidthChange:A,onCornerHeightChange:M,children:(0,h.jsx)(o.sG.div,{dir:N,...c,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":k+"px","--radix-scroll-area-corner-height":E+"px",...e.style}})})});w.displayName=p;var b="ScrollAreaViewport",x=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:i,nonce:a,...u}=e,s=g(b,n),c=r.useRef(null),d=(0,l.s)(t,c,s.onViewportChange);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,h.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...u,ref:d,style:{overflowX:s.scrollbarXEnabled?"scroll":"hidden",overflowY:s.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,h.jsx)("div",{ref:s.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var _="ScrollAreaScrollbar",k=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=g(_,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:l}=i,u="horizontal"===e.orientation;return r.useEffect(()=>(u?a(!0):l(!0),()=>{u?a(!1):l(!1)}),[u,a,l]),"hover"===i.type?(0,h.jsx)(A,{...o,ref:t,forceMount:n}):"scroll"===i.type?(0,h.jsx)(E,{...o,ref:t,forceMount:n}):"auto"===i.type?(0,h.jsx)(M,{...o,ref:t,forceMount:n}):"always"===i.type?(0,h.jsx)(C,{...o,ref:t}):null});k.displayName=_;var A=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,a=g(_,e.__scopeScrollArea),[l,u]=r.useState(!1);return r.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),u(!0)},r=()=>{t=window.setTimeout(()=>u(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[a.scrollArea,a.scrollHideDelay]),(0,h.jsx)(i.C,{present:n||l,children:(0,h.jsx)(M,{"data-state":l?"visible":"hidden",...o,ref:t})})}),E=r.forwardRef((e,t)=>{var n;let{forceMount:o,...a}=e,l=g(_,e.__scopeScrollArea),u="horizontal"===e.orientation,s=U(()=>d("SCROLL_END"),100),[c,d]=(n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},"hidden"));return r.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>d("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,l.scrollHideDelay,d]),r.useEffect(()=>{let e=l.viewport,t=u?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(d("SCROLL"),s()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[l.viewport,u,d,s]),(0,h.jsx)(i.C,{present:o||"hidden"!==c,children:(0,h.jsx)(C,{"data-state":"hidden"===c?"hidden":"visible",...a,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),M=r.forwardRef((e,t)=>{let n=g(_,e.__scopeScrollArea),{forceMount:o,...a}=e,[l,u]=r.useState(!1),s="horizontal"===e.orientation,c=U(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;u(s?e:t)}},10);return X(n.viewport,c),X(n.content,c),(0,h.jsx)(i.C,{present:o||l,children:(0,h.jsx)(C,{"data-state":l?"visible":"hidden",...a,ref:t})})}),C=r.forwardRef((e,t)=>{let{orientation:n="vertical",...o}=e,i=g(_,e.__scopeScrollArea),a=r.useRef(null),l=r.useRef(0),[u,s]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=q(u.viewport,u.content),d={...o,sizes:u,onSizesChange:s,hasThumb:!!(c>0&&c<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=$(n),i=t||o/2,a=n.scrollbar.paddingStart+i,l=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),u=n.content-n.viewport;return F([a,l],"ltr"===r?[0,u]:[-+u,0])(e)}(e,l.current,u,t)}return"horizontal"===n?(0,h.jsx)(T,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollLeft,u,i.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===n?(0,h.jsx)(R,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&a.current){let e=B(i.viewport.scrollTop,u);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),T=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=g(_,e.__scopeScrollArea),[u,s]=r.useState(),c=r.useRef(null),d=(0,l.s)(t,c,a.onScrollbarXChange);return r.useEffect(()=>{c.current&&s(getComputedStyle(c.current))},[c]),(0,h.jsx)(N,{"data-orientation":"horizontal",...i,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":$(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&u&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:H(u.paddingLeft),paddingEnd:H(u.paddingRight)}})}})}),R=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...i}=e,a=g(_,e.__scopeScrollArea),[u,s]=r.useState(),c=r.useRef(null),d=(0,l.s)(t,c,a.onScrollbarYChange);return r.useEffect(()=>{c.current&&s(getComputedStyle(c.current))},[c]),(0,h.jsx)(N,{"data-orientation":"vertical",...i,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":$(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),function(e,t){return e>0&&e<t}(r,n)&&t.preventDefault()}},onResize:()=>{c.current&&a.viewport&&u&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:H(u.paddingTop),paddingEnd:H(u.paddingBottom)}})}})}),[S,j]=m(_),N=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:i,hasThumb:a,onThumbChange:s,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:p,onDragScroll:m,onWheelScroll:v,onResize:y,...w}=e,b=g(_,n),[x,k]=r.useState(null),A=(0,l.s)(t,e=>k(e)),E=r.useRef(null),M=r.useRef(""),C=b.viewport,T=i.content-i.viewport,R=(0,u.c)(v),j=(0,u.c)(p),N=U(y,10);function z(e){E.current&&m({x:e.clientX-E.current.left,y:e.clientY-E.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;(null==x?void 0:x.contains(t))&&R(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[C,x,T,R]),r.useEffect(j,[i,j]),X(x,N),X(b.content,N),(0,h.jsx)(S,{scope:n,scrollbar:x,hasThumb:a,onThumbChange:(0,u.c)(s),onThumbPointerUp:(0,u.c)(c),onThumbPositionChange:j,onThumbPointerDown:(0,u.c)(d),children:(0,h.jsx)(o.sG.div,{...w,ref:A,style:{position:"absolute",...w.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),E.current=x.getBoundingClientRect(),M.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),z(e))}),onPointerMove:(0,f.m)(e.onPointerMove,z),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=M.current,b.viewport&&(b.viewport.style.scrollBehavior=""),E.current=null})})})}),z="ScrollAreaThumb",D=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=j(z,e.__scopeScrollArea);return(0,h.jsx)(i.C,{present:n||o.hasThumb,children:(0,h.jsx)(P,{ref:t,...r})})}),P=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:i,...a}=e,u=g(z,n),s=j(z,n),{onThumbPositionChange:c}=s,d=(0,l.s)(t,e=>s.onThumbChange(e)),p=r.useRef(void 0),m=U(()=>{p.current&&(p.current(),p.current=void 0)},100);return r.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{m(),p.current||(p.current=V(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,m,c]),(0,h.jsx)(o.sG.div,{"data-state":s.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;s.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,f.m)(e.onPointerUp,s.onThumbPointerUp)})});D.displayName=z;var O="ScrollAreaCorner",L=r.forwardRef((e,t)=>{let n=g(O,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,h.jsx)(I,{...e,ref:t}):null});L.displayName=O;var I=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,a=g(O,n),[l,u]=r.useState(0),[s,c]=r.useState(0),d=!!(l&&s);return X(a.scrollbarX,()=>{var e;let t=(null===(e=a.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;a.onCornerHeightChange(t),c(t)}),X(a.scrollbarY,()=>{var e;let t=(null===(e=a.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;a.onCornerWidthChange(t),u(t)}),d?(0,h.jsx)(o.sG.div,{...i,ref:t,style:{width:l,height:s,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function q(e,t){let n=e/t;return isNaN(n)?0:n}function $(e){let t=q(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function B(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=$(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,a=t.content-t.viewport,l=(0,d.q)(e,"ltr"===n?[0,a]:[-+a,0]);return F([0,a],[0,i-r])(l)}function F(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var V=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=n.left!==i.left,l=n.top!==i.top;(a||l)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function U(e,t){let n=(0,u.c)(e),o=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(n,t)},[n,t])}function X(e,t){let n=(0,u.c)(t);(0,c.N)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var Y=w,W=x,G=L},49033:(e,t,n)=>{"use strict";e.exports=n(22436)},49103:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-plus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},49376:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},50589:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]])},50806:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>a,GK:()=>i,Rw:()=>o,vr:()=>r});let r={passive:!1},o={capture:!0,passive:!1};function i(e){e.stopImmediatePropagation()}function a(e){e.preventDefault(),e.stopImmediatePropagation()}},54213:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},54861:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},57434:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},60394:(e,t,n)=>{"use strict";function r(){}function o(e){return null==e?r:function(){return this.querySelector(e)}}n.d(t,{A:()=>o})},61235:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r={value:()=>{}};function o(){for(var e,t=0,n=arguments.length,r={};t<n;++t){if(!(e=arguments[t]+"")||e in r||/[\s.]/.test(e))throw Error("illegal type: "+e);r[e]=[]}return new i(r)}function i(e){this._=e}function a(e,t,n){for(var o=0,i=e.length;o<i;++o)if(e[o].name===t){e[o]=r,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}i.prototype=o.prototype={constructor:i,on:function(e,t){var n,r=this._,o=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!r.hasOwnProperty(e))throw Error("unknown type: "+e);return{type:e,name:t}}),i=-1,l=o.length;if(arguments.length<2){for(;++i<l;)if((n=(e=o[i]).type)&&(n=function(e,t){for(var n,r=0,o=e.length;r<o;++r)if((n=e[r]).name===t)return n.value}(r[n],e.name)))return n;return}if(null!=t&&"function"!=typeof t)throw Error("invalid callback: "+t);for(;++i<l;)if(n=(e=o[i]).type)r[n]=a(r[n],e.name,t);else if(null==t)for(n in r)r[n]=a(r[n],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new i(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,r,o=Array(n),i=0;i<n;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(r=this._[e],i=0,n=r.length;i<n;++i)r[i].value.apply(t,o)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};let l=o},62098:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},63449:(e,t,n)=>{"use strict";n.d(t,{H4:()=>E,_V:()=>A,bL:()=>k});var r=n(12115),o=n(46081),i=n(39033),a=n(52712);n(47650);var l=n(99708),u=n(95155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,l.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),c=n(49033);function d(){return()=>{}}var f="Avatar",[h,p]=(0,o.A)(f),[m,v]=h(f),y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,u.jsx)(m,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,u.jsx)(s.span,{...o,ref:t})})});y.displayName=f;var g="AvatarImage",w=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:l=()=>{},...f}=e,h=v(g,n),p=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,c.useSyncExternalStore)(d,()=>!0,()=>!1),l=r.useRef(null),u=i?(l.current||(l.current=new window.Image),l.current):null,[s,f]=r.useState(()=>_(u,e));return(0,a.N)(()=>{f(_(u,e))},[u,e]),(0,a.N)(()=>{let e=e=>()=>{f(e)};if(!u)return;let t=e("loaded"),r=e("error");return u.addEventListener("load",t),u.addEventListener("error",r),n&&(u.referrerPolicy=n),"string"==typeof o&&(u.crossOrigin=o),()=>{u.removeEventListener("load",t),u.removeEventListener("error",r)}},[u,o,n]),s}(o,f),m=(0,i.c)(e=>{l(e),h.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,u.jsx)(s.img,{...f,ref:t,src:o}):null});w.displayName=g;var b="AvatarFallback",x=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=v(b,n),[l,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),l&&"loaded"!==a.imageLoadingStatus?(0,u.jsx)(s.span,{...i,ref:t}):null});function _(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=b;var k=y,A=w,E=x},66102:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(35131);function o(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),r.A.hasOwnProperty(t)?{space:r.A[t],local:e}:e}},69293:(e,t,n)=>{"use strict";function r(){return[]}function o(e){return null==e?r:function(){return this.querySelectorAll(e)}}n.d(t,{A:()=>o})},69474:(e,t,n)=>{"use strict";let r;n.d(t,{_s:()=>L});var o=n(59154),i=n(12115);let a=i.createContext({drawerRef:{current:null},overlayRef:{current:null},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,isDragging:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,handleOnly:!1,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},closeDrawer:()=>{},direction:"bottom",shouldAnimate:{current:!0},shouldScaleBackground:!1,setBackgroundColorOnScale:!0,noBodyStyles:!1,container:null,autoFocus:!1}),l=()=>{let e=i.useContext(a);if(!e)throw Error("useDrawerContext must be used within a Drawer.Root");return e};function u(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function s(){return c(/^iPhone/)||c(/^iPad/)||c(/^Mac/)&&navigator.maxTouchPoints>1}function c(e){return"undefined"!=typeof window&&null!=window.navigator?e.test(window.navigator.platform):void 0}!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}("[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\n[data-state=closed]\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}");let d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect;function f(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];for(let e of t)"function"==typeof e&&e(...n)}}let h="undefined"!=typeof document&&window.visualViewport;function p(e){let t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function m(e){for(p(e)&&(e=e.parentElement);e&&!p(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}let v=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),y=0;function g(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function w(e){let t=document.scrollingElement||document.documentElement;for(;e&&e!==t;){let t=m(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let n=t.getBoundingClientRect().top,r=e.getBoundingClientRect().top;e.getBoundingClientRect().bottom>t.getBoundingClientRect().bottom+24&&(t.scrollTop+=r-n)}e=t.parentElement}}function b(e){return e instanceof HTMLInputElement&&!v.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}function x(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.useCallback(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>t.forEach(t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)})}(...t),t)}let _=new WeakMap;function k(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!(e instanceof HTMLElement))return;let r={};Object.entries(t).forEach(t=>{let[n,o]=t;if(n.startsWith("--")){e.style.setProperty(n,o);return}r[n]=e.style[n],e.style[n]=o}),n||_.set(e,r)}let A=e=>{switch(e){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return e}};function E(e,t){if(!e)return null;let n=window.getComputedStyle(e),r=n.transform||n.webkitTransform||n.mozTransform,o=r.match(/^matrix3d\((.+)\)$/);return o?parseFloat(o[1].split(", ")[A(t)?13:12]):(o=r.match(/^matrix\((.+)\)$/))?parseFloat(o[1].split(", ")[A(t)?5:4]):null}function M(e,t){if(!e)return()=>{};let n=e.style.cssText;return Object.assign(e.style,t),()=>{e.style.cssText=n}}let C={DURATION:.5,EASE:[.32,.72,0,1]},T="vaul-dragging";function R(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current.call(t,...n)},[])}function S(e){let{prop:t,defaultProp:n,onChange:r=()=>{}}=e,[o,a]=function(e){let{defaultProp:t,onChange:n}=e,r=i.useState(t),[o]=r,a=i.useRef(o),l=R(n);return i.useEffect(()=>{a.current!==o&&(l(o),a.current=o)},[o,a,l]),r}({defaultProp:n,onChange:r}),l=void 0!==t,u=l?t:o,s=R(r);return[u,i.useCallback(e=>{if(l){let n="function"==typeof e?e(t):e;n!==t&&s(n)}else a(e)},[l,t,a,s])]}let j=()=>()=>{},N=null;function z(e){var t,n;let{open:l,onOpenChange:c,children:p,onDrag:v,onRelease:x,snapPoints:M,shouldScaleBackground:R=!1,setBackgroundColorOnScale:j=!0,closeThreshold:z=.25,scrollLockTimeout:D=100,dismissible:P=!0,handleOnly:O=!1,fadeFromIndex:L=M&&M.length-1,activeSnapPoint:I,setActiveSnapPoint:H,fixed:q,modal:$=!0,onClose:B,nested:F,noBodyStyles:V=!1,direction:U="bottom",defaultOpen:X=!1,disablePreventScroll:Y=!0,snapToSequentialPoint:W=!1,preventScrollRestoration:G=!1,repositionInputs:K=!0,onAnimationEnd:Z,container:J,autoFocus:Q=!1}=e,[ee=!1,et]=S({defaultProp:X,prop:l,onChange:e=>{null==c||c(e),e||F||eN(),setTimeout(()=>{null==Z||Z(e)},1e3*C.DURATION),e&&!$&&"undefined"!=typeof window&&window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"}),e||(document.body.style.pointerEvents="auto")}}),[en,er]=i.useState(!1),[eo,ei]=i.useState(!1),[ea,el]=i.useState(!1),eu=i.useRef(null),es=i.useRef(null),ec=i.useRef(null),ed=i.useRef(null),ef=i.useRef(null),eh=i.useRef(!1),ep=i.useRef(null),em=i.useRef(0),ev=i.useRef(!1),ey=i.useRef(!X),eg=i.useRef(0),ew=i.useRef(null),eb=i.useRef((null==(t=ew.current)?void 0:t.getBoundingClientRect().height)||0),ex=i.useRef((null==(n=ew.current)?void 0:n.getBoundingClientRect().width)||0),e_=i.useRef(0),ek=i.useCallback(e=>{M&&e===eT.length-1&&(es.current=new Date)},[]),{activeSnapPoint:eA,activeSnapPointIndex:eE,setActiveSnapPoint:eM,onRelease:eC,snapPointsOffset:eT,onDrag:eR,shouldFade:eS,getPercentageDragged:ej}=function(e){let{activeSnapPointProp:t,setActiveSnapPointProp:n,snapPoints:r,drawerRef:o,overlayRef:a,fadeFromIndex:l,onSnapPointChange:u,direction:s="bottom",container:c,snapToSequentialPoint:d}=e,[f,h]=S({prop:t,defaultProp:null==r?void 0:r[0],onChange:n}),[p,m]=i.useState("undefined"!=typeof window?{innerWidth:window.innerWidth,innerHeight:window.innerHeight}:void 0);i.useEffect(()=>{function e(){m({innerWidth:window.innerWidth,innerHeight:window.innerHeight})}return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let v=i.useMemo(()=>f===(null==r?void 0:r[r.length-1])||null,[r,f]),y=i.useMemo(()=>{var e;return null!=(e=null==r?void 0:r.findIndex(e=>e===f))?e:null},[r,f]),g=r&&r.length>0&&(l||0===l)&&!Number.isNaN(l)&&r[l]===f||!r,w=i.useMemo(()=>{var e;let t=c?{width:c.getBoundingClientRect().width,height:c.getBoundingClientRect().height}:"undefined"!=typeof window?{width:window.innerWidth,height:window.innerHeight}:{width:0,height:0};return null!=(e=null==r?void 0:r.map(e=>{let n="string"==typeof e,r=0;if(n&&(r=parseInt(e,10)),A(s)){let o=n?r:p?e*t.height:0;return p?"bottom"===s?t.height-o:-t.height+o:o}let o=n?r:p?e*t.width:0;return p?"right"===s?t.width-o:-t.width+o:o}))?e:[]},[r,p,c]),b=i.useMemo(()=>null!==y?null==w?void 0:w[y]:null,[w,y]),x=i.useCallback(e=>{var t;let n=null!=(t=null==w?void 0:w.findIndex(t=>t===e))?t:null;u(n),k(o.current,{transition:"transform ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")"),transform:A(s)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")}),w&&n!==w.length-1&&void 0!==l&&n!==l&&n<l?k(a.current,{transition:"opacity ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")"),opacity:"0"}):k(a.current,{transition:"opacity ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")"),opacity:"1"}),h(null==r?void 0:r[Math.max(n,0)])},[o.current,r,w,l,a,h]);return i.useEffect(()=>{if(f||t){var e;let n=null!=(e=null==r?void 0:r.findIndex(e=>e===t||e===f))?e:-1;w&&-1!==n&&"number"==typeof w[n]&&x(w[n])}},[f,t,r,w,x]),{isLastSnapPoint:v,activeSnapPoint:f,shouldFade:g,getPercentageDragged:function(e,t){if(!r||"number"!=typeof y||!w||void 0===l)return null;let n=y===l-1;if(y>=l&&t)return 0;if(n&&!t)return 1;if(!g&&!n)return null;let o=n?y+1:y-1,i=e/Math.abs(n?w[o]-w[o-1]:w[o+1]-w[o]);return n?1-i:i},setActiveSnapPoint:h,activeSnapPointIndex:y,onRelease:function(e){let{draggedDistance:t,closeDrawer:n,velocity:o,dismissible:i}=e;if(void 0===l)return;let u="bottom"===s||"right"===s?(null!=b?b:0)-t:(null!=b?b:0)+t,c=y===l-1,f=0===y,h=t>0;if(c&&k(a.current,{transition:"opacity ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")")}),!d&&o>2&&!h){i?n():x(w[0]);return}if(!d&&o>2&&h&&w&&r){x(w[r.length-1]);return}let p=null==w?void 0:w.reduce((e,t)=>"number"!=typeof e||"number"!=typeof t?e:Math.abs(t-u)<Math.abs(e-u)?t:e),m=A(s)?window.innerHeight:window.innerWidth;if(o>.4&&Math.abs(t)<.4*m){let e=h?1:-1;if(e>0&&v&&r){x(w[r.length-1]);return}if(f&&e<0&&i&&n(),null===y)return;x(w[y+e]);return}x(p)},onDrag:function(e){let{draggedDistance:t}=e;if(null===b)return;let n="bottom"===s||"right"===s?b-t:b+t;("bottom"!==s&&"right"!==s||!(n<w[w.length-1]))&&(("top"===s||"left"===s)&&n>w[w.length-1]||k(o.current,{transform:A(s)?"translate3d(0, ".concat(n,"px, 0)"):"translate3d(".concat(n,"px, 0, 0)")}))},snapPointsOffset:w}}({snapPoints:M,activeSnapPointProp:I,setActiveSnapPointProp:H,drawerRef:ew,fadeFromIndex:L,overlayRef:eu,onSnapPointChange:ek,direction:U,container:J,snapToSequentialPoint:W});!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isDisabled:t}=e;d(()=>{if(!t){var e,n,o;let t,i,a,l,u,c,d;return 1==++y&&s()&&(a=0,l=window.pageXOffset,u=window.pageYOffset,c=f((e=document.documentElement,n="paddingRight",o="".concat(window.innerWidth-document.documentElement.clientWidth,"px"),t=e.style[n],e.style[n]=o,()=>{e.style[n]=t})),window.scrollTo(0,0),d=f(g(document,"touchstart",e=>{((i=m(e.target))!==document.documentElement||i!==document.body)&&(a=e.changedTouches[0].pageY)},{passive:!1,capture:!0}),g(document,"touchmove",e=>{if(!i||i===document.documentElement||i===document.body){e.preventDefault();return}let t=e.changedTouches[0].pageY,n=i.scrollTop,r=i.scrollHeight-i.clientHeight;0!==r&&((n<=0&&t>a||n>=r&&t<a)&&e.preventDefault(),a=t)},{passive:!1,capture:!0}),g(document,"touchend",e=>{let t=e.target;b(t)&&t!==document.activeElement&&(e.preventDefault(),t.style.transform="translateY(-2000px)",t.focus(),requestAnimationFrame(()=>{t.style.transform=""}))},{passive:!1,capture:!0}),g(document,"focus",e=>{let t=e.target;b(t)&&(t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",h&&(h.height<window.innerHeight?requestAnimationFrame(()=>{w(t)}):h.addEventListener("resize",()=>w(t),{once:!0}))}))},!0),g(window,"scroll",()=>{window.scrollTo(0,0)})),r=()=>{c(),d(),window.scrollTo(l,u)}),()=>{0==--y&&(null==r||r())}}},[t])}({isDisabled:!ee||eo||!$||ea||!en||!K||!Y});let{restorePositionSetting:eN}=function(e){let{isOpen:t,modal:n,nested:r,hasBeenOpened:o,preventScrollRestoration:a,noBodyStyles:l}=e,[s,c]=i.useState(()=>"undefined"!=typeof window?window.location.href:""),d=i.useRef(0),f=i.useCallback(()=>{if(u()&&null===N&&t&&!l){N={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height,right:"unset"};let{scrollX:e,innerHeight:t}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:"".concat(-d.current,"px"),left:"".concat(-e,"px"),right:"0px",height:"auto"}),window.setTimeout(()=>window.requestAnimationFrame(()=>{let e=t-window.innerHeight;e&&d.current>=t&&(document.body.style.top="".concat(-(d.current+e),"px"))}),300)}},[t]),h=i.useCallback(()=>{if(u()&&null!==N&&!l){let e=-parseInt(document.body.style.top,10),t=-parseInt(document.body.style.left,10);Object.assign(document.body.style,N),window.requestAnimationFrame(()=>{if(a&&s!==window.location.href){c(window.location.href);return}window.scrollTo(t,e)}),N=null}},[s]);return i.useEffect(()=>{function e(){d.current=window.scrollY}return e(),window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),i.useEffect(()=>{if(n)return()=>{"undefined"!=typeof document&&(document.querySelector("[data-vaul-drawer]")||h())}},[n,h]),i.useEffect(()=>{r||!o||(t?(window.matchMedia("(display-mode: standalone)").matches||f(),n||window.setTimeout(()=>{h()},500)):h())},[t,o,s,n,r,f,h]),{restorePositionSetting:h}}({isOpen:ee,modal:$,nested:null!=F&&F,hasBeenOpened:en,preventScrollRestoration:G,noBodyStyles:V});function ez(){return(window.innerWidth-26)/window.innerWidth}function eD(e,t){var n;let r=e,o=null==(n=window.getSelection())?void 0:n.toString(),i=ew.current?E(ew.current,U):null,a=new Date;if("SELECT"===r.tagName||r.hasAttribute("data-vaul-no-drag")||r.closest("[data-vaul-no-drag]"))return!1;if("right"===U||"left"===U)return!0;if(es.current&&a.getTime()-es.current.getTime()<500)return!1;if(null!==i&&("bottom"===U?i>0:i<0))return!0;if(o&&o.length>0)return!1;if(ef.current&&a.getTime()-ef.current.getTime()<D&&0===i||t)return ef.current=a,!1;for(;r;){if(r.scrollHeight>r.clientHeight){if(0!==r.scrollTop)return ef.current=new Date,!1;if("dialog"===r.getAttribute("role"))break}r=r.parentNode}return!0}function eP(e){eo&&ew.current&&(ew.current.classList.remove(T),eh.current=!1,ei(!1),ed.current=new Date),null==B||B(),e||et(!1),setTimeout(()=>{M&&eM(M[0])},1e3*C.DURATION)}function eO(){if(!ew.current)return;let e=document.querySelector("[data-vaul-drawer-wrapper]"),t=E(ew.current,U);k(ew.current,{transform:"translate3d(0, 0, 0)",transition:"transform ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")")}),k(eu.current,{transition:"opacity ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")"),opacity:"1"}),R&&t&&t>0&&ee&&k(e,{borderRadius:"".concat(8,"px"),overflow:"hidden",...A(U)?{transform:"scale(".concat(ez(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)"),transformOrigin:"top"}:{transform:"scale(".concat(ez(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)"),transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:"".concat(C.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(C.EASE.join(","),")")},!0)}return i.useEffect(()=>{window.requestAnimationFrame(()=>{ey.current=!0})},[]),i.useEffect(()=>{var e;function t(){if(ew.current&&K&&(b(document.activeElement)||ev.current)){var e;let t=(null==(e=window.visualViewport)?void 0:e.height)||0,n=window.innerHeight,r=n-t,o=ew.current.getBoundingClientRect().height||0;e_.current||(e_.current=o);let i=ew.current.getBoundingClientRect().top;if(Math.abs(eg.current-r)>60&&(ev.current=!ev.current),M&&M.length>0&&eT&&eE&&(r+=eT[eE]||0),eg.current=r,o>t||ev.current){let e=ew.current.getBoundingClientRect().height,a=e;e>t&&(a=t-(o>.8*n?i:26)),q?ew.current.style.height="".concat(e-Math.max(r,0),"px"):ew.current.style.height="".concat(Math.max(a,t-i),"px")}else!function(){let e=navigator.userAgent;return"undefined"!=typeof window&&(/Firefox/.test(e)&&/Mobile/.test(e)||/FxiOS/.test(e))}()&&(ew.current.style.height="".concat(e_.current,"px"));M&&M.length>0&&!ev.current?ew.current.style.bottom="0px":ew.current.style.bottom="".concat(Math.max(r,0),"px")}}return null==(e=window.visualViewport)||e.addEventListener("resize",t),()=>{var e;return null==(e=window.visualViewport)?void 0:e.removeEventListener("resize",t)}},[eE,M,eT]),i.useEffect(()=>(ee&&(k(document.documentElement,{scrollBehavior:"auto"}),es.current=new Date),()=>{!function(e,t){if(!e||!(e instanceof HTMLElement))return;let n=_.get(e);n&&(e.style[t]=n[t])}(document.documentElement,"scrollBehavior")}),[ee]),i.useEffect(()=>{$||window.requestAnimationFrame(()=>{document.body.style.pointerEvents="auto"})},[$]),i.createElement(o.bL,{defaultOpen:X,onOpenChange:e=>{(P||e)&&(e?er(!0):eP(!0),et(e))},open:ee},i.createElement(a.Provider,{value:{activeSnapPoint:eA,snapPoints:M,setActiveSnapPoint:eM,drawerRef:ew,overlayRef:eu,onOpenChange:c,onPress:function(e){var t,n;(P||M)&&(!ew.current||ew.current.contains(e.target))&&(eb.current=(null==(t=ew.current)?void 0:t.getBoundingClientRect().height)||0,ex.current=(null==(n=ew.current)?void 0:n.getBoundingClientRect().width)||0,ei(!0),ec.current=new Date,s()&&window.addEventListener("touchend",()=>eh.current=!1,{once:!0}),e.target.setPointerCapture(e.pointerId),em.current=A(U)?e.pageY:e.pageX)},onRelease:function(e){var t,n;if(!eo||!ew.current)return;ew.current.classList.remove(T),eh.current=!1,ei(!1),ed.current=new Date;let r=E(ew.current,U);if(!e||!eD(e.target,!1)||!r||Number.isNaN(r)||null===ec.current)return;let o=ed.current.getTime()-ec.current.getTime(),i=em.current-(A(U)?e.pageY:e.pageX),a=Math.abs(i)/o;if(a>.05&&(el(!0),setTimeout(()=>{el(!1)},200)),M){eC({draggedDistance:i*("bottom"===U||"right"===U?1:-1),closeDrawer:eP,velocity:a,dismissible:P}),null==x||x(e,!0);return}if("bottom"===U||"right"===U?i>0:i<0){eO(),null==x||x(e,!0);return}if(a>.4){eP(),null==x||x(e,!1);return}let l=Math.min(null!=(t=ew.current.getBoundingClientRect().height)?t:0,window.innerHeight),u=Math.min(null!=(n=ew.current.getBoundingClientRect().width)?n:0,window.innerWidth);if(Math.abs(r)>=("left"===U||"right"===U?u:l)*z){eP(),null==x||x(e,!1);return}null==x||x(e,!0),eO()},onDrag:function(e){if(ew.current&&eo){let t="bottom"===U||"right"===U?1:-1,n=(em.current-(A(U)?e.pageY:e.pageX))*t,r=n>0,o=M&&!P&&!r;if(o&&0===eE)return;let i=Math.abs(n),a=document.querySelector("[data-vaul-drawer-wrapper]"),l=i/("bottom"===U||"top"===U?eb.current:ex.current),u=ej(i,r);if(null!==u&&(l=u),o&&l>=1||!eh.current&&!eD(e.target,r))return;if(ew.current.classList.add(T),eh.current=!0,k(ew.current,{transition:"none"}),k(eu.current,{transition:"none"}),M&&eR({draggedDistance:n}),r&&!M){let e=Math.min(-(8*(Math.log(n+1)-2)*1),0)*t;k(ew.current,{transform:A(U)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")});return}let s=1-l;if((eS||L&&eE===L-1)&&(null==v||v(e,l),k(eu.current,{opacity:"".concat(s),transition:"none"},!0)),a&&eu.current&&R){let e=Math.min(ez()+l*(1-ez()),1),t=8-8*l,n=Math.max(0,14-14*l);k(a,{borderRadius:"".concat(t,"px"),transform:A(U)?"scale(".concat(e,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(e,") translate3d(").concat(n,"px, 0, 0)"),transition:"none"},!0)}if(!M){let e=i*t;k(ew.current,{transform:A(U)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})}}},dismissible:P,shouldAnimate:ey,handleOnly:O,isOpen:ee,isDragging:eo,shouldFade:eS,closeDrawer:eP,onNestedDrag:function(e,t){if(t<0)return;let n=(window.innerWidth-16)/window.innerWidth,r=n+t*(1-n),o=-16+16*t;k(ew.current,{transform:A(U)?"scale(".concat(r,") translate3d(0, ").concat(o,"px, 0)"):"scale(".concat(r,") translate3d(").concat(o,"px, 0, 0)"),transition:"none"})},onNestedOpenChange:function(e){let t=e?(window.innerWidth-16)/window.innerWidth:1,n=e?-16:0;ep.current&&window.clearTimeout(ep.current),k(ew.current,{transition:"transform ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")"),transform:A(U)?"scale(".concat(t,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(t,") translate3d(").concat(n,"px, 0, 0)")}),!e&&ew.current&&(ep.current=setTimeout(()=>{let e=E(ew.current,U);k(ew.current,{transition:"none",transform:A(U)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})},500))},onNestedRelease:function(e,t){let n=A(U)?window.innerHeight:window.innerWidth,r=t?(n-16)/n:1,o=t?-16:0;t&&k(ew.current,{transition:"transform ".concat(C.DURATION,"s cubic-bezier(").concat(C.EASE.join(","),")"),transform:A(U)?"scale(".concat(r,") translate3d(0, ").concat(o,"px, 0)"):"scale(".concat(r,") translate3d(").concat(o,"px, 0, 0)")})},keyboardIsOpen:ev,modal:$,snapPointsOffset:eT,activeSnapPointIndex:eE,direction:U,shouldScaleBackground:R,setBackgroundColorOnScale:j,noBodyStyles:V,container:J,autoFocus:Q}},p))}let D=i.forwardRef(function(e,t){let{...n}=e,{overlayRef:r,snapPoints:a,onRelease:u,shouldFade:s,isOpen:c,modal:d,shouldAnimate:f}=l(),h=x(t,r),p=a&&a.length>0;if(!d)return null;let m=i.useCallback(e=>u(e),[u]);return i.createElement(o.hJ,{onMouseUp:m,ref:h,"data-vaul-overlay":"","data-vaul-snap-points":c&&p?"true":"false","data-vaul-snap-points-overlay":c&&s?"true":"false","data-vaul-animate":(null==f?void 0:f.current)?"true":"false",...n})});D.displayName="Drawer.Overlay";let P=i.forwardRef(function(e,t){let{onPointerDownOutside:n,style:r,onOpenAutoFocus:a,...u}=e,{drawerRef:s,onPress:c,onRelease:d,onDrag:f,keyboardIsOpen:h,snapPointsOffset:p,activeSnapPointIndex:m,modal:v,isOpen:y,direction:g,snapPoints:w,container:b,handleOnly:_,shouldAnimate:k,autoFocus:E}=l(),[T,R]=i.useState(!1),S=x(t,s),N=i.useRef(null),z=i.useRef(null),D=i.useRef(!1),P=w&&w.length>0,{direction:O,isOpen:L,shouldScaleBackground:I,setBackgroundColorOnScale:H,noBodyStyles:q}=l(),$=i.useRef(null),B=(0,i.useMemo)(()=>document.body.style.backgroundColor,[]);function F(){return(window.innerWidth-26)/window.innerWidth}i.useEffect(()=>{if(L&&I){$.current&&clearTimeout($.current);let e=document.querySelector("[data-vaul-drawer-wrapper]")||document.querySelector("[vaul-drawer-wrapper]");if(!e)return;!function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]}(H&&!q?M(document.body,{background:"black"}):j,M(e,{transformOrigin:A(O)?"top":"left",transitionProperty:"transform, border-radius",transitionDuration:"".concat(C.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(C.EASE.join(","),")")}));let t=M(e,{borderRadius:"".concat(8,"px"),overflow:"hidden",...A(O)?{transform:"scale(".concat(F(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)")}:{transform:"scale(".concat(F(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)")}});return()=>{t(),$.current=window.setTimeout(()=>{B?document.body.style.background=B:document.body.style.removeProperty("background")},1e3*C.DURATION)}}},[L,I,B]);let V=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(D.current)return!0;let r=Math.abs(e.y),o=Math.abs(e.x),i=o>r,a=["bottom","right"].includes(t)?1:-1;if("left"===t||"right"===t){if(!(e.x*a<0)&&o>=0&&o<=n)return i}else if(!(e.y*a<0)&&r>=0&&r<=n)return!i;return D.current=!0,!0};function U(e){N.current=null,D.current=!1,d(e)}return i.useEffect(()=>{P&&window.requestAnimationFrame(()=>{R(!0)})},[]),i.createElement(o.UC,{"data-vaul-drawer-direction":g,"data-vaul-drawer":"","data-vaul-delayed-snap-points":T?"true":"false","data-vaul-snap-points":y&&P?"true":"false","data-vaul-custom-container":b?"true":"false","data-vaul-animate":(null==k?void 0:k.current)?"true":"false",...u,ref:S,style:p&&p.length>0?{"--snap-point-height":"".concat(p[null!=m?m:0],"px"),...r}:r,onPointerDown:e=>{_||(null==u.onPointerDown||u.onPointerDown.call(u,e),N.current={x:e.pageX,y:e.pageY},c(e))},onOpenAutoFocus:e=>{null==a||a(e),E||e.preventDefault()},onPointerDownOutside:e=>{if(null==n||n(e),!v||e.defaultPrevented){e.preventDefault();return}h.current&&(h.current=!1)},onFocusOutside:e=>{if(!v){e.preventDefault();return}},onPointerMove:e=>{if(z.current=e,_||(null==u.onPointerMove||u.onPointerMove.call(u,e),!N.current))return;let t=e.pageY-N.current.y,n=e.pageX-N.current.x,r="touch"===e.pointerType?10:2;V({x:n,y:t},g,r)?f(e):(Math.abs(n)>r||Math.abs(t)>r)&&(N.current=null)},onPointerUp:e=>{null==u.onPointerUp||u.onPointerUp.call(u,e),N.current=null,D.current=!1,d(e)},onPointerOut:e=>{null==u.onPointerOut||u.onPointerOut.call(u,e),U(z.current)},onContextMenu:e=>{null==u.onContextMenu||u.onContextMenu.call(u,e),z.current&&U(z.current)}})});P.displayName="Drawer.Content";let O=i.forwardRef(function(e,t){let{preventCycle:n=!1,children:r,...o}=e,{closeDrawer:a,isDragging:u,snapPoints:s,activeSnapPoint:c,setActiveSnapPoint:d,dismissible:f,handleOnly:h,isOpen:p,onPress:m,onDrag:v}=l(),y=i.useRef(null),g=i.useRef(!1);function w(){y.current&&window.clearTimeout(y.current),g.current=!1}return i.createElement("div",{onClick:function(){if(g.current){w();return}window.setTimeout(()=>{!function(){if(u||n||g.current){w();return}if(w(),!s||0===s.length){f||a();return}if(c===s[s.length-1]&&f){a();return}let e=s.findIndex(e=>e===c);-1!==e&&d(s[e+1])}()},120)},onPointerCancel:w,onPointerDown:e=>{h&&m(e),y.current=window.setTimeout(()=>{g.current=!0},250)},onPointerMove:e=>{h&&v(e)},ref:t,"data-vaul-drawer-visible":p?"true":"false","data-vaul-handle":"","aria-hidden":"true",...o},i.createElement("span",{"data-vaul-handle-hitarea":"","aria-hidden":"true"},r))});O.displayName="Drawer.Handle";let L={Root:z,NestedRoot:function(e){let{onDrag:t,onOpenChange:n,open:r,...o}=e,{onNestedDrag:a,onNestedOpenChange:u,onNestedRelease:s}=l();if(!a)throw Error("Drawer.NestedRoot must be placed in another drawer");return i.createElement(z,{nested:!0,open:r,onClose:()=>{u(!1)},onDrag:(e,n)=>{a(e,n),null==t||t(e,n)},onOpenChange:e=>{e&&u(e),null==n||n(e)},onRelease:s,...o})},Content:P,Overlay:D,Trigger:o.l9,Portal:function(e){let t=l(),{container:n=t.container,...r}=e;return i.createElement(o.ZL,{container:n,...r})},Handle:O,Close:o.bm,Title:o.hE,Description:o.VY}},69803:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},70306:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("log-in",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},71007:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},73314:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},74126:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},74498:(e,t,n)=>{"use strict";n.d(t,{LN:()=>I,Ay:()=>q,zr:()=>L});var r=n(60394),o=n(69293),i=n(83875),a=Array.prototype.find;function l(){return this.firstElementChild}var u=Array.prototype.filter;function s(){return Array.from(this.children)}function c(e){return Array(e.length)}function d(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function f(e,t,n,r,o,i){for(var a,l=0,u=t.length,s=i.length;l<s;++l)(a=t[l])?(a.__data__=i[l],r[l]=a):n[l]=new d(e,i[l]);for(;l<u;++l)(a=t[l])&&(o[l]=a)}function h(e,t,n,r,o,i,a){var l,u,s,c=new Map,f=t.length,h=i.length,p=Array(f);for(l=0;l<f;++l)(u=t[l])&&(p[l]=s=a.call(u,u.__data__,l,t)+"",c.has(s)?o[l]=u:c.set(s,u));for(l=0;l<h;++l)s=a.call(e,i[l],l,i)+"",(u=c.get(s))?(r[l]=u,u.__data__=i[l],c.delete(s)):n[l]=new d(e,i[l]);for(l=0;l<f;++l)(u=t[l])&&c.get(p[l])===u&&(o[l]=u)}function p(e){return e.__data__}function m(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}d.prototype={constructor:d,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};var v=n(66102),y=n(9393);function g(e){return e.trim().split(/^|\s+/)}function w(e){return e.classList||new b(e)}function b(e){this._node=e,this._names=g(e.getAttribute("class")||"")}function x(e,t){for(var n=w(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function _(e,t){for(var n=w(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function k(){this.textContent=""}function A(){this.innerHTML=""}function E(){this.nextSibling&&this.parentNode.appendChild(this)}function M(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}b.prototype={add:function(e){0>this._names.indexOf(e)&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var C=n(35131);function T(e){var t=(0,v.A)(e);return(t.local?function(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}:function(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===C.g&&t.documentElement.namespaceURI===C.g?t.createElement(e):t.createElementNS(n,e)}})(t)}function R(){return null}function S(){var e=this.parentNode;e&&e.removeChild(this)}function j(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function N(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function z(e){return function(){var t=this.__on;if(t){for(var n,r=0,o=-1,i=t.length;r<i;++r)(n=t[r],e.type&&n.type!==e.type||n.name!==e.name)?t[++o]=n:this.removeEventListener(n.type,n.listener,n.options);++o?t.length=o:delete this.__on}}}function D(e,t,n){return function(){var r,o=this.__on,i=function(e){t.call(this,e,this.__data__)};if(o){for(var a=0,l=o.length;a<l;++a)if((r=o[a]).type===e.type&&r.name===e.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=n),r.value=t;return}}this.addEventListener(e.type,i,n),r={type:e.type,name:e.name,value:t,listener:i,options:n},o?o.push(r):this.__on=[r]}}var P=n(27271);function O(e,t,n){var r=(0,P.A)(e),o=r.CustomEvent;"function"==typeof o?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}var L=[null];function I(e,t){this._groups=e,this._parents=t}function H(){return new I([[document.documentElement]],L)}I.prototype=H.prototype={constructor:I,select:function(e){"function"!=typeof e&&(e=(0,r.A)(e));for(var t=this._groups,n=t.length,o=Array(n),i=0;i<n;++i)for(var a,l,u=t[i],s=u.length,c=o[i]=Array(s),d=0;d<s;++d)(a=u[d])&&(l=e.call(a,a.__data__,d,u))&&("__data__"in a&&(l.__data__=a.__data__),c[d]=l);return new I(o,this._parents)},selectAll:function(e){if("function"==typeof e){var t;t=e,e=function(){var e;return e=t.apply(this,arguments),null==e?[]:Array.isArray(e)?e:Array.from(e)}}else e=(0,o.A)(e);for(var n=this._groups,r=n.length,i=[],a=[],l=0;l<r;++l)for(var u,s=n[l],c=s.length,d=0;d<c;++d)(u=s[d])&&(i.push(e.call(u,u.__data__,d,s)),a.push(u));return new I(i,a)},selectChild:function(e){var t;return this.select(null==e?l:(t="function"==typeof e?e:(0,i.j)(e),function(){return a.call(this.children,t)}))},selectChildren:function(e){var t;return this.selectAll(null==e?s:(t="function"==typeof e?e:(0,i.j)(e),function(){return u.call(this.children,t)}))},filter:function(e){"function"!=typeof e&&(e=(0,i.A)(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var a,l=t[o],u=l.length,s=r[o]=[],c=0;c<u;++c)(a=l[c])&&e.call(a,a.__data__,c,l)&&s.push(a);return new I(r,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,p);var n=t?h:f,r=this._parents,o=this._groups;"function"!=typeof e&&(x=e,e=function(){return x});for(var i=o.length,a=Array(i),l=Array(i),u=Array(i),s=0;s<i;++s){var c=r[s],d=o[s],m=d.length,v="object"==typeof(b=e.call(c,c&&c.__data__,s,r))&&"length"in b?b:Array.from(b),y=v.length,g=l[s]=Array(y),w=a[s]=Array(y);n(c,d,g,w,u[s]=Array(m),v,t);for(var b,x,_,k,A=0,E=0;A<y;++A)if(_=g[A]){for(A>=E&&(E=A+1);!(k=w[E])&&++E<y;);_._next=k||null}}return(a=new I(a,r))._enter=l,a._exit=u,a},enter:function(){return new I(this._enter||this._groups.map(c),this._parents)},exit:function(){return new I(this._exit||this._groups.map(c),this._parents)},join:function(e,t,n){var r=this.enter(),o=this,i=this.exit();return"function"==typeof e?(r=e(r))&&(r=r.selection()):r=r.append(e+""),null!=t&&(o=t(o))&&(o=o.selection()),null==n?i.remove():n(i),r&&o?r.merge(o).order():o},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),l=Array(o),u=0;u<a;++u)for(var s,c=n[u],d=r[u],f=c.length,h=l[u]=Array(f),p=0;p<f;++p)(s=c[p]||d[p])&&(h[p]=s);for(;u<o;++u)l[u]=n[u];return new I(l,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r,o=e[t],i=o.length-1,a=o[i];--i>=0;)(r=o[i])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=m);for(var n=this._groups,r=n.length,o=Array(r),i=0;i<r;++i){for(var a,l=n[i],u=l.length,s=o[i]=Array(u),c=0;c<u;++c)(a=l[c])&&(s[c]=a);s.sort(t)}return new I(o,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null},size:function(){let e=0;for(let t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o,i=t[n],a=0,l=i.length;a<l;++a)(o=i[a])&&e.call(o,o.__data__,a,i);return this},attr:function(e,t){var n=(0,v.A)(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==t?n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}}:"function"==typeof t?n.local?function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}:function(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}:n.local?function(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}:function(e,t){return function(){this.setAttribute(e,t)}})(n,t))},style:y.A,property:function(e,t){return arguments.length>1?this.each((null==t?function(e){return function(){delete this[e]}}:"function"==typeof t?function(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}:function(e,t){return function(){this[e]=t}})(e,t)):this.node()[e]},classed:function(e,t){var n=g(e+"");if(arguments.length<2){for(var r=w(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each(("function"==typeof t?function(e,t){return function(){(t.apply(this,arguments)?x:_)(this,e)}}:t?function(e){return function(){x(this,e)}}:function(e){return function(){_(this,e)}})(n,t))},text:function(e){return arguments.length?this.each(null==e?k:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}:function(e){return function(){this.textContent=e}})(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?A:("function"==typeof e?function(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}:function(e){return function(){this.innerHTML=e}})(e)):this.node().innerHTML},raise:function(){return this.each(E)},lower:function(){return this.each(M)},append:function(e){var t="function"==typeof e?e:T(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})},insert:function(e,t){var n="function"==typeof e?e:T(e),o=null==t?R:"function"==typeof t?t:(0,r.A)(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)})},remove:function(){return this.each(S)},clone:function(e){return this.select(e?N:j)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var r,o,i=(e+"").trim().split(/^|\s+/).map(function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}),a=i.length;if(arguments.length<2){var l=this.node().__on;if(l){for(var u,s=0,c=l.length;s<c;++s)for(r=0,u=l[s];r<a;++r)if((o=i[r]).type===u.type&&o.name===u.name)return u.value}return}for(r=0,l=t?D:z;r<a;++r)this.each(l(i[r],t,n));return this},dispatch:function(e,t){return this.each(("function"==typeof t?function(e,t){return function(){return O(this,e,t.apply(this,arguments))}}:function(e,t){return function(){return O(this,e,t)}})(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r,o=e[t],i=0,a=o.length;i<a;++i)(r=o[i])&&(yield r)}};let q=H},75694:(e,t,n)=>{"use strict";n.d(t,{A:()=>function e(t){if("string"==typeof t||"number"==typeof t)return""+t;let n="";if(Array.isArray(t))for(let r=0,o;r<t.length;r++)""!==(o=e(t[r]))&&(n+=(n&&" ")+o);else for(let e in t)t[e]&&(n+=(n&&" ")+e);return n}})},76356:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("arrow-right-left",[["path",{d:"m16 3 4 4-4 4",key:"1x1c3m"}],["path",{d:"M20 7H4",key:"zbl0bi"}],["path",{d:"m8 21-4-4 4-4",key:"h9nckh"}],["path",{d:"M4 17h16",key:"g4d7ey"}]])},79295:(e,t,n)=>{"use strict";n.d(t,{H:()=>m});var r=n(12115),o=n(75694);function i(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var a=n(74211);function l(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},r.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function u(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},r.createElement("path",{d:"M0 0h32v4.2H0z"}))}function s(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},r.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function c(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function d(){return r.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},r.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}let f=({children:e,className:t,...n})=>r.createElement("button",{type:"button",className:(0,o.A)(["react-flow__controls-button",t]),...n},e);f.displayName="ControlButton";let h=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),p=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:p=!0,fitViewOptions:m,onZoomIn:v,onZoomOut:y,onFitView:g,onInteractiveChange:w,className:b,children:x,position:_="bottom-left"})=>{let k=(0,a.PI)(),[A,E]=(0,r.useState)(!1),{isInteractive:M,minZoomReached:C,maxZoomReached:T}=(0,a.Pj)(h,i),{zoomIn:R,zoomOut:S,fitView:j}=(0,a.VH)();return((0,r.useEffect)(()=>{E(!0)},[]),A)?r.createElement(a.Zk,{className:(0,o.A)(["react-flow__controls",b]),position:_,style:e,"data-testid":"rf__controls"},t&&r.createElement(r.Fragment,null,r.createElement(f,{onClick:()=>{R(),v?.()},className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:T},r.createElement(l,null)),r.createElement(f,{onClick:()=>{S(),y?.()},className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:C},r.createElement(u,null))),n&&r.createElement(f,{className:"react-flow__controls-fitview",onClick:()=>{j(m),g?.()},title:"fit view","aria-label":"fit view"},r.createElement(s,null)),p&&r.createElement(f,{className:"react-flow__controls-interactive",onClick:()=>{k.setState({nodesDraggable:!M,nodesConnectable:!M,elementsSelectable:!M}),w?.(!M)},title:"toggle interactivity","aria-label":"toggle interactivity"},M?r.createElement(d,null):r.createElement(c,null)),x):null};p.displayName="Controls";var m=(0,r.memo)(p)},81284:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},82903:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(74498);function o(e){return"string"==typeof e?new r.LN([[document.querySelector(e)]],[document.documentElement]):new r.LN([[e]],r.zr)}},83875:(e,t,n)=>{"use strict";function r(e){return function(){return this.matches(e)}}function o(e){return function(t){return t.matches(e)}}n.d(t,{A:()=>r,j:()=>o})},85690:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},87489:(e,t,n)=>{"use strict";n.d(t,{b:()=>s});var r=n(12115),o=n(63655),i=n(95155),a="horizontal",l=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=a,...s}=e,c=(n=u,l.includes(n))?u:a;return(0,i.jsx)(o.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...s,ref:t})});u.displayName="Separator";var s=u},88653:(e,t,n)=>{"use strict";function r(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,r]of e)if(!Object.is(r,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}n.d(t,{x:()=>r})},89613:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>V,UC:()=>W,ZL:()=>Y,bL:()=>U,i3:()=>G,l9:()=>X});var r=n(12115),o=n(85185),i=n(6101),a=n(46081),l=n(58434),u=n(61285),s=n(63753),c=n(34378),d=n(28905),f=n(63655),h=n(99708),p=n(5845),m=n(2564),v=n(95155),[y,g]=(0,a.A)("Tooltip",[s.Bk]),w=(0,s.Bk)(),b="TooltipProvider",x="tooltip.open",[_,k]=y(b),A=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,l=r.useRef(!0),u=r.useRef(!1),s=r.useRef(0);return r.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(_,{scope:t,isOpenDelayedRef:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(s.current),l.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:a})};A.displayName=b;var E="Tooltip",[M,C]=y(E),T=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i=!1,onOpenChange:a,disableHoverableContent:l,delayDuration:c}=e,d=k(E,e.__scopeTooltip),f=w(t),[h,m]=r.useState(null),y=(0,u.B)(),g=r.useRef(0),b=null!=l?l:d.disableHoverableContent,_=null!=c?c:d.delayDuration,A=r.useRef(!1),[C=!1,T]=(0,p.i)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(x))):d.onClose(),null==a||a(e)}}),R=r.useMemo(()=>C?A.current?"delayed-open":"instant-open":"closed",[C]),S=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,A.current=!1,T(!0)},[T]),j=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,T(!1)},[T]),N=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{A.current=!0,T(!0),g.current=0},_)},[_,T]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,v.jsx)(s.bL,{...f,children:(0,v.jsx)(M,{scope:t,contentId:y,open:C,stateAttribute:R,trigger:h,onTriggerChange:m,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?N():S()},[d.isOpenDelayedRef,N,S]),onTriggerLeave:r.useCallback(()=>{b?j():(window.clearTimeout(g.current),g.current=0)},[j,b]),onOpen:S,onClose:j,disableHoverableContent:b,children:n})})};T.displayName=E;var R="TooltipTrigger",S=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=C(R,n),u=k(R,n),c=w(n),d=r.useRef(null),h=(0,i.s)(t,d,l.onTriggerChange),p=r.useRef(!1),m=r.useRef(!1),y=r.useCallback(()=>p.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,v.jsx)(s.Mz,{asChild:!0,...c,children:(0,v.jsx)(f.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||m.current||u.isPointerInTransitRef.current||(l.onTriggerEnter(),m.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),m.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),p.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{p.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});S.displayName=R;var j="TooltipPortal",[N,z]=y(j,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=C(j,t);return(0,v.jsx)(N,{scope:t,forceMount:n,children:(0,v.jsx)(d.C,{present:n||i.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};D.displayName=j;var P="TooltipContent",O=r.forwardRef((e,t)=>{let n=z(P,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,a=C(P,e.__scopeTooltip);return(0,v.jsx)(d.C,{present:r||a.open,children:a.disableHoverableContent?(0,v.jsx)($,{side:o,...i,ref:t}):(0,v.jsx)(L,{side:o,...i,ref:t})})}),L=r.forwardRef((e,t)=>{let n=C(P,e.__scopeTooltip),o=k(P,e.__scopeTooltip),a=r.useRef(null),l=(0,i.s)(t,a),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,f=a.current,{onPointerInTransitChange:h}=o,p=r.useCallback(()=>{s(null),h(!1)},[h]),m=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),h(!0)},[h]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(c&&f){let e=e=>m(e,f),t=e=>m(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,m,p]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,u=t[i].x,s=t[i].y;l>r!=s>r&&n<(u-a)*(r-l)/(s-l)+a&&(o=!o)}return o}(n,u);r?p():o&&(p(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,d,p]),(0,v.jsx)($,{...e,ref:l})}),[I,H]=y(E,{isInside:!1}),q=(0,h.Dc)("TooltipContent"),$=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:u,...c}=e,d=C(P,n),f=w(n),{onClose:h}=d;return r.useEffect(()=>(document.addEventListener(x,h),()=>document.removeEventListener(x,h)),[h]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,v.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,v.jsxs)(s.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(q,{children:o}),(0,v.jsx)(I,{scope:n,isInside:!0,children:(0,v.jsx)(m.b,{id:d.contentId,role:"tooltip",children:i||o})})]})})});O.displayName=P;var B="TooltipArrow",F=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=w(n);return H(B,n).isInside?null:(0,v.jsx)(s.i3,{...o,...r,ref:t})});F.displayName=B;var V=A,U=T,X=S,Y=D,W=O,G=F},90232:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},91788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},92657:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},93219:(e,t,n)=>{"use strict";n.d(t,{s_:()=>eQ,GS:()=>eV});var r,o=n(61235),i=n(29204);function a(e){return((e=Math.exp(e))+1/e)/2}let l=function e(t,n,r){function o(e,o){var i,l,u=e[0],s=e[1],c=e[2],d=o[0],f=o[1],h=o[2],p=d-u,m=f-s,v=p*p+m*m;if(v<1e-12)l=Math.log(h/c)/t,i=function(e){return[u+e*p,s+e*m,c*Math.exp(t*e*l)]};else{var y=Math.sqrt(v),g=(h*h-c*c+r*v)/(2*c*n*y),w=(h*h-c*c-r*v)/(2*h*n*y),b=Math.log(Math.sqrt(g*g+1)-g);l=(Math.log(Math.sqrt(w*w+1)-w)-b)/t,i=function(e){var r,o,i=e*l,d=a(b),f=c/(n*y)*(d*(((r=Math.exp(2*(r=t*i+b)))-1)/(r+1))-((o=Math.exp(o=b))-1/o)/2);return[u+f*p,s+f*m,c*d/a(t*i+b)]}}return i.duration=1e3*l*t/Math.SQRT2,i}return o.rho=function(t){var n=Math.max(.001,+t),r=n*n;return e(n,r,r*r)},o}(Math.SQRT2,2,4);var u,s,c=n(82903),d=n(14897),f=n(74498),h=0,p=0,m=0,v=0,y=0,g=0,w="object"==typeof performance&&performance.now?performance:Date,b="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function x(){return y||(b(_),y=w.now()+g)}function _(){y=0}function k(){this._call=this._time=this._next=null}function A(e,t,n){var r=new k;return r.restart(e,t,n),r}function E(){y=(v=w.now())+g,h=p=0;try{x(),++h;for(var e,t=u;t;)(e=y-t._time)>=0&&t._call.call(void 0,e),t=t._next;--h}finally{h=0,function(){for(var e,t,n=u,r=1/0;n;)n._call?(r>n._time&&(r=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:u=t);s=e,C(r)}(),y=0}}function M(){var e=w.now(),t=e-v;t>1e3&&(g-=t,v=e)}function C(e){!h&&(p&&(p=clearTimeout(p)),e-y>24?(e<1/0&&(p=setTimeout(E,e-w.now()-g)),m&&(m=clearInterval(m))):(m||(v=w.now(),m=setInterval(M,1e3)),h=1,b(E)))}function T(e,t,n){var r=new k;return t=null==t?0:+t,r.restart(n=>{r.stop(),e(n+t)},t,n),r}k.prototype=A.prototype={constructor:k,restart:function(e,t,n){if("function"!=typeof e)throw TypeError("callback is not a function");n=(null==n?x():+n)+(null==t?0:+t),this._next||s===this||(s?s._next=this:u=this,s=this),this._call=e,this._time=n,C()},stop:function(){this._call&&(this._call=null,this._time=1/0,C())}};var R=(0,o.A)("start","end","cancel","interrupt"),S=[];function j(e,t,n,r,o,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var r,o=e.__transition;function i(u){var s,c,d,f;if(1!==n.state)return l();for(s in o)if((f=o[s]).name===n.name){if(3===f.state)return T(i);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",e,e.__data__,f.index,f.group),delete o[s]):+s<t&&(f.state=6,f.timer.stop(),f.on.call("cancel",e,e.__data__,f.index,f.group),delete o[s])}if(T(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(u))}),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(s=0,n.state=3,r=Array(d=n.tween.length),c=-1;s<d;++s)(f=n.tween[s].value.call(e,e.__data__,n.index,n.group))&&(r[++c]=f);r.length=c+1}}function a(t){for(var o=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=r.length;++i<a;)r[i].call(e,o);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var r in n.state=6,n.timer.stop(),delete o[t],o)return;delete e.__transition}o[t]=n,n.timer=A(function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)},0,n.time)}(e,n,{name:t,index:r,group:o,on:R,tween:S,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function N(e,t){var n=D(e,t);if(n.state>0)throw Error("too late; already scheduled");return n}function z(e,t){var n=D(e,t);if(n.state>3)throw Error("too late; already running");return n}function D(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw Error("transition not found");return n}function P(e,t){var n,r,o,i=e.__transition,a=!0;if(i){for(o in t=null==t?null:t+"",i){if((n=i[o]).name!==t){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[o]}a&&delete e.__transition}}function O(e,t){return e*=1,t*=1,function(n){return e*(1-n)+t*n}}var L=180/Math.PI,I={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function H(e,t,n,r,o,i){var a,l,u;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(u=e*n+t*r)&&(n-=e*u,r-=t*u),(l=Math.sqrt(n*n+r*r))&&(n/=l,r/=l,u/=l),e*r<t*n&&(e=-e,t=-t,u=-u,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*L,skewX:Math.atan(u)*L,scaleX:a,scaleY:l}}function q(e,t,n,r){function o(e){return e.length?e.pop()+" ":""}return function(i,a){var l,u,s,c,d=[],f=[];return i=e(i),a=e(a),!function(e,r,o,i,a,l){if(e!==o||r!==i){var u=a.push("translate(",null,t,null,n);l.push({i:u-4,x:O(e,o)},{i:u-2,x:O(r,i)})}else(o||i)&&a.push("translate("+o+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,d,f),l=i.rotate,l!==(u=a.rotate)?(l-u>180?u+=360:u-l>180&&(l+=360),f.push({i:d.push(o(d)+"rotate(",null,r)-2,x:O(l,u)})):u&&d.push(o(d)+"rotate("+u+r),s=i.skewX,s!==(c=a.skewX)?f.push({i:d.push(o(d)+"skewX(",null,r)-2,x:O(s,c)}):c&&d.push(o(d)+"skewX("+c+r),!function(e,t,n,r,i,a){if(e!==n||t!==r){var l=i.push(o(i)+"scale(",null,",",null,")");a.push({i:l-4,x:O(e,n)},{i:l-2,x:O(t,r)})}else(1!==n||1!==r)&&i.push(o(i)+"scale("+n+","+r+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,d,f),i=a=null,function(e){for(var t,n=-1,r=f.length;++n<r;)d[(t=f[n]).i]=t.x(e);return d.join("")}}}var $=q(function(e){let t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?I:H(t.a,t.b,t.c,t.d,t.e,t.f)},"px, ","px)","deg)"),B=q(function(e){return null==e?I:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",e),e=r.transform.baseVal.consolidate())?H((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):I},", ",")",")"),F=n(66102);function V(e,t,n){var r=e._id;return e.each(function(){var e=z(this,r);(e.value||(e.value={}))[t]=n.apply(this,arguments)}),function(e){return D(e,r).value[t]}}function U(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function X(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function Y(){}var W="\\s*([+-]?\\d+)\\s*",G="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",K="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Z=/^#([0-9a-f]{3,8})$/,J=RegExp(`^rgb\\(${W},${W},${W}\\)$`),Q=RegExp(`^rgb\\(${K},${K},${K}\\)$`),ee=RegExp(`^rgba\\(${W},${W},${W},${G}\\)$`),et=RegExp(`^rgba\\(${K},${K},${K},${G}\\)$`),en=RegExp(`^hsl\\(${G},${K},${K}\\)$`),er=RegExp(`^hsla\\(${G},${K},${K},${G}\\)$`),eo={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function ei(){return this.rgb().formatHex()}function ea(){return this.rgb().formatRgb()}function el(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Z.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?eu(t):3===n?new ed(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?es(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?es(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=J.exec(e))?new ed(t[1],t[2],t[3],1):(t=Q.exec(e))?new ed(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=ee.exec(e))?es(t[1],t[2],t[3],t[4]):(t=et.exec(e))?es(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=en.exec(e))?ey(t[1],t[2]/100,t[3]/100,1):(t=er.exec(e))?ey(t[1],t[2]/100,t[3]/100,t[4]):eo.hasOwnProperty(e)?eu(eo[e]):"transparent"===e?new ed(NaN,NaN,NaN,0):null}function eu(e){return new ed(e>>16&255,e>>8&255,255&e,1)}function es(e,t,n,r){return r<=0&&(e=t=n=NaN),new ed(e,t,n,r)}function ec(e,t,n,r){var o;return 1==arguments.length?((o=e)instanceof Y||(o=el(o)),o)?new ed((o=o.rgb()).r,o.g,o.b,o.opacity):new ed:new ed(e,t,n,null==r?1:r)}function ed(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function ef(){return`#${ev(this.r)}${ev(this.g)}${ev(this.b)}`}function eh(){let e=ep(this.opacity);return`${1===e?"rgb(":"rgba("}${em(this.r)}, ${em(this.g)}, ${em(this.b)}${1===e?")":`, ${e})`}`}function ep(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function em(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function ev(e){return((e=em(e))<16?"0":"")+e.toString(16)}function ey(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new ew(e,t,n,r)}function eg(e){if(e instanceof ew)return new ew(e.h,e.s,e.l,e.opacity);if(e instanceof Y||(e=el(e)),!e)return new ew;if(e instanceof ew)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=NaN,l=i-o,u=(i+o)/2;return l?(a=t===i?(n-r)/l+(n<r)*6:n===i?(r-t)/l+2:(t-n)/l+4,l/=u<.5?i+o:2-i-o,a*=60):l=u>0&&u<1?0:a,new ew(a,l,u,e.opacity)}function ew(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function eb(e){return(e=(e||0)%360)<0?e+360:e}function ex(e){return Math.max(0,Math.min(1,e||0))}function e_(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}function ek(e,t,n,r,o){var i=e*e,a=i*e;return((1-3*e+3*i-a)*t+(4-6*i+3*a)*n+(1+3*e+3*i-3*a)*r+a*o)/6}U(Y,el,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:ei,formatHex:ei,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return eg(this).formatHsl()},formatRgb:ea,toString:ea}),U(ed,ec,X(Y,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ed(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ed(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new ed(em(this.r),em(this.g),em(this.b),ep(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ef,formatHex:ef,formatHex8:function(){return`#${ev(this.r)}${ev(this.g)}${ev(this.b)}${ev((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:eh,toString:eh})),U(ew,function(e,t,n,r){return 1==arguments.length?eg(e):new ew(e,t,n,null==r?1:r)},X(Y,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ew(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ew(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new ed(e_(e>=240?e-240:e+120,o,r),e_(e,o,r),e_(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new ew(eb(this.h),ex(this.s),ex(this.l),ep(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=ep(this.opacity);return`${1===e?"hsl(":"hsla("}${eb(this.h)}, ${100*ex(this.s)}%, ${100*ex(this.l)}%${1===e?")":`, ${e})`}`}}));let eA=e=>()=>e;function eE(e,t){var n,r,o=t-e;return o?(n=e,r=o,function(e){return n+e*r}):eA(isNaN(e)?t:e)}let eM=function e(t){var n,r=1==(n=+t)?eE:function(e,t){var r,o,i;return t-e?(r=e,o=t,r=Math.pow(r,i=n),o=Math.pow(o,i)-r,i=1/i,function(e){return Math.pow(r+e*o,i)}):eA(isNaN(e)?t:e)};function o(e,t){var n=r((e=ec(e)).r,(t=ec(t)).r),o=r(e.g,t.g),i=r(e.b,t.b),a=eE(e.opacity,t.opacity);return function(t){return e.r=n(t),e.g=o(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function eC(e){return function(t){var n,r,o=t.length,i=Array(o),a=Array(o),l=Array(o);for(n=0;n<o;++n)r=ec(t[n]),i[n]=r.r||0,a[n]=r.g||0,l[n]=r.b||0;return i=e(i),a=e(a),l=e(l),r.opacity=1,function(e){return r.r=i(e),r.g=a(e),r.b=l(e),r+""}}}eC(function(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),o=e[r],i=e[r+1],a=r>0?e[r-1]:2*o-i,l=r<t-1?e[r+2]:2*i-o;return ek((n-r/t)*t,a,o,i,l)}}),eC(function(e){var t=e.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*t),o=e[(r+t-1)%t],i=e[r%t],a=e[(r+1)%t],l=e[(r+2)%t];return ek((n-r/t)*t,o,i,a,l)}});var eT=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,eR=RegExp(eT.source,"g");function eS(e,t){var n;return("number"==typeof t?O:t instanceof el?eM:(n=el(t))?(t=n,eM):function(e,t){var n,r,o,i,a,l=eT.lastIndex=eR.lastIndex=0,u=-1,s=[],c=[];for(e+="",t+="";(o=eT.exec(e))&&(i=eR.exec(t));)(a=i.index)>l&&(a=t.slice(l,a),s[u]?s[u]+=a:s[++u]=a),(o=o[0])===(i=i[0])?s[u]?s[u]+=i:s[++u]=i:(s[++u]=null,c.push({i:u,x:O(o,i)})),l=eR.lastIndex;return l<t.length&&(a=t.slice(l),s[u]?s[u]+=a:s[++u]=a),s.length<2?c[0]?(n=c[0].x,function(e){return n(e)+""}):(r=t,function(){return r}):(t=c.length,function(e){for(var n,r=0;r<t;++r)s[(n=c[r]).i]=n.x(e);return s.join("")})})(e,t)}var ej=n(83875),eN=n(60394),ez=n(69293),eD=f.Ay.prototype.constructor,eP=n(9393);function eO(e){return function(){this.style.removeProperty(e)}}var eL=0;function eI(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}var eH=f.Ay.prototype;eI.prototype=(function(e){return(0,f.Ay)().transition(e)}).prototype={constructor:eI,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=(0,eN.A)(e));for(var r=this._groups,o=r.length,i=Array(o),a=0;a<o;++a)for(var l,u,s=r[a],c=s.length,d=i[a]=Array(c),f=0;f<c;++f)(l=s[f])&&(u=e.call(l,l.__data__,f,s))&&("__data__"in l&&(u.__data__=l.__data__),d[f]=u,j(d[f],t,n,f,d,D(l,n)));return new eI(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=(0,ez.A)(e));for(var r=this._groups,o=r.length,i=[],a=[],l=0;l<o;++l)for(var u,s=r[l],c=s.length,d=0;d<c;++d)if(u=s[d]){for(var f,h=e.call(u,u.__data__,d,s),p=D(u,n),m=0,v=h.length;m<v;++m)(f=h[m])&&j(f,t,n,m,h,p);i.push(h),a.push(u)}return new eI(i,a,t,n)},selectChild:eH.selectChild,selectChildren:eH.selectChildren,filter:function(e){"function"!=typeof e&&(e=(0,ej.A)(e));for(var t=this._groups,n=t.length,r=Array(n),o=0;o<n;++o)for(var i,a=t[o],l=a.length,u=r[o]=[],s=0;s<l;++s)(i=a[s])&&e.call(i,i.__data__,s,a)&&u.push(i);return new eI(r,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw Error();for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=Array(r),l=0;l<i;++l)for(var u,s=t[l],c=n[l],d=s.length,f=a[l]=Array(d),h=0;h<d;++h)(u=s[h]||c[h])&&(f[h]=u);for(;l<r;++l)a[l]=t[l];return new eI(a,this._parents,this._name,this._id)},selection:function(){return new eD(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=++eL,r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],u=l.length,s=0;s<u;++s)if(a=l[s]){var c=D(a,t);j(a,e,n,s,l,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new eI(r,this._parents,e,n)},call:eH.call,nodes:eH.nodes,node:eH.node,size:eH.size,empty:eH.empty,each:eH.each,on:function(e,t){var n,r,o,i,a,l,u=this._id;return arguments.length<2?D(this.node(),u).on.on(e):this.each((n=u,r=e,o=t,l=(r+"").trim().split(/^|\s+/).every(function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e})?N:z,function(){var e=l(this,n),t=e.on;t!==i&&(a=(i=t).copy()).on(r,o),e.on=a}))},attr:function(e,t){var n=(0,F.A)(e),r="transform"===n?B:eS;return this.attrTween(e,"function"==typeof t?(n.local?function(e,t,n){var r,o,i;return function(){var a,l,u=n(this);return null==u?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local))===(l=u+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,u))}}:function(e,t,n){var r,o,i;return function(){var a,l,u=n(this);return null==u?void this.removeAttribute(e):(a=this.getAttribute(e))===(l=u+"")?null:a===r&&l===o?i:(o=l,i=t(r=a,u))}})(n,r,V(this,"attr."+e,t)):null==t?(n.local?function(e){return function(){this.removeAttributeNS(e.space,e.local)}}:function(e){return function(){this.removeAttribute(e)}})(n):(n.local?function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===r?o:o=t(r=a,n)}}:function(e,t,n){var r,o,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===r?o:o=t(r=a,n)}})(n,r,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();var r=(0,F.A)(e);return this.tween(n,(r.local?function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttributeNS(e.space,e.local,o.call(this,t))}),n}return o._value=t,o}:function(e,t){var n,r;function o(){var o=t.apply(this,arguments);return o!==r&&(n=(r=o)&&function(t){this.setAttribute(e,o.call(this,t))}),n}return o._value=t,o})(r,t))},style:function(e,t,n){var r,o,i,a,l,u,s,c,d,f,h,p,m,v,y,g,w,b,x,_,k,A="transform"==(e+="")?$:eS;return null==t?this.styleTween(e,(r=e,function(){var e=(0,eP.j)(this,r),t=(this.style.removeProperty(r),(0,eP.j)(this,r));return e===t?null:e===o&&t===i?a:a=A(o=e,i=t)})).on("end.style."+e,eO(e)):"function"==typeof t?this.styleTween(e,(l=e,u=V(this,"style."+e,t),function(){var e=(0,eP.j)(this,l),t=u(this),n=t+"";return null==t&&(this.style.removeProperty(l),n=t=(0,eP.j)(this,l)),e===n?null:e===s&&n===c?d:(c=n,d=A(s=e,t))})).each((f=this._id,w="end."+(g="style."+(h=e)),function(){var e=z(this,f),t=e.on,n=null==e.value[g]?y||(y=eO(h)):void 0;(t!==p||v!==n)&&(m=(p=t).copy()).on(w,v=n),e.on=m})):this.styleTween(e,(b=e,k=t+"",function(){var e=(0,eP.j)(this,b);return e===k?null:e===x?_:_=A(x=e,t)}),n).on("end.style."+e,null)},styleTween:function(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==t)return this.tween(r,null);if("function"!=typeof t)throw Error();return this.tween(r,function(e,t,n){var r,o;function i(){var i=t.apply(this,arguments);return i!==o&&(r=(o=i)&&function(t){this.style.setProperty(e,i.call(this,t),n)}),r}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){var t,n;return this.tween("text","function"==typeof e?(t=V(this,"text",e),function(){var e=t(this);this.textContent=null==e?"":e}):(n=null==e?"":e+"",function(){this.textContent=n}))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw Error();return this.tween(t,function(e){var t,n;function r(){var r=e.apply(this,arguments);return r!==n&&(t=(n=r)&&function(e){this.textContent=r.call(this,e)}),t}return r._value=e,r}(e))},remove:function(){var e;return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r,o=D(this.node(),n).tween,i=0,a=o.length;i<a;++i)if((r=o[i]).name===e)return r.value;return null}return this.each((null==t?function(e,t){var n,r;return function(){var o=z(this,e),i=o.tween;if(i!==n){r=n=i;for(var a=0,l=r.length;a<l;++a)if(r[a].name===t){(r=r.slice()).splice(a,1);break}}o.tween=r}}:function(e,t,n){var r,o;if("function"!=typeof n)throw Error();return function(){var i=z(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var l={name:t,value:n},u=0,s=o.length;u<s;++u)if(o[u].name===t){o[u]=l;break}u===s&&o.push(l)}i.tween=o}})(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){N(this,e).delay=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){N(this,e).delay=t}})(t,e)):D(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?function(e,t){return function(){z(this,e).duration=+t.apply(this,arguments)}}:function(e,t){return t*=1,function(){z(this,e).duration=t}})(t,e)):D(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw Error();return function(){z(this,e).ease=t}}(t,e)):D(this.node(),t).ease},easeVarying:function(e){var t;if("function"!=typeof e)throw Error();return this.each((t=this._id,function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw Error();z(this,t).ease=n}))},end:function(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,a){var l={value:a},u={value:function(){0==--o&&i()}};n.each(function(){var n=z(this,r),o=n.on;o!==e&&((t=(e=o).copy())._.cancel.push(l),t._.interrupt.push(l),t._.end.push(u)),n.on=t}),0===o&&i()})},[Symbol.iterator]:eH[Symbol.iterator]};var eq={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};f.Ay.prototype.interrupt=function(e){return this.each(function(){P(this,e)})},f.Ay.prototype.transition=function(e){var t,n;e instanceof eI?(t=e._id,e=e._name):(t=++eL,(n=eq).time=x(),e=null==e?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a,l=r[i],u=l.length,s=0;s<u;++s)(a=l[s])&&j(a,e,t,s,l,n||function(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw Error(`transition ${t} not found`);return n}(a,t));return new eI(r,this._parents,e,t)};let e$=e=>()=>e;function eB(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function eF(e,t,n){this.k=e,this.x=t,this.y=n}eF.prototype={constructor:eF,scale:function(e){return 1===e?this:new eF(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new eF(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var eV=new eF(1,0,0);function eU(e){e.stopImmediatePropagation()}function eX(e){e.preventDefault(),e.stopImmediatePropagation()}function eY(e){return(!e.ctrlKey||"wheel"===e.type)&&!e.button}function eW(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function eG(){return this.__zoom||eV}function eK(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function eZ(){return navigator.maxTouchPoints||"ontouchstart"in this}function eJ(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function eQ(){var e,t,n,r=eY,a=eW,u=eJ,s=eK,f=eZ,h=[0,1/0],p=[[-1/0,-1/0],[1/0,1/0]],m=250,v=l,y=(0,o.A)("start","zoom","end"),g=0,w=10;function b(e){e.property("__zoom",eG).on("wheel.zoom",C,{passive:!1}).on("mousedown.zoom",T).on("dblclick.zoom",R).filter(f).on("touchstart.zoom",S).on("touchmove.zoom",j).on("touchend.zoom touchcancel.zoom",N).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(e,t){return(t=Math.max(h[0],Math.min(h[1],t)))===e.k?e:new eF(t,e.x,e.y)}function _(e,t,n){var r=t[0]-n[0]*e.k,o=t[1]-n[1]*e.k;return r===e.x&&o===e.y?e:new eF(e.k,r,o)}function k(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function A(e,t,n,r){e.on("start.zoom",function(){E(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){E(this,arguments).event(r).end()}).tween("zoom",function(){var e=arguments,o=E(this,e).event(r),i=a.apply(this,e),l=null==n?k(i):"function"==typeof n?n.apply(this,e):n,u=Math.max(i[1][0]-i[0][0],i[1][1]-i[0][1]),s=this.__zoom,c="function"==typeof t?t.apply(this,e):t,d=v(s.invert(l).concat(u/s.k),c.invert(l).concat(u/c.k));return function(e){if(1===e)e=c;else{var t=d(e),n=u/t[2];e=new eF(n,l[0]-t[0]*n,l[1]-t[1]*n)}o.zoom(null,e)}})}function E(e,t,n){return!n&&e.__zooming||new M(e,t)}function M(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=a.apply(e,t),this.taps=0}function C(e,...t){if(r.apply(this,arguments)){var n=E(this,t).event(e),o=this.__zoom,i=Math.max(h[0],Math.min(h[1],o.k*Math.pow(2,s.apply(this,arguments)))),a=(0,d.A)(e);if(n.wheel)(n.mouse[0][0]!==a[0]||n.mouse[0][1]!==a[1])&&(n.mouse[1]=o.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(o.k===i)return;n.mouse=[a,o.invert(a)],P(this),n.start()}eX(e),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",u(_(x(o,i),n.mouse[0],n.mouse[1]),n.extent,p))}}function T(e,...t){if(!n&&r.apply(this,arguments)){var o=e.currentTarget,a=E(this,t,!0).event(e),l=(0,c.A)(e.view).on("mousemove.zoom",function(e){if(eX(e),!a.moved){var t=e.clientX-f,n=e.clientY-h;a.moved=t*t+n*n>g}a.event(e).zoom("mouse",u(_(a.that.__zoom,a.mouse[0]=(0,d.A)(e,o),a.mouse[1]),a.extent,p))},!0).on("mouseup.zoom",function(e){l.on("mousemove.zoom mouseup.zoom",null),(0,i.y)(e.view,a.moved),eX(e),a.event(e).end()},!0),s=(0,d.A)(e,o),f=e.clientX,h=e.clientY;(0,i.A)(e.view),eU(e),a.mouse=[s,this.__zoom.invert(s)],P(this),a.start()}}function R(e,...t){if(r.apply(this,arguments)){var n=this.__zoom,o=(0,d.A)(e.changedTouches?e.changedTouches[0]:e,this),i=n.invert(o),l=n.k*(e.shiftKey?.5:2),s=u(_(x(n,l),o,i),a.apply(this,t),p);eX(e),m>0?(0,c.A)(this).transition().duration(m).call(A,s,o,e):(0,c.A)(this).call(b.transform,s,o,e)}}function S(n,...o){if(r.apply(this,arguments)){var i,a,l,u,s=n.touches,c=s.length,f=E(this,o,n.changedTouches.length===c).event(n);for(eU(n),a=0;a<c;++a)l=s[a],u=[u=(0,d.A)(l,this),this.__zoom.invert(u),l.identifier],f.touch0?f.touch1||f.touch0[2]===u[2]||(f.touch1=u,f.taps=0):(f.touch0=u,i=!0,f.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(f.taps<2&&(t=u[0],e=setTimeout(function(){e=null},500)),P(this),f.start())}}function j(e,...t){if(this.__zooming){var n,r,o,i,a=E(this,t).event(e),l=e.changedTouches,s=l.length;for(eX(e),n=0;n<s;++n)r=l[n],o=(0,d.A)(r,this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=o:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=o);if(r=a.that.__zoom,a.touch1){var c=a.touch0[0],f=a.touch0[1],h=a.touch1[0],m=a.touch1[1],v=(v=h[0]-c[0])*v+(v=h[1]-c[1])*v,y=(y=m[0]-f[0])*y+(y=m[1]-f[1])*y;r=x(r,Math.sqrt(v/y)),o=[(c[0]+h[0])/2,(c[1]+h[1])/2],i=[(f[0]+m[0])/2,(f[1]+m[1])/2]}else{if(!a.touch0)return;o=a.touch0[0],i=a.touch0[1]}a.zoom("touch",u(_(r,o,i),a.extent,p))}}function N(e,...r){if(this.__zooming){var o,i,a=E(this,r).event(e),l=e.changedTouches,u=l.length;for(eU(e),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),o=0;o<u;++o)i=l[o],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=(0,d.A)(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<w)){var s=(0,c.A)(this).on("dblclick.zoom");s&&s.apply(this,arguments)}}}return b.transform=function(e,t,n,r){var o=e.selection?e.selection():e;o.property("__zoom",eG),e!==o?A(e,t,n,r):o.interrupt().each(function(){E(this,arguments).event(r).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()})},b.scaleBy=function(e,t,n,r){b.scaleTo(e,function(){var e=this.__zoom.k,n="function"==typeof t?t.apply(this,arguments):t;return e*n},n,r)},b.scaleTo=function(e,t,n,r){b.transform(e,function(){var e=a.apply(this,arguments),r=this.__zoom,o=null==n?k(e):"function"==typeof n?n.apply(this,arguments):n,i=r.invert(o),l="function"==typeof t?t.apply(this,arguments):t;return u(_(x(r,l),o,i),e,p)},n,r)},b.translateBy=function(e,t,n,r){b.transform(e,function(){return u(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),a.apply(this,arguments),p)},null,r)},b.translateTo=function(e,t,n,r,o){b.transform(e,function(){var e=a.apply(this,arguments),o=this.__zoom,i=null==r?k(e):"function"==typeof r?r.apply(this,arguments):r;return u(eV.translate(i[0],i[1]).scale(o.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,p)},r,o)},M.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=(0,c.A)(this.that).datum();y.call(e,this.that,new eB(e,{sourceEvent:this.sourceEvent,target:b,type:e,transform:this.that.__zoom,dispatch:y}),t)}},b.wheelDelta=function(e){return arguments.length?(s="function"==typeof e?e:e$(+e),b):s},b.filter=function(e){return arguments.length?(r="function"==typeof e?e:e$(!!e),b):r},b.touchable=function(e){return arguments.length?(f="function"==typeof e?e:e$(!!e),b):f},b.extent=function(e){return arguments.length?(a="function"==typeof e?e:e$([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),b):a},b.scaleExtent=function(e){return arguments.length?(h[0]=+e[0],h[1]=+e[1],b):[h[0],h[1]]},b.translateExtent=function(e){return arguments.length?(p[0][0]=+e[0][0],p[1][0]=+e[1][0],p[0][1]=+e[0][1],p[1][1]=+e[1][1],b):[[p[0][0],p[0][1]],[p[1][0],p[1][1]]]},b.constrain=function(e){return arguments.length?(u=e,b):u},b.duration=function(e){return arguments.length?(m=+e,b):m},b.interpolate=function(e){return arguments.length?(v=e,b):v},b.on=function(){var e=y.on.apply(y,arguments);return e===y?b:e},b.clickDistance=function(e){return arguments.length?(g=(e*=1)*e,b):Math.sqrt(g)},b.tapDistance=function(e){return arguments.length?(w=+e,b):w},b}eF.prototype},93509:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},94788:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}}]);