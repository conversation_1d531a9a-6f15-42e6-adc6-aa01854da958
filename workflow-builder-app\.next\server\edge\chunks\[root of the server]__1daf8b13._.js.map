{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/routes.ts"], "sourcesContent": ["/**\r\n * Shared route definitions for the application\r\n * Matches the implementation in ruh-app-fe for consistency.\r\n */\r\n\r\n// Authentication routes\r\nexport const loginRoute = \"/login\";\r\nexport const signupRoute = \"/signup\";\r\nexport const verifyEmailRoute = \"/verify-email\";\r\nexport const updatePasswordRoute = \"/reset-password\";\r\nexport const authRoute = `${process.env.NEXT_PUBLIC_AUTHENTICATION_URL}?redirect_url=${process.env.NEXT_PUBLIC_APP_URL}`;\r\n\r\n// Dashboard routes\r\nexport const homeRoute = \"/workflows\"; // Updated to point to workflows instead of /home\r\nexport const workflowsRoute = \"/workflows\";\r\nexport const settingsRoute = \"/settings\";\r\nexport const credentialsRoute = \"/credentials\";\r\n\r\n// Public routes\r\nexport const aboutRoute = \"/about\";\r\nexport const contactRoute = \"/contact\";\r\n\r\n// Define public routes that don't require authentication\r\nexport const publicRoutes = [\r\n  loginRoute,\r\n  signupRoute,\r\n  verifyEmailRoute,\r\n  updatePasswordRoute,\r\n  aboutRoute,\r\n  contactRoute,\r\n];\r\n\r\n// Define protected routes that require authentication\r\nexport const protectedRoutes = [workflowsRoute, workflowsRoute, settingsRoute, credentialsRoute];\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,wBAAwB;;;;;;;;;;;;;;;;AACjB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,sBAAsB;AAC5B,MAAM,YAAY,8DAA8C,cAAc,8DAAmC;AAGjH,MAAM,YAAY,cAAc,iDAAiD;AACjF,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,MAAM,mBAAmB;AAGzB,MAAM,aAAa;AACnB,MAAM,eAAe;AAGrB,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,kBAAkB;IAAC;IAAgB;IAAgB;IAAe;CAAiB"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["/**\r\n * Next.js middleware for route protection\r\n * Matches the implementation in ruh-app-fe for consistency.\r\n */\r\n\r\nimport { NextResponse } from \"next/server\";\r\nimport type { NextRequest } from \"next/server\";\r\nimport { workflowsRoute, homeRoute, loginRoute, publicRoutes } from \"@/shared/routes\";\r\n\r\nexport function middleware(request: NextRequest) {\r\n  // TEMPORARY: Uncomment this line to bypass all auth checks for testing\r\n  // return NextResponse.next();\r\n\r\n  const path = request.nextUrl.pathname;\r\n\r\n  // Check if the current path is public\r\n  const isPublicRoute = publicRoutes.some(\r\n    (route) =>\r\n      // For exact matches (like login, signup)\r\n      path === route ||\r\n      // For routes with parameters (like reset-password/[token])\r\n      (route.startsWith(\"/\") && path.startsWith(route)),\r\n  );\r\n\r\n  // Check if the user has an auth token\r\n  const hasAuthTokens = request.cookies.has(\"accessToken\") || request.cookies.has(\"refreshToken\");\r\n\r\n  // If user is logged in and trying to access auth/public pages, redirect to workflows page\r\n  if (isPublicRoute && hasAuthTokens) {\r\n    return NextResponse.redirect(new URL(workflowsRoute, request.url));\r\n  }\r\n  // If user is not logged in and trying to access any non-public route, redirect to login\r\n  if (!isPublicRoute && !hasAuthTokens) {\r\n    // Add the current path as a callback URL for post-login redirection\r\n    const loginUrl = new URL(loginRoute, request.url);\r\n    loginUrl.searchParams.set(\"callbackUrl\", path);\r\n    return NextResponse.redirect(loginUrl);\r\n  }\r\n\r\n  return NextResponse.next();\r\n}\r\n\r\n// Configure middleware to run on all routes except api, _next/static, etc.\r\nexport const config = {\r\n  matcher: [\"/((?!api|_next|_vercel|.*\\\\.[\\\\w]+$).*)\"],\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAAA;AAEA;;;AAEO,SAAS,WAAW,OAAoB;IAC7C,uEAAuE;IACvE,8BAA8B;IAE9B,MAAM,OAAO,QAAQ,OAAO,CAAC,QAAQ;IAErC,sCAAsC;IACtC,MAAM,gBAAgB,+HAAA,CAAA,eAAY,CAAC,IAAI,CACrC,CAAC,QACC,yCAAyC;QACzC,SAAS,SAER,MAAM,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC;IAG9C,sCAAsC;IACtC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEhF,0FAA0F;IAC1F,IAAI,iBAAiB,eAAe;QAClC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,+HAAA,CAAA,iBAAc,EAAE,QAAQ,GAAG;IAClE;IACA,wFAAwF;IACxF,IAAI,CAAC,iBAAiB,CAAC,eAAe;QACpC,oEAAoE;QACpE,MAAM,WAAW,IAAI,IAAI,+HAAA,CAAA,aAAU,EAAE,QAAQ,GAAG;QAChD,SAAS,YAAY,CAAC,GAAG,CAAC,eAAe;QACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QAAC;KAA0C;AACtD"}}]}